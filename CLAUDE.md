# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概览

toolFork 是一个工具聚合网站，通过 WebAssembly 在本地设备处理文件，无需云服务器。技术实现参考 vert.sh 项目。

## 项目结构

这是一个包含 Next.js 应用的单体仓库，主要应用位于 `fork-app/` 目录下。

```
fork-app/
├── src/
│   ├── app/                  # Next.js App Router 页面
│   │   ├── articles/         # 文章页面
│   │   ├── login/            # 登录页面  
│   │   ├── register/         # 注册页面
│   │   ├── search/           # 搜索页面
│   │   ├── mcp/              # MCP 集成页面
│   │   ├── pwa/              # PWA 设置页面
│   │   ├── offline/          # 离线页面
│   │   └── tool/[id]/        # 工具详情页面（动态路由）
│   ├── components/           # React 组件
│   │   ├── cards/            # 卡片组件（CardGrid, ClientCard, ServerCard）
│   │   ├── layout/           # 布局组件（Header, HeroSection, UnifiedLayout）
│   │   ├── navigation/       # 导航组件（TabNavigation）
│   │   ├── pwa/              # PWA 组件（InstallPrompt, PWASettings）
│   │   ├── sections/         # 页面区块（FAQ）
│   │   ├── tools/            # 工具相关组件（ImageCutout, UIGenerator, ToolCard）
│   │   └── ui/               # UI 基础组件（Button, Card, Input）
│   ├── data/                 # 数据文件
│   │   ├── tools.ts          # 工具数据定义
│   │   └── mcp-data.ts       # MCP 数据定义
│   ├── hooks/                # React hooks
│   │   ├── useAuth.ts        # 认证 hook
│   │   └── usePWA.ts         # PWA hook
│   ├── lib/                  # 工具函数
│   │   ├── utils.ts          # 通用工具函数（含 cn 函数）
│   │   └── tool-registry.ts  # 工具组件注册系统
│   └── types/                # TypeScript 类型定义
│       ├── tool.ts           # 工具类型定义（Tool, ToolCategory）
│       ├── mcp.ts            # MCP 类型定义
│       └── next-pwa.d.ts     # PWA 类型定义
└── public/                   # 静态资源
    ├── icons/                # 应用图标
    ├── screenshots/          # 应用截图
    └── sw.js                 # Service Worker

```

## 开发命令

在 `fork-app/` 目录下执行：

```bash
# 安装依赖（使用 pnpm）
pnpm install

# 启动开发服务器（使用 Turbopack）
pnpm dev

# 构建项目
pnpm build

# 使用 Turbopack 构建（实验性）
pnpm build:turbo

# 启动生产服务器
pnpm start

# 代码检查
pnpm lint

# 测试 PWA 功能
pnpm test:pwa

# PWA 构建和启动
pnpm pwa:build

# 创建新工具（交互式向导）
pnpm create-tool
```

## 技术栈

- **框架**: Next.js 15.5.0 (App Router)
- **运行时**: React 19.1.0
- **语言**: TypeScript 5.x
- **样式**: Tailwind CSS v4 + clsx + tailwind-merge
- **图标**: lucide-react
- **字体**: Geist 字体系列
- **包管理器**: pnpm
- **构建工具**: Turbopack（开发模式默认启用）
- **PWA**: next-pwa + Workbox（离线支持、缓存策略）
- **WebAssembly**: 计划集成（参考 vert.sh 实现）

## 架构说明

### 应用架构
- 使用 Next.js App Router 模式
- 源代码位于 `fork-app/src/` 目录
- 主要页面组件在 `fork-app/src/app/` 下
- 路径别名配置: `@/*` 映射到 `./src/*`
- 使用动态路由实现工具详情页 (`tool/[id]`)
- 工具数据定义在 `src/data/tools.ts`
- 组件结构分层：cards、layout、navigation、pwa、sections、tools、ui
- 工具组件通过 `src/lib/tool-registry.ts` 动态加载和注册

### PWA 配置
- 使用 next-pwa 实现渐进式 Web 应用
- Service Worker 自动生成和注册
- 离线页面: `/offline`
- 缓存策略:
  - 字体: CacheFirst (365天)
  - 图片: StaleWhileRevalidate (24小时)
  - JS/CSS: StaleWhileRevalidate (24小时)
  - 网络请求: NetworkFirst (离线缓存)
- manifest.json 配置应用元数据

### WebAssembly 集成计划
- 计划通过 WASM 实现客户端文件处理
- 避免文件上传到服务器，保护用户隐私
- 参考 vert.sh 的实现方案（详见 `docs/vert.md`）
- vert.sh 架构要点:
  - 使用 Web Workers 处理重型任务
  - WASM 模块动态加载（pandoc.wasm、ImageMagick）
  - 视频处理通过 vertd 服务（可选）

### TypeScript 配置
- 严格模式启用 (`strict: true`)
- 目标编译版本: ES2017
- 模块解析: bundler 模式
- JSX 保留模式用于 Next.js 处理

### ESLint 配置
- 扩展 Next.js 核心 Web Vitals 规则
- 支持 TypeScript 检查
- 忽略构建产物和依赖目录

## 重要文件路径

- 工具数据配置: `fork-app/src/data/tools.ts`
- 工具类型定义: `fork-app/src/types/tool.ts`
- 工具注册系统: `fork-app/src/lib/tool-registry.ts`
- 全局样式: `fork-app/src/app/globals.css`
- 根布局: `fork-app/src/app/layout.tsx`
- PWA 配置: `fork-app/next.config.ts`
- 工具页面模板: `fork-app/src/app/tool/[id]/page.tsx`

## 工具开发流程

### 快速创建新工具

使用CLI工具快速创建新工具：

```bash
# 进入fork-app目录
cd fork-app

# 运行工具创建向导
pnpm create-tool
```

向导会引导您：
1. 输入工具ID、名称、描述
2. 选择工具分类和复杂度
3. 自动生成工具组件和配置

### 手动添加工具

1. **添加工具数据** - 在 `src/data/tools.ts` 中添加工具配置
2. **创建工具组件** - 在 `src/components/tools/` 创建组件
3. **注册工具** - 在 `src/lib/tool-registry.ts` 注册组件
4. **实现功能** - 根据复杂度选择实现方式：
   - 简单工具：纯前端JS/TS实现
   - 中等工具：集成WebAssembly模块
   - 复杂工具：调用后端API

### 工具开发规范

- 使用TypeScript和函数组件
- 遵循现有的UI组件规范（Card、Button等）
- 支持响应式设计
- 提供清晰的使用说明
- 处理错误和加载状态

### 工具分类

| 分类枚举 | 标签 | 说明 |
|---------|------|------|
| `ToolCategory.TEXT` | 文本类 | 文本处理和分析工具 |
| `ToolCategory.DOCUMENT` | 文档类 | 文档操作和转换工具 |
| `ToolCategory.IMAGE` | 图像类 | 图片处理和编辑工具 |
| `ToolCategory.AUDIO` | 音频类 | 音频处理和编辑工具 |
| `ToolCategory.VIDEO` | 视频类 | 视频处理和编辑工具 |
| `ToolCategory.DEVELOPER` | 开发类 | 开发者工具 |
| `ToolCategory.UTILITY` | 实用工具 | 日常实用小工具 |
| `ToolCategory.DESIGN` | 设计类 | 设计相关工具 |
| `ToolCategory.CRYPTO` | 加密类 | 加密解密工具 |
| `ToolCategory.CONVERTER` | 转换类 | 格式转换工具 |

### 工具数据结构

```typescript
interface Tool {
  id: string;                    // 工具唯一标识
  name: string;                  // 工具名称
  description: string;           // 工具描述
  category: ToolCategory;        // 工具分类
  tags: string[];               // 标签数组
  icon: string;                 // Emoji 图标
  url: string;                  // 工具路径
  featured?: boolean;           // 是否为特色工具
  usageCount?: number;          // 使用次数
  status?: 'development' | 'beta' | 'stable';  // 开发状态
  complexity?: 'simple' | 'medium' | 'complex'; // 复杂度
  features?: string[];          // 功能特性列表
  requirements?: {              // 技术需求
    webAssembly?: boolean;
    api?: boolean;
    offline?: boolean;
    auth?: boolean;
  };
}
```

详细开发指南请查看 `docs/TOOL_DEVELOPMENT.md`