# ToolFork - 在线工具聚合站

<div align="center">

![ToolFork Logo](https://img.shields.io/badge/ToolFork-在线工具聚合站-blue?style=for-the-badge)

一个基于 WebAssembly 技术的现代化在线工具聚合平台，提供安全、高效的客户端文件处理服务。

[![Next.js](https://img.shields.io/badge/Next.js-15.5.0-black?logo=next.js)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-19.1.0-blue?logo=react)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.x-blue?logo=typescript)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-4.x-38B2AC?logo=tailwind-css)](https://tailwindcss.com/)

</div>

## 📖 项目简介

ToolFork 是一个现代化的在线工具聚合平台，类似于 [tool.lu](https://tool.lu/) 的功能定位，但采用了更先进的技术架构。项目的核心理念是**隐私优先**和**客户端处理**，通过 WebAssembly 技术在用户浏览器中直接处理文件，无需上传到服务器，确保用户数据的安全性和隐私性。

### 🎯 项目愿景

- **隐私保护**：所有文件处理都在客户端完成，数据不离开用户设备
- **高性能**：基于 WebAssembly 技术，提供接近原生应用的处理速度
- **无限制**：不受服务器文件大小限制，支持大文件处理
- **开源免费**：完全开源，社区驱动的发展模式

## ✨ 核心特性

### 🔒 隐私优先
- **客户端处理**：文件转换和处理完全在浏览器中进行
- **零上传**：文件数据不会发送到任何服务器
- **即时处理**：无需等待网络传输，处理速度更快

### 🚀 技术先进
- **WebAssembly 集成**：支持多种 WASM 模块（Pandoc、ImageMagick 等）
- **Web Workers**：后台处理重型任务，不阻塞用户界面
- **现代化框架**：基于 Next.js 15 + React 19 构建

### 🛠️ 工具丰富
- **图像处理**：抠图、放大、格式转换、证件照制作
- **文档转换**：PDF 操作、格式转换、批量处理
- **开发工具**：代码格式化、混淆/反混淆、UI 生成
- **文本处理**：格式化、编码转换、内容分析
- **音频视频**：格式转换、剪辑、调音工具

### 📱 用户友好
- **响应式设计**：完美适配桌面和移动设备
- **直观界面**：简洁明了的用户体验
- **快速搜索**：智能搜索和分类筛选
- **收藏功能**：个性化工具管理

### 🔧 PWA 支持
- **应用安装**：支持安装到桌面和移动设备，获得原生应用体验
- **离线功能**：核心工具离线可用，无网络也能正常使用
- **智能缓存**：自动缓存静态资源，提升加载速度
- **后台更新**：自动检查和更新应用版本
- **推送通知**：支持重要消息推送（可选）

## 🏗️ 技术架构

### 前端技术栈
```
├── Next.js 15.5.0          # React 全栈框架
├── React 19.1.0            # 用户界面库
├── TypeScript 5.x          # 类型安全的 JavaScript
├── Tailwind CSS 4.x        # 原子化 CSS 框架
├── Lucide React            # 现代图标库
└── clsx + tailwind-merge   # 样式工具库
```

### WebAssembly 集成
```
├── Web Workers             # 后台任务处理
├── WASM 模块管理           # 动态加载和执行
├── 文件流处理              # 高效的数据传输
└── 错误处理和恢复          # 健壮的异常处理
```

### PWA 技术栈
```
├── next-pwa                # PWA 插件和配置
├── Service Worker          # 离线缓存和后台同步
├── Web App Manifest        # 应用安装配置
├── Cache API               # 智能缓存管理
├── Push API                # 推送通知支持
└── Background Sync         # 后台数据同步
```

### 项目结构
```
toolFork/
├── docs/                   # 项目文档
│   └── vert.md            # 技术参考文档
├── fork-app/              # 主应用目录
│   ├── src/
│   │   ├── app/           # Next.js App Router
│   │   ├── components/    # React 组件
│   │   │   ├── layout/    # 布局组件
│   │   │   ├── tools/     # 工具相关组件
│   │   │   ├── pwa/       # PWA 相关组件
│   │   │   └── ui/        # 基础 UI 组件
│   │   ├── data/          # 数据定义
│   │   │   └── tools.ts   # 工具数据配置
│   │   ├── hooks/         # React Hooks
│   │   │   └── usePWA.ts  # PWA 状态管理
│   │   ├── lib/           # 工具函数
│   │   └── types/         # TypeScript 类型定义
│   ├── public/            # 静态资源
│   │   ├── icons/         # PWA 应用图标
│   │   ├── manifest.json  # Web App Manifest
│   │   ├── sw.js          # Service Worker
│   │   └── browserconfig.xml # 浏览器配置
│   └── package.json       # 依赖配置
└── README.md              # 项目说明
```

## 🚀 快速开始

### 环境要求
- Node.js 18.0 或更高版本
- pnpm 8.0 或更高版本（推荐）

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/WaltCuller/toolFork.git
cd toolFork
```

2. **安装依赖**
```bash
cd fork-app
pnpm install
```

3. **启动开发服务器**
```bash
pnpm dev
```

4. **访问应用**
打开浏览器访问 [http://localhost:3000](http://localhost:3000)

5. **体验 PWA 功能**
- 访问 [http://localhost:3000/pwa](http://localhost:3000/pwa) 查看 PWA 设置
- 在支持的浏览器中会自动显示"安装应用"提示
- 安装后可从桌面或应用列表直接启动

### 构建生产版本
```bash
# 构建应用（包含 PWA 功能）
pnpm build

# 启动生产服务器
pnpm start
```

> **注意**：PWA 功能需要在生产环境或 HTTPS 下才能完全工作。开发环境下部分功能可能受限。

## 🔧 工具分类

项目支持以下工具分类：

| 分类 | 描述 | 示例工具 |
|------|------|----------|
| 📝 **文本类** | 文本处理和分析工具 | 智能文案、字数统计、格式转换 |
| 📄 **文档类** | 文档操作和转换工具 | PDF 合并、文件重命名、格式转换 |
| 🖼️ **图像类** | 图片处理和编辑工具 | 一键抠图、图片放大、证件照制作 |
| 🎵 **音频类** | 音频处理和编辑工具 | 音频剪辑、格式转换、调音器 |
| 🎬 **视频类** | 视频处理和编辑工具 | 视频转换、剪辑、压缩 |
| 💻 **开发类** | 开发者工具 | 代码格式化、UI 生成、反混淆 |
| 🔧 **实用工具** | 日常实用小工具 | IP 查询、抛硬币、单位转换 |
| 🎨 **设计类** | 设计相关工具 | 颜色选择器、字体预览、图标生成 |
| 🔐 **加密类** | 加密解密工具 | 哈希计算、加密解密、签名验证 |
| 🔄 **转换类** | 格式转换工具 | 进制转换、编码转换、文件转换 |

## 🛠️ 开发指南

### 添加新工具

1. **定义工具数据**

在 `fork-app/src/data/tools.ts` 中添加工具定义：

```typescript
{
  id: 'your-tool-id',
  name: '工具名称',
  description: '工具描述',
  category: ToolCategory.IMAGE, // 选择合适的分类
  tags: ['标签1', '标签2'],
  icon: '🔧', // 选择合适的 emoji 图标
  url: '/tool/your-tool-id',
  featured: false, // 是否为特色工具
  usageCount: 0 // 使用次数（可选）
}
```

2. **创建工具页面**

在 `fork-app/src/app/tool/your-tool-id/` 目录下创建 `page.tsx`：

```typescript
'use client';

import React from 'react';

export default function YourToolPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">工具名称</h1>
      {/* 工具界面实现 */}
    </div>
  );
}
```

3. **集成 WebAssembly（可选）**

如果工具需要 WASM 支持：

```typescript
// 创建 Web Worker
const worker = new Worker('/workers/your-tool-worker.js');

// 处理文件
const processFile = async (file: File) => {
  return new Promise((resolve, reject) => {
    worker.postMessage({ file, action: 'process' });
    worker.onmessage = (e) => {
      if (e.data.success) {
        resolve(e.data.result);
      } else {
        reject(e.data.error);
      }
    };
  });
};
```

4. **添加组件样式**

使用 Tailwind CSS 创建响应式界面：

```typescript
<div className="max-w-4xl mx-auto">
  <div className="bg-white rounded-lg shadow-md p-6">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* 工具内容 */}
    </div>
  </div>
</div>
```

### 开发规范

#### 代码风格
- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint 配置的代码规范
- 使用 Prettier 进行代码格式化
- 组件名使用 PascalCase
- 文件名使用 kebab-case

#### 组件结构
```typescript
'use client'; // 如果需要客户端功能

import React, { useState, useEffect } from 'react';
import { SomeIcon } from 'lucide-react';

interface ComponentProps {
  // 定义 props 类型
}

export default function Component({ }: ComponentProps) {
  // 组件逻辑

  return (
    <div>
      {/* JSX 内容 */}
    </div>
  );
}
```

#### 状态管理
- 使用 React Hooks 进行状态管理
- 复杂状态使用 useReducer
- 全局状态考虑使用 Context API

### PWA 开发指南

#### 添加 PWA 功能
```typescript
// 使用 PWA Hook
import { usePWA } from '@/hooks/usePWA';

function MyComponent() {
  const { isInstalled, installApp, isOnline } = usePWA();

  return (
    <div>
      {!isInstalled && (
        <button onClick={installApp}>安装应用</button>
      )}
      <span>状态: {isOnline ? '在线' : '离线'}</span>
    </div>
  );
}
```

#### PWA 配置
- **Manifest 配置**：编辑 `public/manifest.json`
- **Service Worker**：自定义 `public/sw.js`
- **缓存策略**：在 `next.config.ts` 中配置
- **图标管理**：添加不同尺寸的图标到 `public/icons/`

#### PWA 测试
```bash
# 构建生产版本（PWA 功能需要生产环境）
pnpm build && pnpm start

# 使用 Chrome DevTools 的 Application 面板测试
# - 检查 Service Worker 注册
# - 验证 Manifest 配置
# - 测试离线功能
# - 查看缓存状态
```

### 测试指南

```bash
# 运行 ESLint 检查
pnpm lint

# 类型检查
pnpm type-check

# 构建测试
pnpm build

# PWA 功能测试
pnpm test:pwa
# 或者手动测试
pnpm build && pnpm start
# 然后在 Chrome DevTools > Application 中验证 PWA 功能
```

## 🚀 部署指南

### Vercel 部署（推荐）

1. **连接 GitHub 仓库**
   - 登录 [Vercel](https://vercel.com)
   - 导入 GitHub 仓库
   - 选择 `fork-app` 作为根目录

2. **配置构建设置**
   ```
   Framework Preset: Next.js
   Root Directory: fork-app
   Build Command: pnpm build
   Output Directory: .next
   Install Command: pnpm install
   ```

3. **环境变量配置**
   ```
   NODE_ENV=production
   ```

4. **PWA 部署验证**
   - 部署完成后访问应用
   - 检查浏览器是否显示安装提示
   - 使用 Chrome DevTools > Lighthouse 运行 PWA 审计
   - 验证离线功能是否正常工作

### Docker 部署

1. **创建 Dockerfile**
```dockerfile
FROM node:18-alpine AS base

# 安装 pnpm
RUN npm install -g pnpm

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY fork-app/package.json fork-app/pnpm-lock.yaml ./

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY fork-app/ .

# 构建应用
RUN pnpm build

# 生产阶段
FROM node:18-alpine AS runner
WORKDIR /app

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=base /app/public ./public
COPY --from=base --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=base --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

2. **构建和运行**
```bash
# 构建镜像
docker build -t toolfork .

# 运行容器
docker run -p 3000:3000 toolfork
```

### 静态部署

```bash
# 构建静态文件
pnpm build

# 部署 out 目录到静态托管服务
# 如 Netlify、GitHub Pages 等
```

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 贡献方式

1. **报告问题**
   - 使用 GitHub Issues 报告 bug
   - 提供详细的复现步骤
   - 包含环境信息和错误截图

2. **功能建议**
   - 在 Issues 中提出新功能建议
   - 详细描述功能需求和使用场景
   - 讨论实现方案

3. **代码贡献**
   - Fork 项目到你的 GitHub 账户
   - 创建功能分支：`git checkout -b feature/amazing-feature`
   - 提交更改：`git commit -m 'feat: 添加某个功能'`
   - 推送分支：`git push origin feature/amazing-feature`
   - 创建 Pull Request

### 提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<类型>: <描述>

<详细描述>（可选）
```

**类型说明：**
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `perf`: 性能优化
- `test`: 测试相关
- `chore`: 构建/工具/依赖更新

### 开发流程

1. **设置开发环境**
```bash
git clone https://github.com/your-username/toolFork.git
cd toolFork/fork-app
pnpm install
pnpm dev
```

2. **创建功能分支**
```bash
git checkout -b feature/your-feature-name
```

3. **开发和测试**
```bash
# 开发过程中持续测试
pnpm lint
pnpm type-check
pnpm build
```

4. **提交代码**
```bash
git add .
git commit -m "feat: 添加新功能描述"
git push origin feature/your-feature-name
```

## 📚 技术参考

### 相关项目
- [vert.sh](https://github.com/vert-sh/vert) - WebAssembly 文件转换工具
- [tool.lu](https://tool.lu/) - 在线工具聚合站参考

### 技术文档
- [Next.js 文档](https://nextjs.org/docs)
- [React 文档](https://react.dev/)
- [WebAssembly 文档](https://webassembly.org/)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)

### WASM 资源
- [WebAssembly Studio](https://webassembly.studio/)
- [Emscripten](https://emscripten.org/)
- [wasm-pack](https://rustwasm.github.io/wasm-pack/)

### PWA 资源
- [PWA 官方文档](https://web.dev/progressive-web-apps/)
- [next-pwa 插件](https://github.com/shadowwalker/next-pwa)
- [Web App Manifest](https://developer.mozilla.org/en-US/docs/Web/Manifest)
- [Service Worker API](https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API)
- [Workbox 工具库](https://developers.google.com/web/tools/workbox)
- [PWA Builder](https://www.pwabuilder.com/)

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源许可证。

```
MIT License

Copyright (c) 2025 ToolFork

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## ✨ PWA 功能亮点

ToolFork 作为一个现代化的 PWA 应用，提供了以下核心功能：

### 🚀 安装体验
- **一键安装**：支持 Chrome、Edge、Safari 等主流浏览器
- **原生体验**：安装后获得类似原生应用的使用体验
- **桌面图标**：自动添加到桌面或应用列表

### 📱 离线功能
- **核心工具离线可用**：文本处理、代码格式化、单位转换等
- **智能缓存**：自动缓存常用资源，提升加载速度
- **离线提示**：友好的离线状态提示和功能指引

### 🔄 自动更新
- **后台更新**：自动检查和下载应用更新
- **版本管理**：智能的缓存版本控制
- **更新提示**：及时通知用户可用的新版本

### ⚡ 性能优化
- **快速启动**：预缓存关键资源，实现秒级启动
- **网络优先**：智能的缓存策略，平衡性能和数据新鲜度
- **资源压缩**：优化的资源加载和压缩策略

### 🛠️ 开发友好
- **PWA 管理面板**：完整的 PWA 状态监控和管理界面
- **开发工具集成**：与 Chrome DevTools 完美集成
- **可扩展架构**：模块化的 PWA 功能设计

## 🙏 致谢

- 感谢 [vert.sh](https://github.com/vert-sh/vert) 项目提供的技术参考
- 感谢 [tool.lu](https://tool.lu/) 提供的功能设计灵感
- 感谢 PWA 社区提供的最佳实践指导
- 感谢所有开源项目和贡献者

## 📞 联系我们

- **GitHub Issues**: [提交问题](https://github.com/WaltCuller/toolFork/issues)
- **GitHub Discussions**: [参与讨论](https://github.com/WaltCuller/toolFork/discussions)

---

<div align="center">

**⭐ 如果这个项目对你有帮助，请给我们一个 Star！**

Made with ❤️ by [ToolFork Team](https://github.com/WaltCuller/toolFork)

</div>

