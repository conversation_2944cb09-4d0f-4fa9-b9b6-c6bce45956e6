# 🛠️ ToolFork 工具开发指南

本文档详细介绍如何在 ToolFork 平台上开发和发布新工具。

## 📋 目录

- [快速开始](#快速开始)
- [工具架构](#工具架构)
- [开发流程](#开发流程)
- [工具类型](#工具类型)
- [技术规范](#技术规范)
- [测试指南](#测试指南)
- [发布流程](#发布流程)
- [最佳实践](#最佳实践)

## 🚀 快速开始

### 1. 使用CLI创建新工具

```bash
# 进入项目目录
cd fork-app

# 运行工具创建向导
pnpm create-tool
```

向导会引导您完成以下步骤：
- 输入工具ID（如 `image-resize`）
- 输入工具名称（如 `图片压缩`）
- 输入工具描述
- 选择工具分类
- 选择工具复杂度
- 输入工具图标（emoji）

### 2. 自动生成的文件

CLI工具会自动创建以下文件：
- `src/components/tools/YourTool.tsx` - 工具组件
- 更新 `src/data/tools.ts` - 添加工具数据
- 自动注册到工具注册系统

### 3. 开始开发

```bash
# 启动开发服务器
pnpm dev

# 访问您的工具
http://localhost:3000/tool/your-tool-id
```

## 🏗️ 工具架构

### 三层架构模型

```
┌─────────────────────────────────────┐
│         用户界面层 (UI Layer)         │
│    - React 组件                      │
│    - 用户交互                        │
│    - 状态管理                        │
├─────────────────────────────────────┤
│        处理层 (Processing Layer)     │
│    - 业务逻辑                        │
│    - WebAssembly 模块                │
│    - Web Workers                     │
├─────────────────────────────────────┤
│         数据层 (Data Layer)          │
│    - 文件处理                        │
│    - API 调用                        │
│    - 本地存储                        │
└─────────────────────────────────────┘
```

### 工具组件结构

```typescript
interface ToolComponentProps {
  className?: string;
}

export function YourTool({ className }: ToolComponentProps) {
  // 状态管理
  const [input, setInput] = useState('');
  const [output, setOutput] = useState('');
  
  // 处理逻辑
  const handleProcess = async () => {
    // 实现功能
  };
  
  // 渲染UI
  return (
    <Card>
      {/* 工具界面 */}
    </Card>
  );
}
```

## 📝 开发流程

### 第一阶段：规划设计

1. **需求分析**
   - 明确工具功能
   - 确定目标用户
   - 评估技术可行性

2. **选择工具类型**
   - 简单工具：纯前端实现
   - 中等工具：需要WebAssembly
   - 复杂工具：需要后端API

3. **设计用户界面**
   - 绘制界面草图
   - 设计交互流程
   - 确定数据流向

### 第二阶段：功能实现

1. **基础功能开发**
   ```typescript
   // 1. 定义状态
   const [data, setData] = useState(initialState);
   
   // 2. 实现处理逻辑
   const processData = async (input) => {
     // 处理逻辑
     return result;
   };
   
   // 3. 处理用户交互
   const handleSubmit = async () => {
     const result = await processData(input);
     setOutput(result);
   };
   ```

2. **错误处理**
   ```typescript
   try {
     const result = await processData(input);
     setOutput(result);
   } catch (error) {
     setError('处理失败: ' + error.message);
   }
   ```

3. **加载状态**
   ```typescript
   const [isLoading, setIsLoading] = useState(false);
   
   const handleProcess = async () => {
     setIsLoading(true);
     try {
       // 处理逻辑
     } finally {
       setIsLoading(false);
     }
   };
   ```

### 第三阶段：优化完善

1. **性能优化**
   - 使用 `useMemo` 和 `useCallback`
   - 实现虚拟滚动（大数据列表）
   - 添加防抖和节流

2. **用户体验**
   - 添加进度指示
   - 实现实时预览
   - 提供操作反馈

3. **响应式设计**
   - 适配移动端
   - 处理不同屏幕尺寸
   - 优化触摸交互

## 🎯 工具类型

### 1. 简单工具 (Simple Tools)

**特点：**
- 纯前端实现
- 无需外部依赖
- 即时响应

**适用场景：**
- 文本处理（格式化、编码转换）
- 简单计算（单位转换、日期计算）
- 数据生成（UUID、随机数）

**示例代码：**
```typescript
export function TextFormatter() {
  const [text, setText] = useState('');
  
  const formatText = () => {
    // 纯JS实现的文本处理
    const formatted = text.toUpperCase();
    setText(formatted);
  };
  
  return (
    <div>
      <textarea value={text} onChange={(e) => setText(e.target.value)} />
      <button onClick={formatText}>格式化</button>
    </div>
  );
}
```

### 2. WebAssembly工具 (WASM Tools)

**特点：**
- 高性能计算
- 本地文件处理
- 无需服务器

**适用场景：**
- 图像处理（压缩、格式转换）
- 音视频编辑
- 文档转换

**集成步骤：**

1. **准备WASM模块**
   ```bash
   # 将WASM文件放入public目录
   public/wasm/your-module.wasm
   ```

2. **创建Worker**
   ```javascript
   // public/workers/image-processor.js
   let wasmModule;
   
   self.addEventListener('message', async (e) => {
     if (e.data.type === 'init') {
       wasmModule = await loadWasmModule('/wasm/image.wasm');
     } else if (e.data.type === 'process') {
       const result = wasmModule.processImage(e.data.image);
       self.postMessage({ result });
     }
   });
   ```

3. **在组件中使用**
   ```typescript
   export function ImageProcessor() {
     const workerRef = useRef<Worker>();
     
     useEffect(() => {
       workerRef.current = new Worker('/workers/image-processor.js');
       workerRef.current.postMessage({ type: 'init' });
       
       return () => workerRef.current?.terminate();
     }, []);
     
     const processImage = (file: File) => {
       workerRef.current?.postMessage({ 
         type: 'process', 
         image: file 
       });
     };
   }
   ```

### 3. API工具 (API Tools)

**特点：**
- 需要后端支持
- 可调用外部服务
- 支持复杂功能

**适用场景：**
- AI功能（文本生成、图像识别）
- 数据分析
- 第三方服务集成

**API路由示例：**

1. **创建API路由**
   ```typescript
   // app/api/tools/[toolId]/route.ts
   export async function POST(
     request: Request,
     { params }: { params: { toolId: string } }
   ) {
     const body = await request.json();
     
     // 调用外部API或处理逻辑
     const result = await processWithAPI(body);
     
     return Response.json({ 
       success: true, 
       data: result 
     });
   }
   ```

2. **在组件中调用**
   ```typescript
   const callAPI = async (input: any) => {
     const response = await fetch('/api/tools/my-tool', {
       method: 'POST',
       headers: { 'Content-Type': 'application/json' },
       body: JSON.stringify(input)
     });
     
     return response.json();
   };
   ```

## 📏 技术规范

### 代码规范

1. **TypeScript要求**
   - 所有组件必须使用TypeScript
   - 定义清晰的接口和类型
   - 避免使用 `any` 类型

2. **组件规范**
   - 使用函数组件和Hooks
   - 组件名使用PascalCase
   - 文件名与组件名一致

3. **样式规范**
   - 使用Tailwind CSS
   - 避免内联样式
   - 响应式设计优先

### 文件组织

```
src/components/tools/
├── YourTool.tsx           # 主组件
├── YourTool.types.ts      # 类型定义（可选）
├── YourTool.utils.ts      # 工具函数（可选）
└── YourTool.test.tsx      # 测试文件（推荐）
```

### 状态管理

1. **本地状态**
   ```typescript
   const [state, setState] = useState(initialValue);
   ```

2. **复杂状态**
   ```typescript
   const [state, dispatch] = useReducer(reducer, initialState);
   ```

3. **全局状态**（如需要）
   ```typescript
   // 使用Context API或状态管理库
   const { globalState } = useContext(AppContext);
   ```

## 🧪 测试指南

### 单元测试

```typescript
// YourTool.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { YourTool } from './YourTool';

describe('YourTool', () => {
  it('should render correctly', () => {
    render(<YourTool />);
    expect(screen.getByText('工具标题')).toBeInTheDocument();
  });
  
  it('should process input correctly', async () => {
    render(<YourTool />);
    const input = screen.getByRole('textbox');
    const button = screen.getByRole('button');
    
    fireEvent.change(input, { target: { value: 'test' } });
    fireEvent.click(button);
    
    await screen.findByText('处理结果');
  });
});
```

### 性能测试

1. **使用Chrome DevTools**
   - Performance面板分析
   - Memory面板检查内存泄漏
   - Network面板优化加载

2. **Lighthouse审计**
   ```bash
   # 运行Lighthouse
   pnpm build
   pnpm start
   # 在Chrome中运行Lighthouse审计
   ```

### PWA兼容性测试

确保工具支持离线使用：
1. 测试离线状态
2. 验证缓存策略
3. 检查Service Worker

## 🚀 发布流程

### 1. 开发阶段 (Development)

```typescript
// 在tools.ts中设置状态
{
  id: 'your-tool',
  status: 'development',
  // ...
}
```

- 功能开发中
- 内部测试
- 不对外显示

### 2. 测试阶段 (Beta)

```typescript
{
  id: 'your-tool',
  status: 'beta',
  // ...
}
```

- 功能基本完成
- 开放测试
- 收集反馈

### 3. 正式发布 (Stable)

```typescript
{
  id: 'your-tool',
  status: 'stable',
  featured: true, // 可选：设为特色工具
  // ...
}
```

- 功能稳定
- 性能优化完成
- 正式上线

### 发布检查清单

- [ ] 功能完整性测试
- [ ] 错误处理完善
- [ ] 响应式设计验证
- [ ] 性能优化完成
- [ ] 文档编写完整
- [ ] 代码审查通过
- [ ] PWA兼容性确认

## 💡 最佳实践

### 1. 用户体验

- **即时反馈**：所有操作都应有视觉反馈
- **错误提示**：清晰友好的错误信息
- **操作引导**：提供使用说明和示例
- **快捷操作**：支持键盘快捷键

### 2. 性能优化

- **懒加载**：按需加载组件和资源
- **缓存策略**：合理使用缓存
- **代码分割**：避免打包体积过大
- **优化算法**：选择高效的处理算法

### 3. 安全考虑

- **输入验证**：验证所有用户输入
- **XSS防护**：避免直接渲染HTML
- **数据隐私**：不收集敏感信息
- **HTTPS**：确保安全传输

### 4. 可维护性

- **代码注释**：关键逻辑添加注释
- **类型定义**：完整的TypeScript类型
- **模块化**：功能模块化设计
- **文档完善**：及时更新文档

## 📚 参考资源

### 技术文档
- [Next.js文档](https://nextjs.org/docs)
- [React文档](https://react.dev)
- [TypeScript文档](https://www.typescriptlang.org/docs)
- [Tailwind CSS文档](https://tailwindcss.com/docs)

### WebAssembly资源
- [MDN WebAssembly](https://developer.mozilla.org/en-US/docs/WebAssembly)
- [Emscripten](https://emscripten.org/)
- [AssemblyScript](https://www.assemblyscript.org/)

### 工具示例
- 查看 `src/components/tools/ImageCutout.tsx` - 图像处理示例
- 查看 `src/components/tools/UIGenerator.tsx` - AI工具示例

## 🤝 获取帮助

如果在开发过程中遇到问题：

1. 查看现有工具的实现作为参考
2. 查阅本文档和项目README
3. 在GitHub Issues提交问题
4. 参与社区讨论

---

祝您开发愉快！🎉