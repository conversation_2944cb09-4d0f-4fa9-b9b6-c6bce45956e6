**High‑Level Summary of the VERT Repository**

VERT (https://vert.sh) is a web‑based file‑conversion utility built with **Svelte**, **TypeScript**, and **Bun**. Its main goal is to perform conversions **on the client side** using WebAssembly, avoiding the need to upload files to a remote server (except for video conversion, which can be delegated to a self‑hosted `vertd` daemon or the official instance).

### Core Features
| Feature | Description |
|---------|-------------|
| **On‑device conversion** | Most file formats are processed directly in the browser via WebAssembly, ensuring privacy and no file‑size limits. |
| **Video conversion** | Handled by a separate Rust‑based service (`vertd`) that runs locally or on a remote server; the UI communicates with this daemon via HTTP. |
| **User‑friendly UI** | Built with Svelte, Tailwind CSS, and a set of reusable components (dialogs, toasts, panels, etc.). |
| **Configurable settings** | Users can adjust filename templates, analytics (Plausible), and the `vertd` endpoint through a settings page. |
| **Extensible architecture** | Converters are abstracted (`src/lib/converters`) and workers (`src/lib/workers`) handle heavy processing in the background. |
| **Static site generation** | Uses SvelteKit’s static adapter, making deployment simple (Docker, Nginx, Vercel, etc.). |
| **Open‑source** | Licensed under AGPL‑3.0; source code, issue tracking, and contributions are hosted on GitHub. |

### Project Structure Overview
```
├─ .docker* / .git* / .npmignore / .prettier*   ← configuration & ignore files
├─ Dockerfile, docker‑compose.yml                ← containerised deployment
├─ docs/*                                        ← documentation (FAQ, getting‑started, video conversion)
├─ src/
│  ├─ app.*                                     ← SvelteKit entry points
│  ├─ lib/
│  │  ├─ assets/                               ← images, fonts, SVGs
│  │  ├─ components/
│  │  │   ├─ functional/ (inputs, dialogs, upload UI)
│  │  │   ├─ layout/ (navbar, footer, toast system)
│  │  │   └─ visual/ (panels, progress bar, SVG icons)
│  │  ├─ consts.ts                             ← public constants (GitHub URLs, env vars)
│  │  ├─ converters/                           ← conversion logic (ffmpeg, pandoc, magick, vertd)
│  │  ├─ css/app.scss                          ← global styles (Tailwind + custom)
│  │  ├─ parse/                                 ← parsers (e.g., ICNS, ANI)
│  │  ├─ sections/                              ← page sections (about, settings)
│  │  ├─ store/                                 ← Svelte stores for dialogs, toasts, UI state
│  │  └─ types/                                 ← TypeScript type definitions
│  └─ routes/                                   ← page routes (+layout, +page files)
├─ static/                                       ← static assets (favicon, banner, wasm)
├─ svelte.config.js, vite.config.ts              ← build tooling configuration
└─ package.json, bun.lock                        ← dependencies & scripts
```

### Key Technical Details
* **Build System** – Uses **Bun** for package management and scripts (`bun i`, `bun run build`, `bun dev`). The Docker image builds the app with Bun and serves the static output via **Nginx**.
* **WebAssembly Workers** – Heavy conversion tasks (e.g., Pandoc, ImageMagick) run in Web Workers with WASM modules (`pandoc.wasm` is bundled in `static/`).
* **State Management** – Svelte stores (`DialogProvider`, `ToastProvider`, UI state) manage global UI feedback.
* **Environment Variables** – Public variables prefixed with `PUB_` control hostname, Plausible analytics, environment (`production`, `development`, `nightly`), and the default `vertd` endpoint.
* **Styling** – Tailwind CSS with custom CSS (`src/lib/css/app.scss`) and a set of custom fonts (`HostGrotesk-*.woff2`).

### Deployment Options
1. **Docker** – `docker-compose.yml` builds the image and runs Nginx to serve the compiled site.
2. **Static Hosting** – The static adapter outputs a `build/` folder that can be served by any static web server (e.g., Netlify, Vercel, GitHub Pages).
3. **Self‑hosted `vertd`** – For full‑local video conversion, run the Rust `vertd` daemon (separate repository) and point the UI to its URL via the Settings page.

### Documentation Highlights
* **GETTING_STARTED.md** – Shows prerequisites (Bun), installation steps, local development (`bun dev`), and production build (`bun run build`).
* **VIDEO_CONVERSION.md** – Explains how VERT delegates video work to `vertd`, why it’s necessary, and how to configure a custom instance.
* **DOCKER.md** – Provides Docker‑specific instructions for containerised deployment.

### License
The project is released under the **AGPL‑3.0** license, encouraging open collaboration while ensuring modifications remain open‑source.