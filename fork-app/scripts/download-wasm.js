#!/usr/bin/env node

/**
 * 下载WASM文件的脚本
 * 注意：这是一个示例脚本，实际的WASM文件需要从相应的项目获取
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

// WASM文件配置
const wasmFiles = [
  {
    name: 'pandoc.wasm',
    url: null, // Pandoc WASM需要从特定项目获取
    placeholder: true,
    size: '15MB'
  },
  {
    name: 'magick.wasm',
    url: 'https://unpkg.com/wasm-imagemagick@1.2.8/dist/magick.wasm',
    placeholder: false,
    size: '8MB'
  },
  {
    name: 'ffmpeg-core.wasm',
    url: 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd/ffmpeg-core.wasm',
    placeholder: false,
    size: '25MB'
  }
];

const wasmDir = path.join(__dirname, '..', 'public', 'wasm');

// 确保目录存在
if (!fs.existsSync(wasmDir)) {
  fs.mkdirSync(wasmDir, { recursive: true });
}

// 下载文件函数
function downloadFile(url, dest) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(dest);
    
    https.get(url, (response) => {
      if (response.statusCode === 302 || response.statusCode === 301) {
        // 处理重定向
        https.get(response.headers.location, (redirectResponse) => {
          redirectResponse.pipe(file);
          file.on('finish', () => {
            file.close(resolve);
          });
        }).on('error', reject);
      } else {
        response.pipe(file);
        file.on('finish', () => {
          file.close(resolve);
        });
      }
    }).on('error', reject);
  });
}

// 创建占位文件
function createPlaceholder(name) {
  const dest = path.join(wasmDir, name);
  const content = Buffer.from(`// Placeholder for ${name}\n// This is a mock file for development\n`);
  fs.writeFileSync(dest, content);
  console.log(`✅ Created placeholder: ${name}`);
}

// 主函数
async function main() {
  console.log('🚀 开始下载WASM文件...\n');

  for (const file of wasmFiles) {
    const dest = path.join(wasmDir, file.name);
    
    // 检查文件是否已存在
    if (fs.existsSync(dest)) {
      console.log(`⏭️  跳过: ${file.name} (已存在)`);
      continue;
    }

    if (file.placeholder || !file.url) {
      // 创建占位文件
      createPlaceholder(file.name);
    } else {
      // 下载实际文件
      console.log(`⬇️  下载中: ${file.name} (约${file.size})...`);
      try {
        await downloadFile(file.url, dest);
        const stats = fs.statSync(dest);
        const sizeMB = (stats.size / 1024 / 1024).toFixed(2);
        console.log(`✅ 下载完成: ${file.name} (${sizeMB}MB)`);
      } catch (error) {
        console.error(`❌ 下载失败: ${file.name}`, error.message);
        // 创建占位文件
        createPlaceholder(file.name);
      }
    }
  }

  console.log('\n✨ 完成！');
  console.log('\n📝 注意事项：');
  console.log('1. pandoc.wasm 需要从Pandoc WASM项目单独获取');
  console.log('2. 占位文件仅用于开发，实际功能需要真实的WASM文件');
  console.log('3. 可以从以下位置获取WASM文件：');
  console.log('   - ImageMagick: npm install wasm-imagemagick');
  console.log('   - FFmpeg: npm install @ffmpeg/core');
  console.log('   - Pandoc: 需要编译或从相关项目获取');
}

// 运行脚本
main().catch(console.error);