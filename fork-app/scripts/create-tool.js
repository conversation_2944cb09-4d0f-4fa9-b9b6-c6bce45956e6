#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 创建命令行交互接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 问题函数封装
const question = (query) => new Promise((resolve) => rl.question(query, resolve));

// 工具分类选项
const categories = [
  { value: 'text', label: '文本类' },
  { value: 'document', label: '文档类' },
  { value: 'image', label: '图像类' },
  { value: 'audio', label: '音频类' },
  { value: 'video', label: '视频类' },
  { value: 'developer', label: '开发类' },
  { value: 'utility', label: '实用工具' },
  { value: 'design', label: '设计类' },
  { value: 'crypto', label: '加密类' },
  { value: 'converter', label: '转换类' }
];

// 工具复杂度选项
const complexities = [
  { value: 'simple', label: '简单 (纯前端实现)' },
  { value: 'medium', label: '中等 (需要WebAssembly)' },
  { value: 'complex', label: '复杂 (需要后端API)' }
];

// 主函数
async function main() {
  console.log('\n🚀 欢迎使用 ToolFork 工具生成器\n');
  console.log('这个向导将帮助您快速创建一个新的工具。\n');

  try {
    // 收集工具信息
    const toolInfo = await collectToolInfo();
    
    // 确认信息
    console.log('\n📋 工具信息确认：');
    console.log('================================');
    console.log(`ID: ${toolInfo.id}`);
    console.log(`名称: ${toolInfo.name}`);
    console.log(`描述: ${toolInfo.description}`);
    console.log(`分类: ${toolInfo.categoryLabel}`);
    console.log(`复杂度: ${toolInfo.complexityLabel}`);
    console.log(`图标: ${toolInfo.icon}`);
    console.log('================================\n');

    const confirm = await question('确认创建这个工具吗？(y/n): ');
    
    if (confirm.toLowerCase() !== 'y') {
      console.log('❌ 已取消创建工具。');
      rl.close();
      return;
    }

    // 创建工具文件
    await createToolFiles(toolInfo);
    
    console.log('\n✅ 工具创建成功！\n');
    console.log('📝 接下来的步骤：');
    console.log(`1. 编辑 src/components/tools/${toolInfo.componentName}.tsx 实现工具功能`);
    console.log(`2. 运行 pnpm dev 查看效果`);
    console.log(`3. 完成开发后，更新工具状态为 'beta' 或 'stable'`);
    console.log('\n祝您开发愉快！🎉\n');

  } catch (error) {
    console.error('❌ 错误:', error.message);
  } finally {
    rl.close();
  }
}

// 收集工具信息
async function collectToolInfo() {
  const info = {};

  // 工具ID
  info.id = await question('请输入工具ID (小写英文，用-分隔，如 image-resize): ');
  if (!info.id || !/^[a-z0-9-]+$/.test(info.id)) {
    throw new Error('工具ID格式不正确，请使用小写英文和连字符');
  }

  // 工具名称
  info.name = await question('请输入工具名称 (如 图片压缩): ');
  if (!info.name) {
    throw new Error('工具名称不能为空');
  }

  // 工具描述
  info.description = await question('请输入工具描述 (简短说明): ');
  if (!info.description) {
    throw new Error('工具描述不能为空');
  }

  // 工具分类
  console.log('\n请选择工具分类:');
  categories.forEach((cat, index) => {
    console.log(`${index + 1}. ${cat.label} (${cat.value})`);
  });
  const categoryIndex = parseInt(await question('请输入分类编号 (1-10): ')) - 1;
  if (categoryIndex < 0 || categoryIndex >= categories.length) {
    throw new Error('分类选择无效');
  }
  info.category = categories[categoryIndex].value;
  info.categoryLabel = categories[categoryIndex].label;

  // 工具复杂度
  console.log('\n请选择工具复杂度:');
  complexities.forEach((comp, index) => {
    console.log(`${index + 1}. ${comp.label}`);
  });
  const complexityIndex = parseInt(await question('请输入复杂度编号 (1-3): ')) - 1;
  if (complexityIndex < 0 || complexityIndex >= complexities.length) {
    throw new Error('复杂度选择无效');
  }
  info.complexity = complexities[complexityIndex].value;
  info.complexityLabel = complexities[complexityIndex].label;

  // 工具图标
  info.icon = await question('请输入工具图标 (输入一个emoji，如 🎨): ') || '🔧';

  // 生成组件名称
  info.componentName = info.id.split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');

  return info;
}

// 创建工具文件
async function createToolFiles(toolInfo) {
  const basePath = path.join(__dirname, '..');
  
  // 1. 创建工具组件
  await createToolComponent(basePath, toolInfo);
  
  // 2. 更新工具数据
  await updateToolsData(basePath, toolInfo);
  
  // 3. 创建工具页面（如果需要）
  // 由于使用动态路由，不需要为每个工具创建单独的页面
  
  console.log('\n📁 已创建文件:');
  console.log(`  - src/components/tools/${toolInfo.componentName}.tsx`);
  console.log(`  - 已更新 src/data/tools.ts`);
}

// 创建工具组件
async function createToolComponent(basePath, toolInfo) {
  const componentsPath = path.join(basePath, 'src', 'components', 'tools');
  
  // 确保目录存在
  if (!fs.existsSync(componentsPath)) {
    fs.mkdirSync(componentsPath, { recursive: true });
  }

  // 选择合适的模板
  let template;
  switch (toolInfo.complexity) {
    case 'simple':
      template = getSimpleToolTemplate(toolInfo);
      break;
    case 'medium':
      template = getMediumToolTemplate(toolInfo);
      break;
    case 'complex':
      template = getComplexToolTemplate(toolInfo);
      break;
    default:
      template = getSimpleToolTemplate(toolInfo);
  }

  const componentPath = path.join(componentsPath, `${toolInfo.componentName}.tsx`);
  fs.writeFileSync(componentPath, template);
}

// 简单工具模板
function getSimpleToolTemplate(toolInfo) {
  return `'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';

interface ${toolInfo.componentName}Props {
  className?: string;
}

export function ${toolInfo.componentName}({ className }: ${toolInfo.componentName}Props) {
  const [input, setInput] = useState('');
  const [output, setOutput] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  const handleProcess = async () => {
    if (!input.trim()) return;
    
    setIsProcessing(true);
    
    try {
      // TODO: 实现具体的处理逻辑
      // 这里是${toolInfo.name}的核心功能实现
      
      // 模拟处理
      await new Promise(resolve => setTimeout(resolve, 1000));
      setOutput(\`处理结果: \${input}\`);
      
    } catch (error) {
      console.error('处理错误:', error);
      setOutput('处理失败，请重试');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReset = () => {
    setInput('');
    setOutput('');
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle>${toolInfo.name}</CardTitle>
          <CardDescription>
            ${toolInfo.description}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* 输入区域 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              输入内容
            </label>
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="请输入需要处理的内容..."
              className="w-full h-32 p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            />
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-center space-x-4">
            <Button
              onClick={handleProcess}
              disabled={!input.trim() || isProcessing}
              className="bg-green-600 hover:bg-green-700"
            >
              {isProcessing ? '处理中...' : '开始处理'}
            </Button>
            
            <Button
              onClick={handleReset}
              variant="outline"
            >
              重置
            </Button>
          </div>

          {/* 输出区域 */}
          {output && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                处理结果
              </label>
              <div className="p-4 bg-gray-50 rounded-lg">
                <pre className="whitespace-pre-wrap">{output}</pre>
              </div>
            </div>
          )}

          {/* 使用说明 */}
          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">💡 使用说明</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 在输入框中输入需要处理的内容</li>
              <li>• 点击"开始处理"按钮</li>
              <li>• 等待处理完成，查看结果</li>
              <li>• 可以复制结果或重新开始</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
`;
}

// 中等复杂度工具模板（WebAssembly）
function getMediumToolTemplate(toolInfo) {
  return `'use client';

import React, { useState, useEffect } from 'react';
import { Upload, Download, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';

interface ${toolInfo.componentName}Props {
  className?: string;
}

export function ${toolInfo.componentName}({ className }: ${toolInfo.componentName}Props) {
  const [file, setFile] = useState<File | null>(null);
  const [output, setOutput] = useState<any>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [wasmReady, setWasmReady] = useState(false);

  useEffect(() => {
    // TODO: 初始化WebAssembly模块
    // loadWasmModule().then(() => setWasmReady(true));
    setWasmReady(true); // 模拟WASM就绪
  }, []);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const uploadedFile = event.target.files?.[0];
    if (uploadedFile) {
      setFile(uploadedFile);
      setOutput(null);
    }
  };

  const handleProcess = async () => {
    if (!file || !wasmReady) return;
    
    setIsProcessing(true);
    
    try {
      // TODO: 使用WebAssembly处理文件
      // const result = await processWithWasm(file);
      
      // 模拟处理
      await new Promise(resolve => setTimeout(resolve, 2000));
      setOutput({ success: true, message: '处理完成' });
      
    } catch (error) {
      console.error('处理错误:', error);
      setOutput({ success: false, message: '处理失败' });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDownload = () => {
    // TODO: 实现下载功能
    console.log('下载结果');
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle>${toolInfo.name}</CardTitle>
          <CardDescription>
            ${toolInfo.description}（支持WebAssembly加速）
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {!wasmReady ? (
            <div className="text-center py-8">
              <Loader2 className="mx-auto h-8 w-8 animate-spin text-green-600 mb-2" />
              <p className="text-sm text-gray-600">正在加载WebAssembly模块...</p>
            </div>
          ) : (
            <>
              {/* 文件上传 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  选择文件
                </label>
                <div className="flex items-center space-x-4">
                  <input
                    type="file"
                    onChange={handleFileUpload}
                    className="flex-1"
                    accept="*/*"
                  />
                  {file && (
                    <span className="text-sm text-gray-600">
                      {file.name} ({(file.size / 1024).toFixed(2)} KB)
                    </span>
                  )}
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex justify-center space-x-4">
                <Button
                  onClick={handleProcess}
                  disabled={!file || isProcessing}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      处理中...
                    </>
                  ) : (
                    <>
                      <Upload className="w-4 h-4 mr-2" />
                      开始处理
                    </>
                  )}
                </Button>
                
                {output?.success && (
                  <Button
                    onClick={handleDownload}
                    variant="outline"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    下载结果
                  </Button>
                )}
              </div>

              {/* 结果显示 */}
              {output && (
                <div className={\`p-4 rounded-lg \${
                  output.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
                }\`}>
                  {output.message}
                </div>
              )}
            </>
          )}

          {/* 使用说明 */}
          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">💡 技术特性</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 使用WebAssembly技术，处理速度快</li>
              <li>• 所有处理在本地完成，保护隐私</li>
              <li>• 支持大文件处理，无需上传服务器</li>
              <li>• 跨平台兼容，支持各种浏览器</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
`;
}

// 复杂工具模板（需要API）
function getComplexToolTemplate(toolInfo) {
  return `'use client';

import React, { useState } from 'react';
import { Send, Loader2, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';

interface ${toolInfo.componentName}Props {
  className?: string;
}

interface ApiResponse {
  success: boolean;
  data?: any;
  error?: string;
}

export function ${toolInfo.componentName}({ className }: ${toolInfo.componentName}Props) {
  const [input, setInput] = useState('');
  const [response, setResponse] = useState<ApiResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [apiKey, setApiKey] = useState('');

  const handleSubmit = async () => {
    if (!input.trim()) return;
    
    setIsLoading(true);
    setResponse(null);
    
    try {
      // TODO: 调用实际的API
      const res = await fetch('/api/tools/${toolInfo.id}', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(apiKey && { 'Authorization': \`Bearer \${apiKey}\` })
        },
        body: JSON.stringify({ input })
      });

      const data = await res.json();
      setResponse(data);
      
    } catch (error) {
      console.error('API调用失败:', error);
      setResponse({
        success: false,
        error: '服务暂时不可用，请稍后重试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle>${toolInfo.name}</CardTitle>
          <CardDescription>
            ${toolInfo.description}（需要API支持）
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* API密钥输入（如果需要） */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              API密钥（可选）
            </label>
            <input
              type="password"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="输入您的API密钥..."
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
            />
          </div>

          {/* 主输入区域 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              输入内容
            </label>
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="请输入需要处理的内容..."
              className="w-full h-32 p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            />
          </div>

          {/* 提交按钮 */}
          <div className="flex justify-center">
            <Button
              onClick={handleSubmit}
              disabled={!input.trim() || isLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  处理中...
                </>
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  发送请求
                </>
              )}
            </Button>
          </div>

          {/* 响应显示 */}
          {response && (
            <div className={\`p-4 rounded-lg \${
              response.success ? 'bg-green-50' : 'bg-red-50'
            }\`}>
              {response.success ? (
                <div>
                  <h4 className="font-medium text-green-900 mb-2">✅ 处理成功</h4>
                  <pre className="text-sm text-green-800 whitespace-pre-wrap">
                    {JSON.stringify(response.data, null, 2)}
                  </pre>
                </div>
              ) : (
                <div className="flex items-start space-x-2">
                  <AlertCircle className="w-5 h-5 text-red-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-red-900">处理失败</h4>
                    <p className="text-sm text-red-800 mt-1">{response.error}</p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 使用说明 */}
          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">💡 API说明</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 此工具需要后端API支持</li>
              <li>• 可选提供API密钥以获得更高配额</li>
              <li>• 请求结果将实时显示</li>
              <li>• 支持批量处理和高级功能</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
`;
}

// 更新工具数据文件
async function updateToolsData(basePath, toolInfo) {
  const dataPath = path.join(basePath, 'src', 'data', 'tools.ts');
  
  if (!fs.existsSync(dataPath)) {
    console.error('❌ 找不到 tools.ts 文件');
    return;
  }

  let content = fs.readFileSync(dataPath, 'utf-8');
  
  // 创建新的工具数据对象
  const newTool = `  {
    id: '${toolInfo.id}',
    name: '${toolInfo.name}',
    description: '${toolInfo.description}',
    category: ToolCategory.${toolInfo.category.toUpperCase()},
    tags: ['${toolInfo.categoryLabel}'],
    icon: '${toolInfo.icon}',
    url: '/tool/${toolInfo.id}',
    featured: false,
    usageCount: 0
  }`;

  // 在数组末尾添加新工具
  const insertPosition = content.lastIndexOf('];');
  if (insertPosition !== -1) {
    // 找到最后一个工具对象的结束位置
    const lastToolEnd = content.lastIndexOf('}', insertPosition);
    if (lastToolEnd !== -1) {
      content = content.slice(0, lastToolEnd + 1) + ',\n' + newTool + content.slice(lastToolEnd + 1);
      fs.writeFileSync(dataPath, content);
    }
  }
}

// 运行主函数
main().catch(console.error);