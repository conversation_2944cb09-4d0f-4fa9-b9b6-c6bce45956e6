#!/usr/bin/env node

/**
 * ToolFork PWA 功能测试脚本
 * 用于验证 PWA 功能是否正常工作
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 ToolFork PWA 功能测试');
console.log('========================\n');

// 检查必要文件是否存在
const requiredFiles = [
  'public/manifest.json',
  'public/sw.js',
  'public/browserconfig.xml',
  'src/components/pwa/InstallPrompt.tsx',
  'src/components/pwa/PWASettings.tsx',
  'src/hooks/usePWA.ts'
];

console.log('📁 检查 PWA 相关文件...');
let allFilesExist = true;

requiredFiles.forEach(file => {
  const filePath = path.join(__dirname, '..', file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - 文件不存在`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.log('\n❌ 部分 PWA 文件缺失，请检查项目结构');
  process.exit(1);
}

console.log('\n📦 检查依赖包...');
try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
  const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
  
  if (dependencies['next-pwa']) {
    console.log(`✅ next-pwa: ${dependencies['next-pwa']}`);
  } else {
    console.log('❌ next-pwa 未安装');
    allFilesExist = false;
  }
  
  if (dependencies['workbox-webpack-plugin']) {
    console.log(`✅ workbox-webpack-plugin: ${dependencies['workbox-webpack-plugin']}`);
  } else {
    console.log('❌ workbox-webpack-plugin 未安装');
    allFilesExist = false;
  }
} catch (error) {
  console.log('❌ 无法读取 package.json');
  allFilesExist = false;
}

if (!allFilesExist) {
  console.log('\n❌ 依赖包检查失败');
  process.exit(1);
}

console.log('\n🔧 构建生产版本...');
try {
  execSync('pnpm build', { stdio: 'inherit', cwd: path.join(__dirname, '..') });
  console.log('✅ 构建成功');
} catch (error) {
  console.log('❌ 构建失败');
  process.exit(1);
}

console.log('\n🌐 启动生产服务器...');
console.log('请在浏览器中访问 http://localhost:3000 测试以下功能：');
console.log('');
console.log('📱 PWA 功能测试清单：');
console.log('  □ 1. 访问主页，检查是否显示安装提示');
console.log('  □ 2. 访问 /pwa 页面，查看 PWA 设置');
console.log('  □ 3. 在 Chrome DevTools > Application 中检查：');
console.log('     - Service Worker 是否注册');
console.log('     - Manifest 是否加载');
console.log('     - 缓存是否工作');
console.log('  □ 4. 运行 Lighthouse PWA 审计');
console.log('  □ 5. 测试离线功能（断网后刷新页面）');
console.log('  □ 6. 尝试安装应用到桌面');
console.log('');
console.log('🚀 启动服务器中...');

try {
  execSync('pnpm start', { stdio: 'inherit', cwd: path.join(__dirname, '..') });
} catch (error) {
  console.log('❌ 服务器启动失败');
  process.exit(1);
}
