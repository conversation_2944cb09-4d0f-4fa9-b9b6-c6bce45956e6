const fs = require('fs');
const path = require('path');

// 创建一个简单的 PNG 数据 URL（1x1 像素紫色图片）
const createPNGDataURL = (size) => {
  // 这是一个 1x1 紫色像素的 PNG 数据
  const pngData = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==';
  return `data:image/png;base64,${pngData}`;
};

// 创建占位图标文件（实际项目中应该使用真实的图标）
const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];
const iconsDir = path.join(__dirname, '../public/icons');

// 创建一个简单的 HTML 文件来生成图标
const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator</title>
</head>
<body>
    <canvas id="canvas"></canvas>
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        function generateIcon(size) {
            canvas.width = size;
            canvas.height = size;
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#7c3aed');
            gradient.addColorStop(1, '#a855f7');
            
            // 绘制圆角矩形背景
            const radius = size * 0.2;
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();
            
            // 绘制文字
            ctx.fillStyle = 'white';
            ctx.font = 'bold ' + (size * 0.4) + 'px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('工', size / 2, size / 2);
            
            return canvas.toDataURL('image/png');
        }
        
        // 生成所有尺寸的图标
        const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
        sizes.forEach(size => {
            const dataURL = generateIcon(size);
            console.log('Generated icon for size:', size);
        });
    </script>
</body>
</html>
`;

// 写入 HTML 文件
fs.writeFileSync(path.join(__dirname, 'icon-generator.html'), htmlContent);

// 创建简单的占位文件
iconSizes.forEach(size => {
  const filename = `icon-${size}x${size}.png`;
  const filepath = path.join(iconsDir, filename);
  
  // 创建一个简单的占位文件（实际项目中应该使用真实的图标）
  fs.writeFileSync(filepath, '');
  console.log(`Created placeholder for ${filename}`);
});

console.log('Placeholder icons created. Please replace with actual PNG icons.');
console.log('You can use the icon-generator.html file to generate icons manually.');
