const fs = require('fs');
const path = require('path');

// 创建 SVG 图标模板
const createSVGIcon = (size) => `
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a855f7;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="${size}" height="${size}" rx="${size * 0.2}" fill="url(#grad)"/>
  <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="${size * 0.4}" font-weight="bold" fill="white" text-anchor="middle" dominant-baseline="central">工</text>
</svg>
`;

// 图标尺寸列表
const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];

// 创建图标目录
const iconsDir = path.join(__dirname, '../public/icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// 生成 SVG 图标文件
iconSizes.forEach(size => {
  const svgContent = createSVGIcon(size);
  const filename = `icon-${size}x${size}.svg`;
  const filepath = path.join(iconsDir, filename);
  
  fs.writeFileSync(filepath, svgContent.trim());
  console.log(`Generated ${filename}`);
});

// 创建特殊用途图标
const specialIcons = [
  { name: 'search-96x96.svg', icon: '🔍' },
  { name: 'image-96x96.svg', icon: '🖼️' },
  { name: 'dev-96x96.svg', icon: '⚡' }
];

specialIcons.forEach(({ name, icon }) => {
  const svgContent = `
<svg width="96" height="96" viewBox="0 0 96 96" xmlns="http://www.w3.org/2000/svg">
  <rect width="96" height="96" rx="19" fill="#7c3aed"/>
  <text x="50%" y="50%" font-size="48" text-anchor="middle" dominant-baseline="central">${icon}</text>
</svg>
  `.trim();
  
  fs.writeFileSync(path.join(iconsDir, name), svgContent);
  console.log(`Generated ${name}`);
});

console.log('All icons generated successfully!');
