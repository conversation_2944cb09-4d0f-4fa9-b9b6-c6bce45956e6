'use client'

import { useState, useMemo, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import MC<PERSON>Header from '@/components/layout/MCPHeader'
import HeroSection from '@/components/layout/HeroSection'
import MCPFooter from '@/components/layout/MCPFooter'
import Tab<PERSON>avigation from '@/components/navigation/TabNavigation'
import CardGrid from '@/components/cards/CardGrid'
import ServerCard from '@/components/cards/ServerCard'
import ClientCard from '@/components/cards/ClientCard'
import FAQ from '@/components/sections/FAQ'
import { mockServers, mockClients, faqItems } from '@/data/mcp-data'
import { TabType } from '@/types/mcp'

function MCPPageContent() {
  const searchParams = useSearchParams()
  const [searchQuery, setSearchQuery] = useState('')
  const currentTab = (searchParams.get('tab') as TabType) || 'today'

  // 过滤数据基于 tab 和搜索
  const filteredServers = useMemo(() => {
    let servers = [...mockServers]
    
    // 根据 tab 过滤
    switch (currentTab) {
      case 'featured':
        servers = servers.filter(s => s.featured)
        break
      case 'latest':
        servers = servers.sort((a, b) => 
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )
        break
      case 'hosted':
        servers = servers.filter(s => s.hosted)
        break
      case 'official':
        servers = servers.filter(s => s.official)
        break
      case 'today':
      default:
        // 今日推荐 - 混合展示
        servers = servers.slice(0, 6)
    }

    // 搜索过滤
    if (searchQuery) {
      servers = servers.filter(s => 
        s.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        s.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        s.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    return servers
  }, [currentTab, searchQuery])

  const filteredClients = useMemo(() => {
    let clients = [...mockClients]
    
    // 搜索过滤
    if (searchQuery) {
      clients = clients.filter(c => 
        c.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        c.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        c.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    return currentTab === 'clients' ? clients : clients.slice(0, 6)
  }, [currentTab, searchQuery])

  return (
    <div className="min-h-screen bg-gray-50 text-gray-900">
      {/* Header */}
      <MCPHeader />

      {/* Hero Section */}
      <HeroSection 
        totalServers={16382}
        onSearch={setSearchQuery}
      />

      {/* Tab Navigation */}
      <TabNavigation activeTab={currentTab} />

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        {/* Featured Servers */}
        {currentTab !== 'clients' && (
          <CardGrid 
            title="Featured MCP Servers" 
            viewAllLink="/servers?tag=featured"
          >
            {filteredServers.map((server) => (
              <ServerCard
                key={server.id}
                id={server.id}
                name={server.name}
                description={server.description}
                author={server.author}
                imageUrl={server.imageUrl}
                avatar={server.avatar}
                isSponsored={server.sponsored}
                verified={server.verified}
              />
            ))}
          </CardGrid>
        )}

        {/* Featured Clients */}
        <CardGrid 
          title="Featured MCP Clients" 
          viewAllLink="/clients?tag=featured"
        >
          {filteredClients.map((client) => (
            <ClientCard
              key={client.id}
              id={client.id}
              name={client.name}
              description={client.description}
              author={client.author}
              imageUrl={client.imageUrl}
              avatar={client.avatar}
              isSponsored={client.sponsored}
              verified={client.verified}
            />
          ))}
        </CardGrid>

        {/* Latest Servers */}
        {currentTab === 'latest' && (
          <CardGrid 
            title="Latest MCP Servers" 
            viewAllLink="/servers?tag=latest"
          >
            {mockServers
              .sort((a, b) => 
                new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
              )
              .slice(0, 6)
              .map((server) => (
                <ServerCard
                  key={server.id}
                  id={server.id}
                  name={server.name}
                  description={server.description}
                  author={server.author}
                  imageUrl={server.imageUrl}
                  avatar={server.avatar}
                  isSponsored={server.sponsored}
                  verified={server.verified}
                />
              ))}
          </CardGrid>
        )}

        {/* Latest Clients */}
        {currentTab === 'latest' && (
          <CardGrid 
            title="Latest MCP Clients" 
            viewAllLink="/clients?tag=latest"
          >
            {mockClients
              .slice(0, 3)
              .map((client) => (
                <ClientCard
                  key={client.id}
                  id={client.id}
                  name={client.name}
                  description={client.description}
                  author={client.author}
                  imageUrl={client.imageUrl}
                  avatar={client.avatar}
                  isSponsored={client.sponsored}
                  verified={client.verified}
                />
              ))}
          </CardGrid>
        )}
      </main>

      {/* FAQ Section */}
      <FAQ items={faqItems} />

      {/* Footer */}
      <MCPFooter />
    </div>
  )
}

export default function MCPPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    }>
      <MCPPageContent />
    </Suspense>
  )
}