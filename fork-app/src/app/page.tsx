'use client';

import React, { useState, useMemo, useEffect } from 'react';
import Link from 'next/link';
import MainHeader from '@/components/layout/MainHeader';
import MainHero from '@/components/layout/MainHero';
import ToolCardNew from '@/components/tools/ToolCardNew';
import { CategoryNav } from '@/components/tools/CategoryNav';
import { TOOLS_DATA, getFeaturedTools, getToolsByCategory, searchTools } from '@/data/tools';
import { ToolCategory } from '@/types/tool';
import { ArrowRight, Loader2 } from 'lucide-react';

export default function Home() {
  const [activeCategory, setActiveCategory] = useState<ToolCategory | 'all'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [isCategoryChanging, setIsCategoryChanging] = useState(false);

  // 根据搜索查询和分类过滤工具
  const filteredTools = useMemo(() => {
    if (searchQuery.trim()) {
      return searchTools(searchQuery);
    }

    if (activeCategory === 'all') {
      return TOOLS_DATA;
    }

    return getToolsByCategory(activeCategory);
  }, [searchQuery, activeCategory]);

  const featuredTools = getFeaturedTools();

  const handleSearch = (query: string) => {
    setIsSearching(true);
    setSearchQuery(query);

    // 如果搜索查询为空，重置到默认状态
    if (!query.trim()) {
      setActiveCategory('all');
    } else {
      setActiveCategory('all'); // 搜索时重置分类
    }

    // 模拟搜索延迟
    setTimeout(() => {
      setIsSearching(false);
    }, 300);
  };

  const handleCategoryChange = (category: ToolCategory | 'all') => {
    setIsCategoryChanging(true);
    setActiveCategory(category);

    // 切换分类时清空搜索
    if (searchQuery.trim()) {
      setSearchQuery('');
    }

    // 模拟分类切换延迟
    setTimeout(() => {
      setIsCategoryChanging(false);
    }, 200);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <MainHeader />
      
      {/* Hero Section */}
      <MainHero 
        totalTools={TOOLS_DATA.length}
        onSearch={handleSearch}
      />
      
      <main className="container mx-auto px-4 py-8">
        {/* 分类导航 */}
        <section className="mb-8">
          <CategoryNav
            activeCategory={activeCategory}
            onCategoryChange={handleCategoryChange}
            isLoading={isCategoryChanging}
          />
        </section>

        {/* 特色工具 - 仅在显示所有工具且无搜索时显示 */}
        {!searchQuery && activeCategory === 'all' && (
          <section className="mb-12">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">特色工具</h2>
              <Link
                href="/tools?featured=true"
                className="flex items-center gap-1 text-sm text-purple-600 hover:text-purple-700 transition-colors"
              >
                查看全部
                <ArrowRight className="w-4 h-4" />
              </Link>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5">
              {featuredTools.map((tool) => (
                <ToolCardNew
                  key={tool.id}
                  tool={tool}
                  featured={true}
                />
              ))}
            </div>
          </section>
        )}

        {/* 工具列表 */}
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              {searchQuery.trim() ? `搜索结果: "${searchQuery}"` :
               activeCategory === 'all' ? '所有工具' :
               `${activeCategory === 'image' ? '图像类' : activeCategory === 'developer' ? '开发类' : activeCategory} 工具`}
              {(isSearching || isCategoryChanging) && (
                <Loader2 className="w-5 h-5 animate-spin text-purple-600" />
              )}
            </h2>
            <span className="text-sm text-gray-500">
              共 {filteredTools.length} 个工具
            </span>
          </div>

          {(isSearching || isCategoryChanging) ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5">
              {/* 骨架屏 */}
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="bg-white border border-gray-200 rounded-xl p-4 md:p-5 animate-pulse">
                  <div className="flex items-start gap-3 md:gap-4">
                    <div className="w-12 h-12 md:w-14 md:h-14 bg-gray-200 rounded-xl"></div>
                    <div className="flex-1">
                      <div className="h-4 md:h-5 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 md:h-4 bg-gray-200 rounded mb-3 w-3/4"></div>
                      <div className="flex gap-1.5 md:gap-2 mb-3">
                        <div className="h-5 md:h-6 bg-gray-200 rounded-full w-12"></div>
                        <div className="h-5 md:h-6 bg-gray-200 rounded-full w-16"></div>
                      </div>
                      <div className="h-3 md:h-4 bg-gray-200 rounded w-20"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : filteredTools.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5">
              {filteredTools.map((tool) => (
                <ToolCardNew
                  key={tool.id}
                  tool={tool}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">未找到相关工具</h3>
              <p className="text-gray-500">试试其他关键词或浏览不同分类</p>
            </div>
          )}
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-50 border-t border-gray-200 mt-16">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row items-center justify-between gap-6">
            {/* Copyright */}
            <div className="text-sm text-gray-600 text-center md:text-left">
              <p>© 2025 在线工具. All rights reserved.</p>
              <p className="mt-1">
                Powered by Next.js & WebAssembly
              </p>
            </div>

            {/* Footer Navigation */}
            <nav className="flex flex-wrap items-center justify-center gap-4">
              <Link href="/" className="text-sm text-gray-600 hover:text-purple-600 transition-colors">
                首页
              </Link>
              <Link href="/tools" className="text-sm text-gray-600 hover:text-purple-600 transition-colors">
                所有工具
              </Link>
              <Link href="/articles" className="text-sm text-gray-600 hover:text-purple-600 transition-colors">
                文库
              </Link>
              <Link href="/mcp" className="text-sm text-gray-600 hover:text-purple-600 transition-colors">
                MCP
              </Link>
              <Link href="/about" className="text-sm text-gray-600 hover:text-purple-600 transition-colors">
                关于我们
              </Link>
            </nav>

            {/* Legal Links */}
            <div className="flex items-center gap-4">
              <Link href="/privacy" className="text-sm text-gray-600 hover:text-purple-600 transition-colors">
                隐私政策
              </Link>
              <Link href="/terms" className="text-sm text-gray-600 hover:text-purple-600 transition-colors">
                服务条款
              </Link>
              <a href="https://beian.miit.gov.cn/" className="text-sm text-gray-600 hover:text-purple-600 transition-colors" target="_blank" rel="noopener noreferrer">
                浙ICP备14020137号-1
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
