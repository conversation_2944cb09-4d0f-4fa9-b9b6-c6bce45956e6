import React from 'react';
import MainHeader from '@/components/layout/MainHeader';
import PWASettings from '@/components/pwa/PWASettings';

export const metadata = {
  title: 'PWA 设置',
  description: '管理 ToolFork 应用的 PWA 功能，包括安装、更新和缓存管理。',
};

export default function PWAPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <MainHeader />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* 页面标题 */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              PWA 设置
            </h1>
            <p className="text-gray-600 max-w-2xl mx-auto">
              管理 ToolFork 应用的渐进式 Web 应用功能，包括应用安装、更新检查和缓存管理。
            </p>
          </div>

          {/* PWA 设置组件 */}
          <PWASettings />

          {/* 功能说明 */}
          <div className="mt-8 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              PWA 功能说明
            </h2>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium text-gray-900 mb-2">🚀 应用安装</h3>
                <p className="text-sm text-gray-600 mb-4">
                  将 ToolFork 安装到您的设备上，获得类似原生应用的体验。安装后可以从桌面或应用列表直接启动。
                </p>
                
                <h3 className="font-medium text-gray-900 mb-2">📱 离线访问</h3>
                <p className="text-sm text-gray-600">
                  即使在网络连接不稳定或完全离线的情况下，您仍然可以访问已缓存的工具和内容。
                </p>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900 mb-2">⚡ 快速加载</h3>
                <p className="text-sm text-gray-600 mb-4">
                  通过智能缓存策略，应用会预先缓存重要资源，确保快速加载和流畅的用户体验。
                </p>
                
                <h3 className="font-medium text-gray-900 mb-2">🔄 自动更新</h3>
                <p className="text-sm text-gray-600">
                  应用会在后台自动检查更新，确保您始终使用最新版本的功能和安全修复。
                </p>
              </div>
            </div>
          </div>

          {/* 浏览器兼容性 */}
          <div className="mt-6 bg-blue-50 rounded-xl p-6">
            <h3 className="font-medium text-blue-900 mb-3">
              浏览器兼容性
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-blue-800">Chrome 67+</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-blue-800">Edge 79+</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <span className="text-blue-800">Safari 11.1+</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-blue-800">Firefox 58+</span>
              </div>
            </div>
            <p className="text-xs text-blue-700 mt-3">
              • 绿色：完整支持 • 黄色：部分支持
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
