'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { ArrowLeft, Heart, Share2, BookmarkPlus } from 'lucide-react';
import { Header } from '@/components/layout/Header';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { TOOLS_DATA } from '@/data/tools';
import { CATEGORY_LABELS, Tool } from '@/types/tool';
import { loadToolComponent, isToolRegistered } from '@/lib/tool-registry';

interface ToolPageClientProps {
  tool: Tool;
}

export default function ToolPageClient({ tool }: ToolPageClientProps) {
  const [isFavorited, setIsFavorited] = useState(false);

  const handleFavorite = () => {
    setIsFavorited(!isFavorited);
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: tool.name,
        text: tool.description,
        url: window.location.href,
      });
    } else {
      // Fallback to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-6">
          <Link href="/" className="hover:text-green-600">工具</Link>
          <span>›</span>
          <Link href={`/?category=${tool.category}`} className="hover:text-green-600">
            {CATEGORY_LABELS[tool.category]}
          </Link>
          <span>›</span>
          <span className="text-gray-900">{tool.name}</span>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Tool Header */}
            <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-4">
                  <div className="text-4xl">{tool.icon}</div>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">{tool.name}</h1>
                    <p className="text-gray-600 mt-1">{tool.description}</p>
                    <div className="flex items-center gap-3 mt-3">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {CATEGORY_LABELS[tool.category]}
                      </span>
                      {tool.usageCount && (
                        <span className="text-sm text-gray-500">
                          已使用 {tool.usageCount.toLocaleString()} 次
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleFavorite}
                    className={isFavorited ? 'text-red-500' : ''}
                  >
                    <Heart className={`w-4 h-4 ${isFavorited ? 'fill-current' : ''}`} />
                    <span className="ml-1">收藏</span>
                  </Button>
                  <Button variant="ghost" size="sm" onClick={handleShare}>
                    <Share2 className="w-4 h-4" />
                    <span className="ml-1">分享</span>
                  </Button>
                </div>
              </div>
            </div>

            {/* Tool Interface */}
            <Card className="mb-8">
              <CardHeader>
                <CardTitle>{tool.name}</CardTitle>
                <CardDescription>在线使用 {tool.name} 工具</CardDescription>
              </CardHeader>
              <CardContent>
                {getToolInterface(tool.id)}
              </CardContent>
            </Card>

            {/* Tool Description */}
            <Card>
              <CardHeader>
                <CardTitle>工具说明</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose prose-gray max-w-none">
                  <p>{tool.description}</p>
                  <p>这是一个功能强大的{tool.name}工具，可以帮助您快速完成相关任务。</p>
                  
                  <h3>主要特性</h3>
                  <ul>
                    <li>简单易用的界面设计</li>
                    <li>快速处理和实时预览</li>
                    <li>支持多种格式和选项</li>
                    <li>完全免费使用</li>
                  </ul>

                  <h3>使用方法</h3>
                  <ol>
                    <li>按照界面提示输入或上传需要处理的内容</li>
                    <li>选择合适的处理选项</li>
                    <li>点击处理按钮开始操作</li>
                    <li>下载或复制处理结果</li>
                  </ol>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Quick Actions */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="text-lg">快捷操作</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full" onClick={handleFavorite}>
                  <BookmarkPlus className="w-4 h-4 mr-2" />
                  加入收藏
                </Button>
                <Button variant="outline" className="w-full" onClick={handleShare}>
                  <Share2 className="w-4 h-4 mr-2" />
                  分享工具
                </Button>
                <Link href="/" className="block">
                  <Button variant="ghost" className="w-full">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    返回首页
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Tool Stats */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="text-lg">使用统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">总使用次数</span>
                    <span className="font-semibold">{tool.usageCount?.toLocaleString() || 0}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">今日使用</span>
                    <span className="font-semibold">128</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">用户评分</span>
                    <span className="font-semibold">4.8/5</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Related Tools */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">相关工具</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {TOOLS_DATA
                    .filter(t => t.category === tool.category && t.id !== tool.id)
                    .slice(0, 3)
                    .map((relatedTool) => (
                      <Link
                        key={relatedTool.id}
                        href={`/tool/${relatedTool.id}`}
                        className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        <span className="text-lg">{relatedTool.icon}</span>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {relatedTool.name}
                          </p>
                          <p className="text-xs text-gray-500 truncate">
                            {relatedTool.description}
                          </p>
                        </div>
                      </Link>
                    ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}

// 根据工具ID返回对应的界面组件
function getToolInterface(toolId: string) {
  // 特殊处理的内置工具
  if (toolId === 'coin-flip') {
    return <CoinFlipInterface />;
  }
  
  // 检查是否已注册组件
  if (isToolRegistered(toolId)) {
    const ToolComponent = loadToolComponent(toolId);
    return <ToolComponent />;
  }
  
  // 默认组件
  return <DefaultToolInterface toolId={toolId} />;
}

// 抛硬币界面
function CoinFlipInterface() {
  const [result, setResult] = useState<'heads' | 'tails' | null>(null);
  const [isFlipping, setIsFlipping] = useState(false);

  const flipCoin = () => {
    setIsFlipping(true);
    setTimeout(() => {
      setResult(Math.random() < 0.5 ? 'heads' : 'tails');
      setIsFlipping(false);
    }, 1000);
  };

  return (
    <div className="text-center space-y-6">
      <div className="h-40 flex items-center justify-center">
        {isFlipping ? (
          <div className="animate-spin text-6xl">🪙</div>
        ) : result ? (
          <div className="text-6xl">
            {result === 'heads' ? '👑' : '🪙'}
          </div>
        ) : (
          <div className="text-6xl text-gray-400">🪙</div>
        )}
      </div>
      
      {result && !isFlipping && (
        <div className="text-xl font-semibold text-gray-900">
          {result === 'heads' ? '正面！' : '反面！'}
        </div>
      )}
      
      <Button 
        onClick={flipCoin} 
        disabled={isFlipping}
        className="bg-green-600 hover:bg-green-700"
      >
        {isFlipping ? '投掷中...' : '投掷硬币'}
      </Button>
    </div>
  );
}

// 默认工具界面
function DefaultToolInterface({ toolId }: { toolId: string }) {
  return (
    <div className="text-center py-12">
      <div className="text-6xl mb-4">🔧</div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">工具开发中</h3>
      <p className="text-gray-600">
        {toolId} 工具正在开发中，敬请期待！
      </p>
    </div>
  );
}