import React from 'react';
import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { TOOLS_DATA } from '@/data/tools';
import { CATEGORY_LABELS } from '@/types/tool';
import ToolPageClient from './ToolPageClient';

interface ToolPageProps {
  params: Promise<{ id: string }>;
}

// 生成动态SEO metadata
export async function generateMetadata({ params }: ToolPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const tool = TOOLS_DATA.find(t => t.id === resolvedParams.id);
  
  if (!tool) {
    return {
      title: '工具未找到',
      description: '抱歉，您访问的工具不存在。',
    };
  }

  return {
    title: `${tool.name} - ${tool.description}`,
    description: `使用我们的${tool.name}工具，${tool.description}。免费在线使用，无需下载安装。`,
    keywords: [tool.name, CATEGORY_LABELS[tool.category], ...tool.tags, '在线工具'],
    openGraph: {
      title: `${tool.name} - 在线工具`,
      description: tool.description,
      type: 'website',
    },
    twitter: {
      card: 'summary',
      title: `${tool.name} - 在线工具`,
      description: tool.description,
    },
  };
}

export default async function ToolPage({ params }: ToolPageProps) {
  const resolvedParams = await params;
  const tool = TOOLS_DATA.find(t => t.id === resolvedParams.id);

  if (!tool) {
    notFound();
  }

  return <ToolPageClient tool={tool} />;
}