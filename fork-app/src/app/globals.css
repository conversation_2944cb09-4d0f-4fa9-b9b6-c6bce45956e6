@import "tailwindcss";

:root {
  /* Light Theme Colors */
  --background: 0 0% 100%; /* White */
  --foreground: 0 0% 9%; /* Nearly black text */
  --card: 0 0% 100%; /* White cards */
  --card-foreground: 0 0% 9%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 9%;
  --primary: 271 91% 65%; /* Purple: #6E29F6 */
  --primary-foreground: 0 0% 100%;
  --secondary: 0 0% 96%; /* Light gray */
  --secondary-foreground: 0 0% 9%;
  --muted: 0 0% 96%; /* Light gray for muted elements */
  --muted-foreground: 0 0% 45%; /* Medium gray text */
  --accent: 271 91% 65%; /* Purple accent */
  --accent-foreground: 0 0% 100%;
  --destructive: 0 62.8% 50.6%; /* Red for errors */
  --destructive-foreground: 0 0% 100%;
  --border: 0 0% 89%; /* Light gray border */
  --input: 0 0% 89%; /* Light gray input border */
  --ring: 271 91% 65%; /* Purple focus ring */
  
  /* Custom Purple shades for light theme */
  --purple-50: 270 100% 98%;
  --purple-100: 270 100% 95%;
  --purple-200: 271 100% 91%;
  --purple-300: 271 97% 82%;
  --purple-400: 271 94% 73%;
  --purple-500: 271 91% 65%; /* Main purple */
  --purple-600: 271 81% 56%;
  --purple-700: 271 72% 47%;
  --purple-800: 271 67% 38%;
  --purple-900: 271 65% 30%;
  
  /* Gray scale for light theme */
  --gray-50: 0 0% 98%;
  --gray-100: 0 0% 96%;
  --gray-200: 0 0% 89%;
  --gray-300: 0 0% 83%;
  --gray-400: 0 0% 64%;
  --gray-500: 0 0% 45%;
  --gray-600: 0 0% 32%;
  --gray-700: 0 0% 25%;
  --gray-800: 0 0% 15%;
  --gray-900: 0 0% 9%;
}

@theme inline {
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));
  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));
  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));
  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));
  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));
  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));
  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Dark theme is the default - no media query needed */

* {
  border-color: hsl(var(--border));
}

body {
  color: hsl(var(--foreground));
  background: hsl(var(--background));
}

/* Custom utilities */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* Checker pattern for transparency */
.bg-checker-pattern {
  background-image: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                    linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                    linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

/* Mobile touch improvements */
.touch-manipulation {
  touch-action: manipulation;
}

/* Smooth scrolling for mobile category navigation */
.category-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Grid background pattern */
.bg-grid-gray-100\/50 {
  background-image: radial-gradient(circle, #f3f4f6 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Enhanced focus styles for accessibility */
.focus-visible:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Loading animation improvements */
@keyframes pulse-soft {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-pulse-soft {
  animation: pulse-soft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Mobile-specific improvements */
@media (max-width: 768px) {
  /* Improve touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better spacing for mobile */
  .mobile-spacing {
    padding: 1rem;
  }

  /* Optimize text sizes for mobile */
  .mobile-text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .mobile-text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}
