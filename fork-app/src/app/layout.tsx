import type { Metadata, Viewport } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import InstallPrompt from "@/components/pwa/InstallPrompt";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: {
    default: "ToolFork - 在线工具聚合站",
    template: "%s | ToolFork"
  },
  description: "基于 WebAssembly 技术的现代化在线工具聚合平台，提供安全、高效的客户端文件处理服务。隐私优先，无需上传文件。",
  keywords: ["在线工具", "WebAssembly", "抠图工具", "UI生成器", "开发工具", "格式转换", "图片处理", "PWA"],
  authors: [{ name: "ToolFork Team" }],
  creator: "ToolFork",
  publisher: "ToolFork",
  applicationName: "ToolFork",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://toolfork.app'),
  alternates: {
    canonical: '/',
  },
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'ToolFork',
    startupImage: [
      {
        url: '/icons/icon-192x192.svg',
        media: '(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2)',
      },
    ],
  },
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: 'https://toolfork.app',
    title: 'ToolFork - 在线工具聚合站',
    description: '基于 WebAssembly 技术的现代化在线工具聚合平台，提供安全、高效的客户端文件处理服务。',
    siteName: 'ToolFork',
    images: [
      {
        url: '/icons/icon-512x512.svg',
        width: 512,
        height: 512,
        alt: 'ToolFork Logo',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'ToolFork - 在线工具聚合站',
    description: '基于 WebAssembly 技术的现代化在线工具聚合平台，提供安全、高效的客户端文件处理服务。',
    images: ['/icons/icon-512x512.svg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  other: {
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'apple-mobile-web-app-title': 'ToolFork',
    'application-name': 'ToolFork',
    'msapplication-TileColor': '#7c3aed',
    'msapplication-config': '/browserconfig.xml',
    'theme-color': '#7c3aed',
  },
};

export const viewport: Viewport = {
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#7c3aed' },
    { media: '(prefers-color-scheme: dark)', color: '#7c3aed' },
  ],
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: 'cover',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <head>
        <link rel="icon" href="/icons/icon-192x192.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/icons/icon-192x192.svg" />
        <meta name="theme-color" content="#7c3aed" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-white text-gray-900`}
      >
        {children}
        <InstallPrompt />
      </body>
    </html>
  );
}
