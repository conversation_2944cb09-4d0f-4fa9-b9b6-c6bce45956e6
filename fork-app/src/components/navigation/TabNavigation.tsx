'use client'

import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import { Calendar, Star, Clock, Monitor, Cloud, Shield } from 'lucide-react'

interface TabNavigationProps {
  activeTab?: string
}

export default function TabNavigation({ activeTab = 'today' }: TabNavigationProps) {
  const searchParams = useSearchParams()
  const currentTab = searchParams.get('tab') || activeTab

  const tabs = [
    { id: 'today', label: 'Today', icon: Calendar, href: '/' },
    { id: 'featured', label: 'Featured', icon: Star, href: '/?tab=featured' },
    { id: 'latest', label: 'Latest', icon: Clock, href: '/?tab=latest' },
    { id: 'clients', label: 'Clients', icon: Monitor, href: '/?tab=clients' },
    { id: 'hosted', label: 'Hosted', icon: Cloud, href: '/?tab=hosted' },
    { id: 'official', label: 'Official', icon: Shield, href: '/?tab=official' },
  ]

  return (
    <div className="border-b border-gray-200 bg-white">
      <div className="container mx-auto px-4">
        <nav className="flex items-center gap-2 overflow-x-auto py-3 scrollbar-hide">
          {tabs.map((tab) => {
            const Icon = tab.icon
            const isActive = tab.id === currentTab
            
            return (
              <Link
                key={tab.id}
                href={tab.href}
                className={`
                  flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all
                  whitespace-nowrap flex-shrink-0
                  ${isActive 
                    ? 'bg-purple-100 text-purple-700 border border-purple-200' 
                    : 'text-gray-600 hover:text-purple-600 hover:bg-purple-50 border border-transparent'
                  }
                `}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </Link>
            )
          })}
        </nav>
      </div>
    </div>
  )
}