'use client';

import React, { useState, useEffect } from 'react';
import { 
  Download, 
  RefreshCw, 
  Trash2, 
  Wifi, 
  WifiOff, 
  Smartphone, 
  HardDrive,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';
import { usePWA } from '@/hooks/usePWA';

export default function PWASettings() {
  const {
    isInstallable,
    isInstalled,
    isOnline,
    isUpdateAvailable,
    installApp,
    updateApp,
    checkForUpdates,
    getCacheSize,
    clearCache,
  } = usePWA();

  const [cacheSize, setCacheSize] = useState<number>(0);
  const [isCheckingUpdates, setIsCheckingUpdates] = useState(false);
  const [isClearingCache, setIsClearingCache] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);

  const loadCacheSize = React.useCallback(async () => {
    const size = await getCacheSize();
    setCacheSize(size);
  }, [getCacheSize]);

  useEffect(() => {
    loadCacheSize();
  }, [loadCacheSize]);

  const handleInstall = async () => {
    setIsInstalling(true);
    try {
      await installApp();
    } finally {
      setIsInstalling(false);
    }
  };

  const handleUpdate = async () => {
    try {
      await updateApp();
    } catch (error) {
      console.error('更新失败:', error);
    }
  };

  const handleCheckUpdates = async () => {
    setIsCheckingUpdates(true);
    try {
      await checkForUpdates();
    } finally {
      setIsCheckingUpdates(false);
    }
  };

  const handleClearCache = async () => {
    setIsClearingCache(true);
    try {
      await clearCache();
      await loadCacheSize();
    } finally {
      setIsClearingCache(false);
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">PWA 设置</h2>
        
        {/* 应用状态 */}
        <div className="space-y-4 mb-6">
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-3">
              <Smartphone className="w-5 h-5 text-gray-600" />
              <span className="font-medium text-gray-900">应用状态</span>
            </div>
            <div className="flex items-center gap-2">
              {isInstalled ? (
                <>
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm text-green-600">已安装</span>
                </>
              ) : (
                <>
                  <AlertCircle className="w-4 h-4 text-orange-500" />
                  <span className="text-sm text-orange-600">未安装</span>
                </>
              )}
            </div>
          </div>

          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-3">
              {isOnline ? (
                <Wifi className="w-5 h-5 text-green-500" />
              ) : (
                <WifiOff className="w-5 h-5 text-red-500" />
              )}
              <span className="font-medium text-gray-900">网络状态</span>
            </div>
            <span className={`text-sm ${isOnline ? 'text-green-600' : 'text-red-600'}`}>
              {isOnline ? '在线' : '离线'}
            </span>
          </div>

          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-3">
              <HardDrive className="w-5 h-5 text-gray-600" />
              <span className="font-medium text-gray-900">缓存大小</span>
            </div>
            <span className="text-sm text-gray-600">{formatBytes(cacheSize)}</span>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="space-y-3">
          {/* 安装应用 */}
          {isInstallable && !isInstalled && (
            <button
              onClick={handleInstall}
              disabled={isInstalling}
              className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Download className={`w-4 h-4 ${isInstalling ? 'animate-pulse' : ''}`} />
              {isInstalling ? '安装中...' : '安装应用'}
            </button>
          )}

          {/* 更新应用 */}
          {isUpdateAvailable && (
            <button
              onClick={handleUpdate}
              className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              更新应用
            </button>
          )}

          {/* 检查更新 */}
          <button
            onClick={handleCheckUpdates}
            disabled={isCheckingUpdates}
            className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <RefreshCw className={`w-4 h-4 ${isCheckingUpdates ? 'animate-spin' : ''}`} />
            {isCheckingUpdates ? '检查中...' : '检查更新'}
          </button>

          {/* 清除缓存 */}
          <button
            onClick={handleClearCache}
            disabled={isClearingCache}
            className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-red-100 text-red-700 font-medium rounded-lg hover:bg-red-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Trash2 className={`w-4 h-4 ${isClearingCache ? 'animate-pulse' : ''}`} />
            {isClearingCache ? '清除中...' : '清除缓存'}
          </button>
        </div>

        {/* 信息提示 */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <div className="flex items-start gap-3">
            <Info className="w-5 h-5 text-blue-500 flex-shrink-0 mt-0.5" />
            <div className="text-sm text-blue-700">
              <p className="font-medium mb-1">关于 PWA 功能</p>
              <ul className="space-y-1 text-blue-600">
                <li>• 安装后可以像原生应用一样使用</li>
                <li>• 支持离线访问已缓存的内容</li>
                <li>• 自动缓存静态资源以提高加载速度</li>
                <li>• 支持后台更新和推送通知</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
