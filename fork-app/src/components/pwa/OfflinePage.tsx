'use client';

import React, { useState, useEffect } from 'react';
import { WifiOff, RefreshCw, Home, Wrench } from 'lucide-react';
import Link from 'next/link';

export default function OfflinePage() {
  const [isOnline, setIsOnline] = useState(true);
  const [isRetrying, setIsRetrying] = useState(false);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    setIsOnline(navigator.onLine);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleRetry = async () => {
    setIsRetrying(true);
    
    try {
      // 尝试重新加载页面
      await new Promise(resolve => setTimeout(resolve, 1000));
      window.location.reload();
    } catch (error) {
      console.error('重试失败:', error);
    } finally {
      setIsRetrying(false);
    }
  };

  if (isOnline) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 text-center">
        {/* 离线图标 */}
        <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <WifiOff className="w-10 h-10 text-gray-400" />
        </div>

        {/* 标题和描述 */}
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          网络连接已断开
        </h1>
        <p className="text-gray-600 mb-8">
          请检查您的网络连接，然后重试。部分功能在离线状态下仍可使用。
        </p>

        {/* 操作按钮 */}
        <div className="space-y-3 mb-8">
          <button
            onClick={handleRetry}
            disabled={isRetrying}
            className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <RefreshCw className={`w-4 h-4 ${isRetrying ? 'animate-spin' : ''}`} />
            {isRetrying ? '重试中...' : '重新连接'}
          </button>

          <Link
            href="/"
            className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 transition-colors"
          >
            <Home className="w-4 h-4" />
            返回首页
          </Link>
        </div>

        {/* 离线可用功能 */}
        <div className="border-t border-gray-200 pt-6">
          <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center justify-center gap-2">
            <Wrench className="w-4 h-4" />
            离线可用功能
          </h3>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="bg-green-50 text-green-700 px-3 py-2 rounded-lg">
              文本处理
            </div>
            <div className="bg-green-50 text-green-700 px-3 py-2 rounded-lg">
              代码格式化
            </div>
            <div className="bg-green-50 text-green-700 px-3 py-2 rounded-lg">
              单位转换
            </div>
            <div className="bg-green-50 text-green-700 px-3 py-2 rounded-lg">
              颜色工具
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-3">
            这些工具不需要网络连接即可使用
          </p>
        </div>

        {/* 网络状态指示器 */}
        <div className="mt-6 flex items-center justify-center gap-2 text-xs text-gray-500">
          <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-green-500' : 'bg-red-500'}`}></div>
          {isOnline ? '已连接' : '离线状态'}
        </div>
      </div>
    </div>
  );
}
