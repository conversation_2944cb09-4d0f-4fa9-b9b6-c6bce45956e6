'use client'

import { useState, useEffect } from 'react'
import { Search, Sparkles, Zap, Shield, Globe, X } from 'lucide-react'

interface MainHeroProps {
  totalTools: number
  onSearch?: (query: string) => void
}

export default function MainHero({ totalTools, onSearch }: MainHeroProps) {
  const [searchQuery, setSearchQuery] = useState('')

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (onSearch) {
      onSearch(searchQuery.trim())
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchQuery(value)

    // 实时搜索：当输入为空时立即触发搜索重置
    if (onSearch && value.trim() === '') {
      onSearch('')
    }
  }

  const handleClearSearch = () => {
    setSearchQuery('')
    if (onSearch) {
      onSearch('')
    }
  }

  const features = [
    { icon: <Zap className="w-5 h-5" />, text: '快速高效' },
    { icon: <Shield className="w-5 h-5" />, text: '安全可靠' },
    { icon: <Globe className="w-5 h-5" />, text: '免费使用' },
  ]

  return (
    <div className="relative bg-gradient-to-b from-purple-50 via-white to-purple-50/30 py-16 px-4">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-gray-100/50 [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)]" />
      
      <div className="container mx-auto max-w-6xl relative">
        {/* Main Content */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-purple-100 text-purple-700 rounded-full text-sm font-medium mb-6">
            <Sparkles className="w-4 h-4" />
            <span>发现 {totalTools.toLocaleString()}+ 款精选工具</span>
          </div>
          
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-4">
            在线工具
            <span className="text-purple-600">集合</span>
          </h1>
          
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
            为开发者和创作者提供的免费在线工具平台
            <br />
            无需下载，即开即用
          </p>

          {/* Features */}
          <div className="flex items-center justify-center gap-6 mb-12">
            {features.map((feature, index) => (
              <div key={index} className="flex items-center gap-2 text-gray-600">
                <div className="text-purple-600">{feature.icon}</div>
                <span className="text-sm font-medium">{feature.text}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Search Bar */}
        <div className="max-w-2xl mx-auto">
          <form onSubmit={handleSearch} className="relative">
            <div className="relative flex items-center">
              <Search className="absolute left-4 w-5 h-5 text-gray-400 pointer-events-none" />
              <input
                type="text"
                value={searchQuery}
                onChange={handleInputChange}
                placeholder="搜索工具、功能或关键词..."
                className="w-full pl-12 pr-32 py-4 bg-white border border-gray-200 rounded-xl text-gray-900 placeholder-gray-400 focus:outline-none focus:border-purple-400 focus:ring-2 focus:ring-purple-100 shadow-sm transition-all"
              />
              {searchQuery && (
                <button
                  type="button"
                  onClick={handleClearSearch}
                  className="absolute right-24 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
              <button
                type="submit"
                className="absolute right-2 px-6 py-2.5 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors shadow-sm disabled:opacity-50"
              >
                搜索
              </button>
            </div>
          </form>

          {/* Popular Searches */}
          <div className="mt-4 flex items-center justify-center gap-2 flex-wrap">
            <span className="text-sm text-gray-500">热门搜索:</span>
            {['图片抠图', 'UI生成', '格式转换', 'JSON工具', '编码解码'].map((term) => (
              <button
                key={term}
                onClick={() => {
                  setSearchQuery(term)
                  if (onSearch) onSearch(term)
                }}
                className="px-3 py-1 text-sm text-purple-600 hover:text-purple-700 hover:bg-purple-50 rounded-full transition-colors"
              >
                {term}
              </button>
            ))}
          </div>
        </div>

        {/* Stats */}
        <div className="mt-12 grid grid-cols-3 gap-8 max-w-3xl mx-auto">
          <div className="text-center">
            <div className="text-3xl font-bold text-gray-900">{totalTools}+</div>
            <div className="text-sm text-gray-600">在线工具</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-gray-900">100K+</div>
            <div className="text-sm text-gray-600">活跃用户</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-gray-900">5M+</div>
            <div className="text-sm text-gray-600">月使用量</div>
          </div>
        </div>
      </div>
    </div>
  )
}