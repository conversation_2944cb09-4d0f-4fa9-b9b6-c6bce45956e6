'use client'

import { useState } from 'react'
import { Search } from 'lucide-react'
import Link from 'next/link'

interface HeroSectionProps {
  totalServers?: number
  onSearch?: (query: string) => void
}

export default function HeroSection({ totalServers = 16382, onSearch }: HeroSectionProps) {
  const [searchQuery, setSearchQuery] = useState('')

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (onSearch && searchQuery.trim()) {
      onSearch(searchQuery.trim())
    }
  }

  return (
    <div className="relative bg-gradient-to-b from-purple-50 via-white to-purple-50/30 py-16 px-4">
      {/* Announcement Banner */}
      <div className="mb-8 mx-auto max-w-4xl">
        <div className="bg-gradient-to-r from-purple-100 to-purple-50 border border-purple-200 rounded-lg p-4 flex items-center justify-between shadow-sm">
          <p className="text-sm text-gray-700">
            <span className="font-semibold text-purple-600">SiliconFlow:</span>
            {' '}Get GPT-OSS, Deepseek R1&V3, Qwen3 up and running — fully MCP-ready.
            <span className="ml-2">✨</span>
          </p>
          <button className="text-xs text-gray-500 hover:text-gray-700 px-2 py-1 rounded hover:bg-gray-100 transition-colors">
            Dismiss ×
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto text-center">
        {/* Title */}
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
          Find Awesome{' '}
          <span className="text-purple-600">MCP</span>
          {' '}Servers and Clients
        </h1>

        {/* Description */}
        <p className="text-lg text-gray-600 mb-2">
          MCP.so is a third-party MCP Marketplace with{' '}
          <span className="font-semibold text-purple-600">{totalServers.toLocaleString()}</span>
          {' '}MCP Servers collected.
        </p>

        {/* Sponsor Link */}
        <p className="text-sm text-gray-500 mb-8">
          <Link 
            href="https://www.siliconflow.com"
            target="_blank"
            rel="noopener noreferrer"
            className="text-purple-600 hover:text-purple-700 underline"
          >
            SiliconFlow
          </Link>
          : Ready-to-use model APIs for MCP tools — plus $1 bonus for you!
        </p>

        {/* Search Bar */}
        <form onSubmit={handleSearch} className="relative max-w-2xl mx-auto">
          <div className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search with keywords"
              className="w-full px-4 py-3 pl-12 pr-4 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-colors shadow-sm"
            />
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-500" />
          </div>
        </form>

        {/* Submit Button */}
        <div className="mt-8 flex items-center justify-center gap-4">
          <Link
            href="/submit"
            className="inline-flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm text-gray-700 hover:text-purple-600 hover:bg-purple-50 transition-colors shadow-sm"
          >
            <span className="text-lg">📤</span>
            Submit
          </Link>
          
          {/* Language Selector */}
          <button className="inline-flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm text-gray-700 hover:text-purple-600 hover:bg-purple-50 transition-colors shadow-sm">
            <span className="text-lg">🌐</span>
            English
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
      </div>

      {/* Background Gradient Effect */}
      <div className="absolute inset-0 bg-gradient-to-t from-transparent via-purple-100/20 to-transparent pointer-events-none" />
    </div>
  )
}