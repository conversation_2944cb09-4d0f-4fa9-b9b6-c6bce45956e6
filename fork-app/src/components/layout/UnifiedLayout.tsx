'use client';

import React, { ReactNode } from 'react';
import Link from 'next/link';

interface UnifiedLayoutProps {
  children: ReactNode;
  showFooter?: boolean;
}

export function UnifiedLayout({ children, showFooter = true }: UnifiedLayoutProps) {
  return (
    <div className="min-h-screen bg-white text-gray-900">
      {children}
      
      {showFooter && (
        <footer className="bg-gray-50 border-t border-gray-200 mt-16">
          <div className="container mx-auto px-4 py-8">
            <div className="flex flex-col md:flex-row items-center justify-between gap-6">
              {/* Copyright */}
              <div className="text-sm text-gray-600 text-center md:text-left">
                <p>© 2025 在线工具. All rights reserved.</p>
              </div>

              {/* Footer Navigation */}
              <nav className="flex flex-wrap items-center justify-center gap-4">
                <Link href="/" className="text-sm text-gray-600 hover:text-purple-600 transition-colors">
                  首页
                </Link>
                <Link href="/tools" className="text-sm text-gray-600 hover:text-purple-600 transition-colors">
                  工具
                </Link>
                <Link href="/articles" className="text-sm text-gray-600 hover:text-purple-600 transition-colors">
                  文库
                </Link>
                <Link href="/mcp" className="text-sm text-gray-600 hover:text-purple-600 transition-colors">
                  MCP
                </Link>
                <Link href="/about" className="text-sm text-gray-600 hover:text-purple-600 transition-colors">
                  关于
                </Link>
              </nav>

              {/* Legal Links */}
              <div className="flex items-center gap-4">
                <Link href="/privacy" className="text-sm text-gray-600 hover:text-purple-600 transition-colors">
                  隐私政策
                </Link>
                <Link href="/terms" className="text-sm text-gray-600 hover:text-purple-600 transition-colors">
                  服务条款
                </Link>
              </div>
            </div>
          </div>
        </footer>
      )}
    </div>
  );
}