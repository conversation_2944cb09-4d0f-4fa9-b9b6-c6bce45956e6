import Link from 'next/link'

export default function MCPFooter() {
  const footerLinks = [
    { label: 'Explore', href: '/explore' },
    { label: 'Playground', href: '/playground' },
    { label: 'Blog', href: '/posts' },
    { label: 'Cases', href: '/usercases' },
    { label: 'DXT', href: '/dxt' },
    { label: 'Partners', href: '/partners' },
  ]

  const legalLinks = [
    { label: 'Privacy', href: '/privacy-policy' },
    { label: 'Terms', href: '/terms-of-service' },
  ]

  return (
    <footer className="bg-gray-50 border-t border-gray-200">
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row items-center justify-between gap-6">
          {/* Copyright and Recommendation */}
          <div className="text-sm text-gray-600 text-center md:text-left">
            <p>© 2025 MCP.so. All rights reserved.</p>
            <p className="mt-1">
              Recommended: Run GPT-OSS on{' '}
              <Link
                href="https://www.siliconflow.com/models/openai-gpt-oss-120b"
                target="_blank"
                rel="noopener noreferrer"
                className="text-purple-600 hover:text-purple-700 underline"
              >
                SiliconFlow
              </Link>
              .
            </p>
          </div>

          {/* Footer Navigation */}
          <nav className="flex flex-wrap items-center justify-center gap-4">
            {footerLinks.map((link) => (
              <Link
                key={link.label}
                href={link.href}
                className="text-sm text-gray-600 hover:text-purple-600 transition-colors"
              >
                {link.label}
              </Link>
            ))}
          </nav>

          {/* Legal Links */}
          <div className="flex items-center gap-4">
            {legalLinks.map((link) => (
              <Link
                key={link.label}
                href={link.href}
                className="text-sm text-gray-600 hover:text-purple-600 transition-colors"
              >
                {link.label}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </footer>
  )
}