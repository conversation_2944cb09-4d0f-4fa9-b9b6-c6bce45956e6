'use client'

import Link from 'next/link'
import { useState } from 'react'
import { 
  Menu, 
  X, 
  Server, 
  Monitor, 
  Tag, 
  Tags, 
  Rss, 
  Settings,
  Github,
  Twitter,
  MessageCircle,
  Mail,
  Sun,
  Moon
} from 'lucide-react'

export default function MCPHeader() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  const navItems = [
    { icon: Server, label: 'Servers', href: '/servers' },
    { icon: Monitor, label: 'Clients', href: '/clients' },
    { icon: Tag, label: 'Categories', href: '/categories' },
    { icon: Tags, label: 'Tags', href: '/tags' },
    { icon: Rss, label: 'Feed', href: '/feed' },
  ]

  const socialLinks = [
    { icon: Twitter, href: 'https://x.com/chatmcp' },
    { icon: Github, href: 'https://github.com/chatmcp/mcpso/issues' },
    { icon: MessageCircle, href: 'https://discord.gg/RsYPRrnyqg' },
    { icon: Mail, href: 'mailto:<EMAIL>' },
  ]

  return (
    <header className="border-b border-gray-200 bg-white/95 backdrop-blur-sm sticky top-0 z-50 shadow-sm">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-14">
          {/* Logo */}
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center gap-2">
              <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">M</span>
              </div>
              <span className="text-gray-900 font-semibold text-lg">MCP.so</span>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center gap-1">
              {navItems.map((item) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.label}
                    href={item.href}
                    className="flex items-center gap-2 px-3 py-2 rounded-lg text-gray-600 hover:text-purple-600 hover:bg-purple-50 transition-colors"
                  >
                    <Icon className="w-4 h-4" />
                    <span className="text-sm">{item.label}</span>
                  </Link>
                )
              })}
            </nav>
          </div>

          {/* Right Section */}
          <div className="flex items-center gap-4">
            {/* Settings Link */}
            <Link
              href="/my-servers"
              className="hidden md:flex items-center gap-2 px-3 py-2 rounded-lg text-gray-600 hover:text-purple-600 hover:bg-purple-50 transition-colors"
            >
              <Settings className="w-4 h-4" />
              <span className="text-sm">Settings</span>
            </Link>

            {/* Sign In Button */}
            <button className="px-4 py-1.5 bg-purple-600 text-white text-sm font-medium rounded-lg hover:bg-purple-700 transition-colors shadow-sm">
              Sign In
            </button>

            {/* Social Links */}
            <div className="hidden lg:flex items-center gap-2">
              {socialLinks.map((link, index) => {
                const Icon = link.icon
                return (
                  <a
                    key={index}
                    href={link.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-2 text-gray-500 hover:text-purple-600 transition-colors"
                  >
                    <Icon className="w-4 h-4" />
                  </a>
                )
              })}
            </div>

            {/* Theme Toggle */}
            <button className="p-2 text-gray-500 hover:text-purple-600 transition-colors">
              <Sun className="w-4 h-4" />
            </button>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="md:hidden p-2 text-gray-500 hover:text-purple-600 transition-colors"
            >
              {mobileMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <nav className="md:hidden py-4 border-t border-gray-200">
            {navItems.map((item) => {
              const Icon = item.icon
              return (
                <Link
                  key={item.label}
                  href={item.href}
                  className="flex items-center gap-3 px-3 py-2 rounded-lg text-gray-600 hover:text-purple-600 hover:bg-purple-50 transition-colors"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <Icon className="w-4 h-4" />
                  <span>{item.label}</span>
                </Link>
              )
            })}
            <Link
              href="/my-servers"
              className="flex items-center gap-3 px-3 py-2 rounded-lg text-gray-600 hover:text-purple-600 hover:bg-purple-50 transition-colors"
              onClick={() => setMobileMenuOpen(false)}
            >
              <Settings className="w-4 h-4" />
              <span>Settings</span>
            </Link>
          </nav>
        )}
      </div>
    </header>
  )
}