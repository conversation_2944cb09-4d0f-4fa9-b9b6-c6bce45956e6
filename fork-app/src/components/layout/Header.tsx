'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Menu, X, User, LogOut } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { cn } from '@/lib/utils';

interface HeaderProps {
  onSearch?: (query: string) => void;
}

interface User {
  email: string;
  name: string;
  avatar: string;
}

export function Header({ onSearch }: HeaderProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [showUserMenu, setShowUserMenu] = useState(false);

  useEffect(() => {
    // 从 localStorage 中获取用户信息
    try {
      const savedUser = localStorage.getItem('user');
      if (savedUser) {
        setUser(JSON.parse(savedUser));
      }
    } catch (error) {
      console.error('Failed to parse user data:', error);
    }
  }, []);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim() && onSearch) {
      onSearch(searchQuery.trim());
    }
  };

  const handleLogout = () => {
    setUser(null);
    localStorage.removeItem('user');
    setShowUserMenu(false);
    window.location.href = '/';
  };

  const navigation = [
    { name: '工具', href: '/' },
    { name: '文库', href: '/articles' },
    { name: '码库', href: '/snippets' },
    { name: '软件', href: '/software' },
    { name: '网址', href: '/bookmarks' },
    { name: '话题', href: '/topics' },
    { name: '小摊', href: '/shop' }
  ];

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      {/* Top Bar */}
      <div className="bg-muted/30 px-4 py-2 text-center text-sm text-muted-foreground">
        在线工具
      </div>

      {/* Main Header */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="h-8 w-8 rounded bg-green-600 flex items-center justify-center">
              <span className="text-white font-bold">工</span>
            </div>
            <span className="text-xl font-bold text-green-600">在线工具</span>
          </Link>

          {/* Search Bar - Desktop */}
          <div className="hidden md:flex flex-1 max-w-md mx-8">
            <form onSubmit={handleSearch} className="flex w-full">
              <Input
                type="text"
                placeholder="搜索其实很简单"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="rounded-r-none"
              />
              <Button 
                type="submit" 
                className="rounded-l-none px-6 bg-green-600 hover:bg-green-700"
              >
                搜索
              </Button>
            </form>
            <div className="ml-4 flex items-center space-x-2 text-sm">
              <Link href="/search?q=抠图" className="text-blue-600 hover:underline">抠图</Link>
              <Link href="/search?q=反混淆" className="text-blue-600 hover:underline">反混淆</Link>
              <Link href="/search?q=地图坐标转换" className="text-blue-600 hover:underline">地图坐标转换</Link>
            </div>
          </div>

          {/* Right Actions */}
          <div className="flex items-center space-x-4">
            {/* Ranking Link */}
            <Link 
              href="/ranking" 
              className="hidden md:flex items-center text-sm text-muted-foreground hover:text-foreground"
            >
              <span>📊</span>
              <span className="ml-1">排行榜</span>
            </Link>

            {/* Language Selector */}
            <Button variant="ghost" size="sm" className="hidden md:inline-flex">
              语言 ▼
            </Button>

            {/* Auth Links / User Menu */}
            {user ? (
              <div className="relative">
                <button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="flex items-center space-x-2 text-sm font-medium text-gray-700 hover:text-gray-900"
                >
                  <img
                    src={user.avatar}
                    alt={user.name}
                    className="w-8 h-8 rounded-full"
                  />
                  <span className="hidden md:inline">{user.name}</span>
                </button>
                
                {showUserMenu && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50">
                    <div className="py-1">
                      <div className="px-4 py-2 text-sm text-gray-500 border-b border-gray-100">
                        {user.email}
                      </div>
                      <Link
                        href="/profile"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <User className="w-4 h-4 mr-2" />
                        个人资料
                      </Link>
                      <Link
                        href="/favorites"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <span className="w-4 h-4 mr-2">❤️</span>
                        我的收藏
                      </Link>
                      <button
                        onClick={handleLogout}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        <LogOut className="w-4 h-4 mr-2" />
                        退出登录
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <>
                <Link href="/login">
                  <Button variant="ghost" size="sm">登录</Button>
                </Link>
                <Link href="/register">
                  <Button variant="ghost" size="sm">开放注册</Button>
                </Link>
              </>
            )}

            {/* Mobile Menu Toggle */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? <X /> : <Menu />}
            </Button>
          </div>
        </div>

        {/* Mobile Search */}
        <div className="md:hidden mt-4">
          <form onSubmit={handleSearch} className="flex">
            <Input
              type="text"
              placeholder="搜索其实很简单"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="rounded-r-none"
            />
            <Button 
              type="submit" 
              className="rounded-l-none px-6 bg-green-600 hover:bg-green-700"
            >
              搜索
            </Button>
          </form>
        </div>

        {/* Mobile Navigation */}
        <div className={cn(
          'md:hidden mt-4 border-t pt-4',
          isMobileMenuOpen ? 'block' : 'hidden'
        )}>
          <nav className="grid grid-cols-2 gap-2">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="px-3 py-2 text-sm text-center rounded-md hover:bg-muted"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {item.name}
              </Link>
            ))}
          </nav>
        </div>
      </div>

      {/* Desktop Navigation */}
      <div className="hidden md:block border-t">
        <div className="container mx-auto px-4 py-2">
          <nav className="flex items-center space-x-6">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                {item.name}
              </Link>
            ))}
            <Link
              href="/feedback"
              className="ml-auto text-sm text-blue-600 hover:underline"
            >
              反馈
            </Link>
          </nav>
        </div>
      </div>
    </header>
  );
}