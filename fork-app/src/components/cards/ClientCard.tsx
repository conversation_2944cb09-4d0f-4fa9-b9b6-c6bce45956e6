import Link from 'next/link'
import Image from 'next/image'
import { ExternalLink } from 'lucide-react'

interface ClientCardProps {
  id: string
  name: string
  description: string
  author: string
  imageUrl?: string
  avatar?: string
  isSponsored?: boolean
  verified?: boolean
}

export default function ClientCard({
  id,
  name,
  description,
  author,
  imageUrl,
  avatar,
  isSponsored = false,
  verified = false
}: ClientCardProps) {
  // 生成默认头像（首字母）
  const getDefaultAvatar = (name: string) => {
    const firstLetter = name.charAt(0).toUpperCase()
    return (
      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-700 rounded-lg flex items-center justify-center text-white font-bold text-lg">
        {firstLetter}
      </div>
    )
  }

  return (
    <Link
      href={`/client/${id}/${author}`}
      className="group relative block bg-white border border-gray-200 rounded-lg p-4 hover:border-blue-400 hover:shadow-lg transition-all duration-200 shadow-sm"
    >
      {/* Sponsored Badge */}
      {isSponsored && (
        <div className="absolute top-2 right-2 z-10">
          <span className="px-2 py-1 bg-gradient-to-r from-purple-600 to-purple-700 text-white text-xs font-medium rounded">
            Sponsor
          </span>
        </div>
      )}

      <div className="flex gap-3">
        {/* Avatar/Icon */}
        <div className="flex-shrink-0">
          {imageUrl ? (
            <Image
              src={imageUrl}
              alt={name}
              width={48}
              height={48}
              className="rounded-lg"
            />
          ) : avatar ? (
            <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center text-2xl">
              {avatar}
            </div>
          ) : (
            getDefaultAvatar(name)
          )}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors flex items-center gap-1">
              <span className="truncate">{name}</span>
              {verified && (
                <svg className="w-4 h-4 text-blue-400 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              )}
            </h3>
            <ExternalLink className="w-3 h-3 text-gray-400 group-hover:text-blue-600 flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity" />
          </div>
          
          <p className="mt-1 text-sm text-gray-600 line-clamp-2">
            {description}
          </p>
        </div>
      </div>
    </Link>
  )
}