'use client';

import React, { useState, useCallback } from 'react';
import { Upload, Download, Image, Loader2, AlertCircle, Settings, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { ImageMagickConverter, IMAGE_FORMATS, ProcessResult, ImageProcessOptions } from '@/lib/wasm/converters/ImageMagickConverter';

interface ImageProcessorProps {
  className?: string;
}

export function ImageProcessor({ className }: ImageProcessorProps) {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [results, setResults] = useState<ProcessResult[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [converter] = useState(() => ImageMagickConverter.getInstance());

  // 处理选项
  const [options, setOptions] = useState<ImageProcessOptions>({
    format: '',
    quality: 85,
    resize: 'fit'
  });

  // 处理文件选择
  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || []);
    const validFiles = selectedFiles.filter(file => {
      const ext = file.name.split('.').pop()?.toLowerCase() || '';
      return IMAGE_FORMATS.some(format => format.extension === ext);
    });
    
    if (validFiles.length !== selectedFiles.length) {
      setError('部分文件格式不支持，已自动过滤');
      setTimeout(() => setError(null), 3000);
    }
    
    setFiles(validFiles);
    setResults([]);
  }, []);

  // 处理拖放
  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const droppedFiles = Array.from(event.dataTransfer.files);
    const validFiles = droppedFiles.filter(file => {
      const ext = file.name.split('.').pop()?.toLowerCase() || '';
      return IMAGE_FORMATS.some(format => format.extension === ext);
    });
    
    if (validFiles.length !== droppedFiles.length) {
      setError('部分文件格式不支持，已自动过滤');
      setTimeout(() => setError(null), 3000);
    }
    
    setFiles(validFiles);
    setResults([]);
  }, []);

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  // 开始处理
  const handleProcess = async () => {
    if (files.length === 0) return;

    setIsProcessing(true);
    setProgress(0);
    setError(null);
    setResults([]);

    try {
      // 初始化转换器
      if (!converter) {
        setIsInitializing(true);
        await converter.initialize((progress) => {
          setProgress(progress * 0.2); // 初始化占20%进度
        });
        setIsInitializing(false);
      }

      // 批量处理图像
      const processResults = await converter.processBatch(
        files,
        options,
        (current, total) => {
          setProgress(20 + (current / total) * 80); // 处理占80%进度
        }
      );

      setResults(processResults);
      setProgress(100);
    } catch (err) {
      console.error('处理失败:', err);
      setError(err instanceof Error ? err.message : '处理失败，请重试');
    } finally {
      setIsProcessing(false);
      setIsInitializing(false);
    }
  };

  // 下载结果
  const handleDownload = (result: ProcessResult) => {
    converter.downloadResult(result);
  };

  // 下载所有结果
  const handleDownloadAll = () => {
    results.forEach(result => {
      converter.downloadResult(result);
    });
  };

  // 重置
  const handleReset = () => {
    setFiles([]);
    setResults([]);
    setProgress(0);
    setError(null);
    setOptions({
      format: '',
      quality: 85,
      resize: 'fit'
    });
  };

  // 更新选项
  const updateOption = (key: keyof ImageProcessOptions, value: any) => {
    setOptions(prev => ({ ...prev, [key]: value }));
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle>图像处理工具</CardTitle>
          <CardDescription>
            使用ImageMagick WASM在浏览器中处理图像，支持格式转换、调整大小、添加滤镜等
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* 文件上传区域 */}
          <div
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-green-500 transition-colors"
          >
            <Image className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p className="text-lg font-medium text-gray-700 mb-2">
              拖放图片到这里，或点击选择
            </p>
            <p className="text-sm text-gray-500 mb-4">
              支持格式：JPG、PNG、GIF、BMP、WebP、TIFF等
            </p>
            <input
              type="file"
              multiple
              accept="image/*"
              onChange={handleFileSelect}
              className="hidden"
              id="image-input"
            />
            <label htmlFor="image-input">
              <Button as="span" className="cursor-pointer">
                <Upload className="w-4 h-4 mr-2" />
                选择图片
              </Button>
            </label>
          </div>

          {/* 已选择的文件列表 */}
          {files.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-700 mb-2">
                已选择 {files.length} 个图片
              </h4>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {files.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm text-gray-600 truncate">
                      {file.name}
                    </span>
                    <span className="text-xs text-gray-500">
                      {(file.size / 1024).toFixed(2)} KB
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 基础设置 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                输出格式（留空保持原格式）
              </label>
              <select
                value={options.format || ''}
                onChange={(e) => updateOption('format', e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                disabled={isProcessing}
              >
                <option value="">保持原格式</option>
                {IMAGE_FORMATS.map(format => (
                  <option key={format.extension} value={format.extension}>
                    {format.name} (.{format.extension})
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                图片质量 ({options.quality}%)
              </label>
              <input
                type="range"
                min="1"
                max="100"
                value={options.quality}
                onChange={(e) => updateOption('quality', parseInt(e.target.value))}
                className="w-full"
                disabled={isProcessing}
              />
            </div>
          </div>

          {/* 高级设置按钮 */}
          <Button
            variant="outline"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="w-full"
          >
            <Settings className="w-4 h-4 mr-2" />
            {showAdvanced ? '隐藏' : '显示'}高级设置
          </Button>

          {/* 高级设置 */}
          {showAdvanced && (
            <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    宽度（像素，留空自动）
                  </label>
                  <input
                    type="number"
                    placeholder="自动"
                    value={options.width || ''}
                    onChange={(e) => updateOption('width', e.target.value ? parseInt(e.target.value) : undefined)}
                    className="w-full p-2 border border-gray-300 rounded"
                    disabled={isProcessing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    高度（像素，留空自动）
                  </label>
                  <input
                    type="number"
                    placeholder="自动"
                    value={options.height || ''}
                    onChange={(e) => updateOption('height', e.target.value ? parseInt(e.target.value) : undefined)}
                    className="w-full p-2 border border-gray-300 rounded"
                    disabled={isProcessing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    调整模式
                  </label>
                  <select
                    value={options.resize || 'fit'}
                    onChange={(e) => updateOption('resize', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded"
                    disabled={isProcessing}
                  >
                    <option value="fit">适应（保持比例）</option>
                    <option value="fill">填充（拉伸）</option>
                    <option value="cover">覆盖（裁剪）</option>
                    <option value="contain">包含（添加边距）</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    旋转角度
                  </label>
                  <select
                    value={options.rotate || 0}
                    onChange={(e) => updateOption('rotate', parseInt(e.target.value))}
                    className="w-full p-2 border border-gray-300 rounded"
                    disabled={isProcessing}
                  >
                    <option value="0">不旋转</option>
                    <option value="90">90°</option>
                    <option value="180">180°</option>
                    <option value="270">270°</option>
                  </select>
                </div>
              </div>

              <div className="flex flex-wrap gap-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={options.grayscale || false}
                    onChange={(e) => updateOption('grayscale', e.target.checked)}
                    className="mr-2"
                    disabled={isProcessing}
                  />
                  <span className="text-sm text-gray-700">转为灰度</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={options.flip === 'horizontal'}
                    onChange={(e) => updateOption('flip', e.target.checked ? 'horizontal' : undefined)}
                    className="mr-2"
                    disabled={isProcessing}
                  />
                  <span className="text-sm text-gray-700">水平翻转</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={options.flip === 'vertical'}
                    onChange={(e) => updateOption('flip', e.target.checked ? 'vertical' : undefined)}
                    className="mr-2"
                    disabled={isProcessing}
                  />
                  <span className="text-sm text-gray-700">垂直翻转</span>
                </label>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-center space-x-4">
            <Button
              onClick={handleProcess}
              disabled={files.length === 0 || isProcessing}
              className="bg-green-600 hover:bg-green-700"
            >
              {isInitializing ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  初始化中...
                </>
              ) : isProcessing ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  处理中 {progress}%
                </>
              ) : (
                <>
                  <Image className="w-4 h-4 mr-2" />
                  开始处理
                </>
              )}
            </Button>
            
            {results.length > 0 && (
              <Button
                onClick={handleDownloadAll}
                variant="outline"
              >
                <Download className="w-4 h-4 mr-2" />
                下载全部
              </Button>
            )}
            
            <Button
              onClick={handleReset}
              variant="outline"
              disabled={isProcessing}
            >
              重置
            </Button>
          </div>

          {/* 进度条 */}
          {isProcessing && (
            <div className="w-full">
              <div className="flex justify-between text-sm text-gray-600 mb-1">
                <span>处理进度</span>
                <span>{progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          )}

          {/* 错误提示 */}
          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg flex items-start">
              <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 mr-2" />
              <p className="text-sm text-red-800">{error}</p>
            </div>
          )}

          {/* 处理结果 */}
          {results.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-700 mb-2 flex items-center">
                <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                处理完成
              </h4>
              <div className="space-y-2">
                {results.map((result, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div>
                      <span className="text-sm text-gray-700">
                        {result.filename}
                      </span>
                      {result.width && result.height && (
                        <span className="text-xs text-gray-500 ml-2">
                          ({result.width}x{result.height}px, {(result.size! / 1024).toFixed(2)}KB)
                        </span>
                      )}
                    </div>
                    <Button
                      size="sm"
                      onClick={() => handleDownload(result)}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <Download className="w-3 h-3 mr-1" />
                      下载
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 使用说明 */}
          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">💡 使用说明</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 支持批量处理，一次可选择多个图片</li>
              <li>• 所有处理在本地完成，图片不会上传到服务器</li>
              <li>• 首次使用需要加载WASM模块（约8MB）</li>
              <li>• 支持格式转换、调整大小、旋转、翻转等操作</li>
              <li>• 可在高级设置中调整更多参数</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}