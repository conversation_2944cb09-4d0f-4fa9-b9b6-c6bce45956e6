'use client'

import Link from 'next/link'
import { ArrowRight, Star, Users, Loader2 } from 'lucide-react'
import { Tool } from '@/types/tool'
import { useState } from 'react'

interface ToolCardNewProps {
  tool: Tool
  featured?: boolean
}

export default function ToolCardNew({ tool, featured = false }: ToolCardNewProps) {
  const [isNavigating, setIsNavigating] = useState(false)

  const handleClick = () => {
    setIsNavigating(true)
  }

  return (
    <Link
      href={`/tool/${tool.id}`}
      onClick={handleClick}
      className="group relative block bg-white border border-gray-200 rounded-xl p-4 md:p-5 hover:border-purple-400 hover:shadow-lg transition-all duration-200 shadow-sm touch-manipulation active:scale-[0.98]"
    >
      {/* Loading Overlay */}
      {isNavigating && (
        <div className="absolute inset-0 bg-white/80 backdrop-blur-sm rounded-xl flex items-center justify-center z-10">
          <Loader2 className="w-6 h-6 animate-spin text-purple-600" />
        </div>
      )}

      {/* Featured Badge */}
      {featured && (
        <div className="absolute -top-2 -right-2 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs font-medium px-3 py-1.5 rounded-full shadow-md z-20">
          推荐
        </div>
      )}

      {/* Card Content */}
      <div className="flex items-start gap-3 md:gap-4">
        {/* Icon */}
        <div className="flex-shrink-0 w-12 h-12 md:w-14 md:h-14 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl flex items-center justify-center text-2xl md:text-3xl group-hover:scale-110 transition-transform">
          {tool.icon}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-gray-900 mb-1 md:mb-2 group-hover:text-purple-600 transition-colors text-base md:text-lg leading-tight">
            {tool.name}
          </h3>
          <p className="text-sm md:text-base text-gray-600 line-clamp-2 mb-3 md:mb-4 leading-relaxed">
            {tool.description}
          </p>

          {/* Tags */}
          <div className="flex flex-wrap gap-1.5 md:gap-2 mb-3 md:mb-4">
            {tool.tags.slice(0, 2).map((tag, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2 md:px-2.5 py-0.5 md:py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600"
              >
                {tag}
              </span>
            ))}
            {tool.tags.length > 2 && (
              <span className="hidden md:inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                {tool.tags[2]}
              </span>
            )}
          </div>

          {/* Stats */}
          <div className="flex items-center gap-3 md:gap-4 text-xs md:text-sm text-gray-500">
            {tool.usageCount && (
              <div className="flex items-center gap-1">
                <Users className="w-3 h-3 md:w-4 md:h-4" />
                <span>{tool.usageCount.toLocaleString()}</span>
              </div>
            )}
            {tool.rating && (
              <div className="flex items-center gap-1">
                <Star className="w-3 h-3 md:w-4 md:h-4 fill-yellow-400 text-yellow-400" />
                <span>{tool.rating}</span>
              </div>
            )}
            <div className="ml-auto opacity-0 group-hover:opacity-100 md:opacity-100 transition-opacity">
              <ArrowRight className="w-4 h-4 md:w-5 md:h-5 text-purple-600" />
            </div>
          </div>
        </div>
      </div>
    </Link>
  )
}