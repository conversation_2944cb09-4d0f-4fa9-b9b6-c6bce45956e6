'use client';

import React, { useState } from 'react';
import { Wand2, Copy, Download, Eye, Code, RotateCcw, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';

interface UIGeneratorProps {
  className?: string;
}

export function UIGenerator({ className }: UIGeneratorProps) {
  const [prompt, setPrompt] = useState('');
  const [generatedCode, setGeneratedCode] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [activeTab, setActiveTab] = useState<'code' | 'preview'>('code');

  const handleGenerate = async () => {
    if (!prompt.trim()) return;
    
    setIsGenerating(true);
    
    // 模拟API调用
    setTimeout(() => {
      const mockCode = generateMockCode(prompt);
      setGeneratedCode(mockCode);
      setIsGenerating(false);
    }, 2000);
  };

  const copyCode = () => {
    navigator.clipboard.writeText(generatedCode);
  };

  const downloadCode = () => {
    const blob = new Blob([generatedCode], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'generated-ui.html';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const resetTool = () => {
    setPrompt('');
    setGeneratedCode('');
    setActiveTab('code');
  };

  const samplePrompts = [
    '一个现代的登录表单，包含邮箱和密码输入框',
    '一个响应式的卡片布局，展示产品信息',
    '一个简洁的导航栏，包含logo和菜单项',
    '一个带有图片和文字的英雄区域',
    '一个价格表格，包含三个套餐选项',
  ];

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle>AI UI代码生成器</CardTitle>
          <CardDescription>
            描述您想要的界面，AI将为您生成对应的Tailwind CSS代码
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* 输入区域 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              描述您的UI需求
            </label>
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="请详细描述您想要的界面样式和功能，例如：一个包含标题、描述文本和按钮的卡片组件，使用蓝色主题..."
              className="w-full h-32 p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            />
            <div className="text-sm text-gray-500 mt-1">
              {prompt.length}/500 字符
            </div>
          </div>

          {/* 示例提示 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              💡 快速开始
            </label>
            <div className="flex flex-wrap gap-2">
              {samplePrompts.map((sample, index) => (
                <button
                  key={index}
                  onClick={() => setPrompt(sample)}
                  className="text-sm px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-gray-700 transition-colors"
                >
                  {sample}
                </button>
              ))}
            </div>
          </div>

          {/* 生成按钮 */}
          <div className="flex justify-center">
            <Button
              onClick={handleGenerate}
              disabled={!prompt.trim() || isGenerating}
              className="bg-green-600 hover:bg-green-700 px-8"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  生成中...
                </>
              ) : (
                <>
                  <Wand2 className="w-4 h-4 mr-2" />
                  生成代码
                </>
              )}
            </Button>
          </div>

          {/* 结果区域 */}
          {(generatedCode || isGenerating) && (
            <div className="border rounded-lg overflow-hidden">
              <div className="bg-gray-50 border-b px-4 py-3 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="text-sm font-medium text-gray-700">生成结果</span>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setActiveTab('code')}
                      className={`px-3 py-1 text-sm rounded transition-colors ${
                        activeTab === 'code'
                          ? 'bg-white text-gray-900 shadow-sm'
                          : 'text-gray-600 hover:text-gray-900'
                      }`}
                    >
                      <Code className="w-4 h-4 inline mr-1" />
                      代码
                    </button>
                    <button
                      onClick={() => setActiveTab('preview')}
                      className={`px-3 py-1 text-sm rounded transition-colors ${
                        activeTab === 'preview'
                          ? 'bg-white text-gray-900 shadow-sm'
                          : 'text-gray-600 hover:text-gray-900'
                      }`}
                    >
                      <Eye className="w-4 h-4 inline mr-1" />
                      预览
                    </button>
                  </div>
                </div>
                
                {generatedCode && !isGenerating && (
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline" onClick={copyCode}>
                      <Copy className="w-4 h-4 mr-1" />
                      复制
                    </Button>
                    <Button size="sm" variant="outline" onClick={downloadCode}>
                      <Download className="w-4 h-4 mr-1" />
                      下载
                    </Button>
                  </div>
                )}
              </div>
              
              <div className="p-4">
                {isGenerating ? (
                  <div className="h-64 flex items-center justify-center">
                    <div className="text-center">
                      <Loader2 className="mx-auto h-8 w-8 animate-spin text-green-600 mb-2" />
                      <p className="text-sm text-gray-600">AI正在根据您的描述生成代码...</p>
                    </div>
                  </div>
                ) : generatedCode ? (
                  <div className="h-64 overflow-auto">
                    {activeTab === 'code' ? (
                      <pre className="text-sm bg-gray-900 text-gray-100 p-4 rounded overflow-x-auto">
                        <code>{generatedCode}</code>
                      </pre>
                    ) : (
                      <div className="border rounded p-4 bg-white h-full overflow-auto">
                        <div dangerouslySetInnerHTML={{ __html: generatedCode }} />
                      </div>
                    )}
                  </div>
                ) : null}
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          {generatedCode && !isGenerating && (
            <div className="flex justify-center">
              <Button
                onClick={resetTool}
                variant="ghost"
                className="text-gray-600 hover:text-gray-800"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                重新开始
              </Button>
            </div>
          )}

          {/* 使用说明 */}
          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">💡 使用技巧</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 描述越详细，生成的代码越符合需求</li>
              <li>• 可以指定颜色、大小、布局等具体要求</li>
              <li>• 生成的代码基于Tailwind CSS框架</li>
              <li>• 支持响应式设计和现代UI组件</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// 生成模拟代码的函数
function generateMockCode(prompt: string): string {
  // 这里应该调用实际的AI API，现在使用模拟代码
  if (prompt.includes('登录') || prompt.includes('表单')) {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录表单</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <h2 class="text-2xl font-bold text-center text-gray-900 mb-6">登录</h2>
        <form class="space-y-6">
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                    邮箱地址
                </label>
                <input
                    type="email"
                    id="email"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入您的邮箱"
                >
            </div>
            <div>
                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                    密码
                </label>
                <input
                    type="password"
                    id="password"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入您的密码"
                >
            </div>
            <button
                type="submit"
                class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
                登录
            </button>
        </form>
    </div>
</body>
</html>`;
  }

  if (prompt.includes('卡片') || prompt.includes('产品')) {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品卡片</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-sm mx-auto bg-white rounded-lg shadow-md overflow-hidden">
        <img
            src="https://via.placeholder.com/300x200"
            alt="产品图片"
            class="w-full h-48 object-cover"
        >
        <div class="p-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">产品标题</h3>
            <p class="text-gray-600 mb-4">
                这是一个产品的描述文本，介绍产品的特点和优势。
            </p>
            <div class="flex items-center justify-between">
                <span class="text-2xl font-bold text-green-600">¥299</span>
                <button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    购买
                </button>
            </div>
        </div>
    </div>
</body>
</html>`;
  }

  // 默认生成一个简单的组件
  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成的UI</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">AI生成的组件</h2>
        <p class="text-gray-600 mb-4">
            根据您的描述："${prompt}"，这是一个基础的UI组件。
        </p>
        <button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
            点击按钮
        </button>
    </div>
</body>
</html>`;
}