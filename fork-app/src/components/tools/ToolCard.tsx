import React from 'react';
import Link from 'next/link';
import { Heart, Star } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Tool } from '@/types/tool';
import { CATEGORY_LABELS } from '@/types/tool';

interface ToolCardProps {
  tool: Tool;
  onFavorite?: (toolId: string) => void;
  isFavorited?: boolean;
}

export function ToolCard({ tool, onFavorite, isFavorited }: ToolCardProps) {
  const handleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onFavorite?.(tool.id);
  };

  return (
    <Link href={tool.url}>
      <Card className="group h-full hover:shadow-lg transition-all duration-200 hover:border-green-200 cursor-pointer">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              <div className="text-2xl">{tool.icon}</div>
              <div className="flex-1">
                <CardTitle className="text-base group-hover:text-green-600 transition-colors line-clamp-1">
                  {tool.name}
                </CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                    {CATEGORY_LABELS[tool.category]}
                  </span>
                  {tool.featured && (
                    <Star className="w-3 h-3 text-yellow-500 fill-current" />
                  )}
                </div>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={handleFavorite}
            >
              <Heart className={`w-4 h-4 ${isFavorited ? 'fill-red-500 text-red-500' : ''}`} />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          <CardDescription className="text-sm line-clamp-2 mb-3">
            {tool.description}
          </CardDescription>
        </CardContent>

        <CardFooter className="pt-0 flex items-center justify-between">
          <div className="flex items-center space-x-4 text-xs text-muted-foreground">
            {tool.usageCount && (
              <span>使用 {tool.usageCount.toLocaleString()} 次</span>
            )}
          </div>
          <Link 
            href={tool.url}
            className="text-sm text-green-600 hover:text-green-700 font-medium"
            onClick={(e) => e.stopPropagation()}
          >
            进入 →
          </Link>
        </CardFooter>
      </Card>
    </Link>
  );
}