'use client';

import React, { useState, useCallback } from 'react';
import { Upload, Download, RotateCcw, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';

interface ImageCutoutProps {
  className?: string;
}

export function ImageCutout({ className }: ImageCutoutProps) {
  const [image, setImage] = useState<string | null>(null);
  const [processedImage, setProcessedImage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [cutoutMode, setCutoutMode] = useState<'general' | 'portrait' | 'anime' | 'advanced'>('general');
  const [backgroundColor, setBackgroundColor] = useState<'transparent' | 'white' | 'red' | 'blue' | 'gray'>('transparent');
  
  const handleImageUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImage(e.target?.result as string);
        setProcessedImage(null);
      };
      reader.readAsDataURL(file);
    }
  }, []);

  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImage(e.target?.result as string);
        setProcessedImage(null);
      };
      reader.readAsDataURL(file);
    }
  }, []);

  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  }, []);

  const processCutout = async () => {
    if (!image) return;
    
    setIsProcessing(true);
    
    // 模拟API调用处理时间
    setTimeout(() => {
      // 这里应该调用实际的抠图API
      // 现在只是模拟返回原图
      setProcessedImage(image);
      setIsProcessing(false);
    }, 3000);
  };

  const downloadImage = () => {
    if (!processedImage) return;
    
    const link = document.createElement('a');
    link.href = processedImage;
    link.download = 'cutout-result.png';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const resetTool = () => {
    setImage(null);
    setProcessedImage(null);
    setIsProcessing(false);
  };

  const backgroundColors = {
    transparent: { label: '透明背景', value: 'transparent', preview: 'bg-gray-200' },
    white: { label: '白色背景', value: 'white', preview: 'bg-white' },
    red: { label: '红色背景', value: 'red', preview: 'bg-red-500' },
    blue: { label: '蓝色背景', value: 'blue', preview: 'bg-blue-500' },
    gray: { label: '灰色背景', value: 'gray', preview: 'bg-gray-500' },
  };

  const cutoutModes = {
    general: { label: '通用抠图', description: '适用于大部分图片' },
    portrait: { label: '人像抠图', description: '专为人物照片优化' },
    anime: { label: '动漫抠图', description: '适合动漫、卡通图片' },
    advanced: { label: '高级通用', description: '更精确的抠图效果' },
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle>智能抠图工具</CardTitle>
          <CardDescription>
            上传图片，AI将自动识别主体并移除背景。支持多种抠图模式和背景选择。
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* 图片上传区域 */}
          {!image && (
            <div
              className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-green-400 transition-colors cursor-pointer"
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onClick={() => document.getElementById('image-upload')?.click()}
            >
              <div className="space-y-4">
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <div>
                  <p className="text-lg font-medium text-gray-900 mb-2">上传图片进行抠图</p>
                  <p className="text-sm text-gray-500">
                    粘贴、拖拽图片到这里，或者点击选择图片上传
                  </p>
                  <p className="text-xs text-gray-400 mt-2">
                    支持 JPG、PNG、WebP 格式，最大 10MB
                  </p>
                </div>
                <input
                  id="image-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
              </div>
            </div>
          )}

          {/* 图片预览和设置 */}
          {image && (
            <div className="space-y-6">
              {/* 抠图模式选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">抠图模式</label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {Object.entries(cutoutModes).map(([key, mode]) => (
                    <button
                      key={key}
                      onClick={() => setCutoutMode(key as typeof cutoutMode)}
                      className={`p-3 text-left rounded-lg border-2 transition-all ${
                        cutoutMode === key
                          ? 'border-green-500 bg-green-50 text-green-800'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="font-medium text-sm">{mode.label}</div>
                      <div className="text-xs text-gray-500 mt-1">{mode.description}</div>
                    </button>
                  ))}
                </div>
              </div>

              {/* 背景选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">背景颜色</label>
                <div className="flex flex-wrap gap-3">
                  {Object.entries(backgroundColors).map(([key, bg]) => (
                    <button
                      key={key}
                      onClick={() => setBackgroundColor(key as typeof backgroundColor)}
                      className={`flex items-center space-x-2 px-3 py-2 rounded-lg border-2 transition-all ${
                        backgroundColor === key
                          ? 'border-green-500 bg-green-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className={`w-4 h-4 rounded border ${bg.preview}`}></div>
                      <span className="text-sm">{bg.label}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* 图片预览区域 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* 原图 */}
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-3">原图</h3>
                  <div className="relative border rounded-lg overflow-hidden bg-gray-50">
                    <img
                      src={image}
                      alt="Original"
                      className="w-full h-64 object-contain"
                    />
                  </div>
                </div>

                {/* 处理结果 */}
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-3">抠图结果</h3>
                  <div className="relative border rounded-lg overflow-hidden bg-gray-50">
                    {isProcessing ? (
                      <div className="w-full h-64 flex items-center justify-center">
                        <div className="text-center">
                          <Loader2 className="mx-auto h-8 w-8 animate-spin text-green-600 mb-2" />
                          <p className="text-sm text-gray-600">AI正在处理图片...</p>
                        </div>
                      </div>
                    ) : processedImage ? (
                      <div className={`w-full h-64 flex items-center justify-center ${
                        backgroundColor === 'transparent' ? 'bg-checker-pattern' :
                        backgroundColor === 'white' ? 'bg-white' :
                        backgroundColor === 'red' ? 'bg-red-500' :
                        backgroundColor === 'blue' ? 'bg-blue-500' :
                        'bg-gray-500'
                      }`}>
                        <img
                          src={processedImage}
                          alt="Processed"
                          className="max-w-full max-h-full object-contain"
                        />
                      </div>
                    ) : (
                      <div className="w-full h-64 flex items-center justify-center text-gray-400">
                        <div className="text-center">
                          <div className="text-4xl mb-2">🖼️</div>
                          <p className="text-sm">处理结果将在这里显示</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex flex-wrap gap-3 justify-center">
                <Button
                  onClick={processCutout}
                  disabled={isProcessing}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      处理中...
                    </>
                  ) : (
                    <>
                      <span className="mr-2">✨</span>
                      开始抠图
                    </>
                  )}
                </Button>
                
                {processedImage && !isProcessing && (
                  <Button
                    onClick={downloadImage}
                    variant="outline"
                    className="border-green-600 text-green-600 hover:bg-green-50"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    下载图片
                  </Button>
                )}
                
                <Button
                  onClick={resetTool}
                  variant="ghost"
                  className="text-gray-600 hover:text-gray-800"
                >
                  <RotateCcw className="w-4 h-4 mr-2" />
                  重新开始
                </Button>
              </div>
            </div>
          )}

          {/* 使用说明 */}
          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">💡 使用提示</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 人像抠图模式对人物照片效果最佳</li>
              <li>• 通用模式适合物品、风景等各类图片</li>
              <li>• 图片清晰度越高，抠图效果越好</li>
              <li>• 支持透明背景，便于后续设计使用</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}