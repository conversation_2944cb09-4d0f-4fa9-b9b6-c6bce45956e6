'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { ToolCategory, CATEGORY_LABELS } from '@/types/tool';
import { Loader2 } from 'lucide-react';

interface CategoryNavProps {
  activeCategory: ToolCategory | 'all';
  onCategoryChange: (category: ToolCategory | 'all') => void;
  isLoading?: boolean;
}

const CATEGORY_ICONS: Record<ToolCategory, string> = {
  [ToolCategory.TEXT]: '📝',
  [ToolCategory.DOCUMENT]: '📄',
  [ToolCategory.IMAGE]: '🖼️',
  [ToolCategory.AUDIO]: '🎵',
  [ToolCategory.VIDEO]: '🎬',
  [ToolCategory.DEVELOPER]: '⚡',
  [ToolCategory.UTILITY]: '🔧',
  [ToolCategory.DESIGN]: '🎨',
  [ToolCategory.CRYPTO]: '🔐',
  [ToolCategory.CONVERTER]: '🔄'
};

export function CategoryNav({ activeCategory, onCategoryChange, isLoading = false }: CategoryNavProps) {
  const categories = [
    { key: 'all' as const, label: '所有', icon: '🏠' },
    ...Object.entries(ToolCategory).map(([, value]) => ({
      key: value,
      label: CATEGORY_LABELS[value],
      icon: CATEGORY_ICONS[value]
    }))
  ];

  return (
    <div className="w-full">
      {/* Mobile horizontal scroll */}
      <div className="md:hidden">
        <div className="flex space-x-3 overflow-x-auto pb-3 scrollbar-hide px-1">
          {categories.map((category) => (
            <button
              key={category.key}
              onClick={() => !isLoading && onCategoryChange(category.key)}
              disabled={isLoading}
              className={cn(
                'flex-shrink-0 flex items-center space-x-2 px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 min-w-fit touch-manipulation',
                activeCategory === category.key
                  ? 'bg-purple-100 text-purple-800 border border-purple-200 shadow-sm'
                  : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-200 hover:border-gray-300 shadow-sm',
                isLoading && 'opacity-50 cursor-not-allowed'
              )}
            >
              <span className="text-lg">{category.icon}</span>
              <span className="whitespace-nowrap">{category.label}</span>
              {isLoading && activeCategory === category.key && (
                <Loader2 className="w-3 h-3 animate-spin" />
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Desktop grid */}
      <div className="hidden md:grid md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-8 gap-4">
        {categories.map((category) => (
          <button
            key={category.key}
            onClick={() => !isLoading && onCategoryChange(category.key)}
            disabled={isLoading}
            className={cn(
              'flex flex-col items-center p-4 rounded-xl border-2 transition-all duration-200 hover:shadow-md relative',
              activeCategory === category.key
                ? 'bg-purple-50 border-purple-200 text-purple-800 shadow-sm'
                : 'bg-white border-gray-200 text-gray-600 hover:border-gray-300',
              isLoading && 'opacity-50 cursor-not-allowed'
            )}
          >
            <span className="text-2xl mb-2">{category.icon}</span>
            <span className="text-sm font-medium text-center">{category.label}</span>
            {isLoading && activeCategory === category.key && (
              <div className="absolute inset-0 flex items-center justify-center bg-white/80 rounded-xl">
                <Loader2 className="w-5 h-5 animate-spin text-purple-600" />
              </div>
            )}
          </button>
        ))}
      </div>
    </div>
  );
}