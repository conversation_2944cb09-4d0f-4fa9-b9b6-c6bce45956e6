'use client';

import React, { useState, useCallback } from 'react';
import { Upload, Download, FileText, Loader2, AlertCircle, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { PandocConverter, PANDOC_INPUT_FORMATS, PANDOC_OUTPUT_FORMATS, ConversionResult } from '@/lib/wasm/converters/PandocConverter';

interface DocConverterProps {
  className?: string;
}

export function DocConverter({ className }: DocConverterProps) {
  const [files, setFiles] = useState<File[]>([]);
  const [outputFormat, setOutputFormat] = useState<string>('pdf');
  const [isConverting, setIsConverting] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [results, setResults] = useState<ConversionResult[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [converter] = useState(() => PandocConverter.getInstance());

  // 处理文件选择
  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || []);
    const validFiles = selectedFiles.filter(file => {
      const ext = file.name.split('.').pop()?.toLowerCase() || '';
      return PANDOC_INPUT_FORMATS.some(format => format.extension === ext);
    });
    
    if (validFiles.length !== selectedFiles.length) {
      setError('部分文件格式不支持，已自动过滤');
      setTimeout(() => setError(null), 3000);
    }
    
    setFiles(validFiles);
    setResults([]);
  }, []);

  // 处理拖放
  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const droppedFiles = Array.from(event.dataTransfer.files);
    const validFiles = droppedFiles.filter(file => {
      const ext = file.name.split('.').pop()?.toLowerCase() || '';
      return PANDOC_INPUT_FORMATS.some(format => format.extension === ext);
    });
    
    if (validFiles.length !== droppedFiles.length) {
      setError('部分文件格式不支持，已自动过滤');
      setTimeout(() => setError(null), 3000);
    }
    
    setFiles(validFiles);
    setResults([]);
  }, []);

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  // 开始转换
  const handleConvert = async () => {
    if (files.length === 0) return;

    setIsConverting(true);
    setProgress(0);
    setError(null);
    setResults([]);

    try {
      // 初始化转换器（converter会自动检查是否已初始化）
      setIsInitializing(true);
      await converter.initialize((progress) => {
        setProgress(progress * 0.2); // 初始化占20%进度
      });
      setIsInitializing(false);

      // 批量转换文档
      const conversionResults = await converter.convertBatch(
        files,
        { outputFormat },
        (current, total) => {
          setProgress(20 + (current / total) * 80); // 转换占80%进度
        }
      );

      setResults(conversionResults);
      setProgress(100);
    } catch (err) {
      console.error('转换失败:', err);
      setError(err instanceof Error ? err.message : '转换失败，请重试');
    } finally {
      setIsConverting(false);
      setIsInitializing(false);
    }
  };

  // 下载结果
  const handleDownload = (result: ConversionResult) => {
    converter.downloadResult(result);
  };

  // 下载所有结果
  const handleDownloadAll = () => {
    results.forEach(result => {
      converter.downloadResult(result);
    });
  };

  // 重置
  const handleReset = () => {
    setFiles([]);
    setResults([]);
    setProgress(0);
    setError(null);
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle>文档格式转换器</CardTitle>
          <CardDescription>
            使用Pandoc WASM在浏览器中转换文档格式，支持Word、PDF、Markdown等多种格式
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* 文件上传区域 */}
          <div
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-green-500 transition-colors"
          >
            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p className="text-lg font-medium text-gray-700 mb-2">
              拖放文档到这里，或点击选择
            </p>
            <p className="text-sm text-gray-500 mb-4">
              支持格式：Word (docx/doc)、Markdown、HTML、PDF、EPUB等
            </p>
            <input
              type="file"
              multiple
              accept={PANDOC_INPUT_FORMATS.map(f => `.${f.extension}`).join(',')}
              onChange={handleFileSelect}
              className="hidden"
              id="file-input"
            />
            <label htmlFor="file-input" className="inline-block">
              <div className="inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 cursor-pointer">
                <Upload className="w-4 h-4 mr-2" />
                选择文档
              </div>
            </label>
          </div>

          {/* 已选择的文件列表 */}
          {files.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-700 mb-2">
                已选择 {files.length} 个文件
              </h4>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {files.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm text-gray-600 truncate">
                      {file.name}
                    </span>
                    <span className="text-xs text-gray-500">
                      {(file.size / 1024).toFixed(2)} KB
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 输出格式选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              输出格式
            </label>
            <select
              value={outputFormat}
              onChange={(e) => setOutputFormat(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              disabled={isConverting}
            >
              {PANDOC_OUTPUT_FORMATS.map(format => (
                <option key={format.extension} value={format.extension}>
                  {format.name} (.{format.extension})
                </option>
              ))}
            </select>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-center space-x-4">
            <Button
              onClick={handleConvert}
              disabled={files.length === 0 || isConverting}
              className="bg-green-600 hover:bg-green-700"
            >
              {isInitializing ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  初始化中...
                </>
              ) : isConverting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  转换中 {progress}%
                </>
              ) : (
                <>
                  <FileText className="w-4 h-4 mr-2" />
                  开始转换
                </>
              )}
            </Button>
            
            {results.length > 0 && (
              <Button
                onClick={handleDownloadAll}
                variant="outline"
              >
                <Download className="w-4 h-4 mr-2" />
                下载全部
              </Button>
            )}
            
            <Button
              onClick={handleReset}
              variant="outline"
              disabled={isConverting}
            >
              重置
            </Button>
          </div>

          {/* 进度条 */}
          {isConverting && (
            <div className="w-full">
              <div className="flex justify-between text-sm text-gray-600 mb-1">
                <span>转换进度</span>
                <span>{progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          )}

          {/* 错误提示 */}
          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg flex items-start">
              <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 mr-2" />
              <p className="text-sm text-red-800">{error}</p>
            </div>
          )}

          {/* 转换结果 */}
          {results.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-700 mb-2 flex items-center">
                <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                转换完成
              </h4>
              <div className="space-y-2">
                {results.map((result, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <span className="text-sm text-gray-700">
                      {result.filename}
                    </span>
                    <Button
                      size="sm"
                      onClick={() => handleDownload(result)}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <Download className="w-3 h-3 mr-1" />
                      下载
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 使用说明 */}
          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">💡 使用说明</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 支持批量转换，一次可选择多个文档</li>
              <li>• 所有转换在本地完成，文件不会上传到服务器</li>
              <li>• 首次使用需要加载WASM模块（约15MB）</li>
              <li>• 支持的输入格式：Word、Markdown、HTML、RTF、EPUB等</li>
              <li>• 支持的输出格式：PDF、Word、Markdown、HTML、EPUB等</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}