export interface Tool {
  id: string;
  name: string;
  description: string;
  category: ToolCategory;
  tags: string[];
  icon: string;
  url: string;
  featured?: boolean;
  usageCount?: number;
  rating?: number;
  // 新增字段
  status?: 'development' | 'beta' | 'stable';
  complexity?: 'simple' | 'medium' | 'complex';
  features?: string[];
  requirements?: ToolRequirements;
}

export interface ToolRequirements {
  webAssembly?: boolean;
  api?: boolean;
  offline?: boolean;
  auth?: boolean;
}

export enum ToolCategory {
  TEXT = 'text',
  DOCUMENT = 'document', 
  IMAGE = 'image',
  AUDIO = 'audio',
  VIDEO = 'video',
  DEVELOPER = 'developer',
  UTILITY = 'utility',
  DESIGN = 'design',
  CRYPTO = 'crypto',
  CONVERTER = 'converter'
}

export const CATEGORY_LABELS: Record<ToolCategory, string> = {
  [ToolCategory.TEXT]: '文本类',
  [ToolCategory.DOCUMENT]: '文档类',
  [ToolCategory.IMAGE]: '图像类',
  [ToolCategory.AUDIO]: '音频类',
  [ToolCategory.VIDEO]: '视频类',
  [ToolCategory.DEVELOPER]: '开发类',
  [ToolCategory.UTILITY]: '实用工具',
  [ToolCategory.DESIGN]: '设计类',
  [ToolCategory.CRYPTO]: '加密类',
  [ToolCategory.CONVERTER]: '转换类'
};