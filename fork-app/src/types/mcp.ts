// MCP Server 类型定义
export interface MCPServer {
  id: string
  name: string
  description: string
  author: string
  repository?: string
  imageUrl?: string
  avatar?: string
  category: ServerCategory
  tags: string[]
  featured: boolean
  sponsored: boolean
  verified: boolean
  official: boolean
  hosted: boolean
  createdAt: string
  updatedAt: string
  stars?: number
  downloads?: number
}

// MCP Client 类型定义
export interface MCPClient {
  id: string
  name: string
  description: string
  author: string
  repository?: string
  imageUrl?: string
  avatar?: string
  platform: ClientPlatform[]
  tags: string[]
  featured: boolean
  sponsored: boolean
  verified: boolean
  official: boolean
  createdAt: string
  updatedAt: string
  stars?: number
  downloads?: number
}

// Server 分类
export enum ServerCategory {
  AI = 'ai',
  DATA = 'data',
  TOOLS = 'tools',
  SEARCH = 'search',
  API = 'api',
  DATABASE = 'database',
  FILE = 'file',
  COMMUNICATION = 'communication',
  DEVELOPMENT = 'development',
  UTILITY = 'utility',
  OTHER = 'other'
}

// Client 平台
export enum ClientPlatform {
  WINDOWS = 'windows',
  MACOS = 'macos',
  LINUX = 'linux',
  WEB = 'web',
  VSCODE = 'vscode',
  JETBRAINS = 'jetbrains',
  TERMINAL = 'terminal',
  MOBILE = 'mobile'
}

// FAQ 项目
export interface FAQItem {
  id: number
  question: string
  answer: string
}

// Tab 类型
export type TabType = 'today' | 'featured' | 'latest' | 'clients' | 'hosted' | 'official'

// 搜索过滤器
export interface SearchFilters {
  query?: string
  category?: ServerCategory
  platform?: ClientPlatform
  tags?: string[]
  featured?: boolean
  sponsored?: boolean
  verified?: boolean
  official?: boolean
  hosted?: boolean
}