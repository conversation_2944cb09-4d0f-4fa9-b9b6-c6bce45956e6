/**
 * Web Worker管理器
 * 负责创建、管理和通信Web Workers
 */

export interface WorkerMessage {
  type: string;
  data?: any;
  error?: string;
  progress?: number;
}

export interface WorkerConfig {
  name: string;
  url: string;
  maxInstances?: number;
}

export class WorkerManager {
  private static workers = new Map<string, Worker[]>();
  private static configs = new Map<string, WorkerConfig>();
  private static messageHandlers = new Map<string, Map<string, (message: WorkerMessage) => void>>();

  /**
   * 注册Worker配置
   */
  public static register(config: WorkerConfig): void {
    this.configs.set(config.name, config);
    this.workers.set(config.name, []);
    this.messageHandlers.set(config.name, new Map());
  }

  /**
   * 创建新的Worker实例
   */
  public static async createWorker(name: string): Promise<Worker> {
    const config = this.configs.get(name);
    if (!config) {
      throw new Error(`Worker配置未找到: ${name}`);
    }

    const workers = this.workers.get(name) || [];
    const maxInstances = config.maxInstances || 4;

    if (workers.length >= maxInstances) {
      throw new Error(`Worker实例数已达上限: ${maxInstances}`);
    }

    const worker = new Worker(config.url);
    workers.push(worker);
    this.workers.set(name, workers);

    // 设置消息处理
    worker.onmessage = (event) => {
      const handlers = this.messageHandlers.get(name);
      if (handlers) {
        handlers.forEach(handler => handler(event.data));
      }
    };

    worker.onerror = (error) => {
      console.error(`Worker错误 [${name}]:`, error);
    };

    return worker;
  }

  /**
   * 获取可用的Worker实例
   */
  public static async getWorker(name: string): Promise<Worker> {
    const workers = this.workers.get(name) || [];
    
    // 如果没有可用的Worker，创建新的
    if (workers.length === 0) {
      return this.createWorker(name);
    }

    // 返回第一个可用的Worker（简单的负载均衡）
    return workers[0];
  }

  /**
   * 发送消息到Worker
   */
  public static async sendMessage(
    workerName: string,
    message: any,
    transferables?: Transferable[]
  ): Promise<any> {
    const worker = await this.getWorker(workerName);
    
    return new Promise((resolve, reject) => {
      const messageId = Math.random().toString(36).substr(2, 9);
      const timeout = setTimeout(() => {
        reject(new Error('Worker响应超时'));
      }, 60000); // 60秒超时

      const handler = (event: MessageEvent) => {
        if (event.data.id === messageId) {
          clearTimeout(timeout);
          worker.removeEventListener('message', handler);
          
          if (event.data.error) {
            reject(new Error(event.data.error));
          } else {
            resolve(event.data.result);
          }
        }
      };

      worker.addEventListener('message', handler);
      
      if (transferables) {
        worker.postMessage({ ...message, id: messageId }, transferables);
      } else {
        worker.postMessage({ ...message, id: messageId });
      }
    });
  }

  /**
   * 注册消息处理器
   */
  public static onMessage(
    workerName: string,
    handlerId: string,
    handler: (message: WorkerMessage) => void
  ): void {
    const handlers = this.messageHandlers.get(workerName) || new Map();
    handlers.set(handlerId, handler);
    this.messageHandlers.set(workerName, handlers);
  }

  /**
   * 移除消息处理器
   */
  public static offMessage(workerName: string, handlerId: string): void {
    const handlers = this.messageHandlers.get(workerName);
    if (handlers) {
      handlers.delete(handlerId);
    }
  }

  /**
   * 终止Worker
   */
  public static terminateWorker(name: string, worker: Worker): void {
    const workers = this.workers.get(name) || [];
    const index = workers.indexOf(worker);
    
    if (index !== -1) {
      workers.splice(index, 1);
      worker.terminate();
      this.workers.set(name, workers);
    }
  }

  /**
   * 终止所有Workers
   */
  public static terminateAll(name?: string): void {
    if (name) {
      const workers = this.workers.get(name) || [];
      workers.forEach(worker => worker.terminate());
      this.workers.set(name, []);
    } else {
      this.workers.forEach((workers, key) => {
        workers.forEach(worker => worker.terminate());
        this.workers.set(key, []);
      });
    }
  }

  /**
   * 获取Worker状态
   */
  public static getStatus(name?: string): Map<string, number> | number {
    if (name) {
      const workers = this.workers.get(name) || [];
      return workers.length;
    }

    const status = new Map<string, number>();
    this.workers.forEach((workers, key) => {
      status.set(key, workers.length);
    });
    return status;
  }

  /**
   * 执行任务并自动管理Worker生命周期
   */
  public static async executeTask<T>(
    workerName: string,
    task: any,
    onProgress?: (progress: number) => void
  ): Promise<T> {
    const worker = await this.createWorker(workerName);
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.terminateWorker(workerName, worker);
        reject(new Error('任务执行超时'));
      }, 300000); // 5分钟超时

      const messageHandler = (event: MessageEvent) => {
        const { type, data, error, progress } = event.data;

        switch (type) {
          case 'progress':
            if (onProgress && typeof progress === 'number') {
              onProgress(progress);
            }
            break;

          case 'complete':
            clearTimeout(timeout);
            worker.removeEventListener('message', messageHandler);
            this.terminateWorker(workerName, worker);
            resolve(data);
            break;

          case 'error':
            clearTimeout(timeout);
            worker.removeEventListener('message', messageHandler);
            this.terminateWorker(workerName, worker);
            reject(new Error(error || '未知错误'));
            break;
        }
      };

      worker.addEventListener('message', messageHandler);
      worker.addEventListener('error', (error) => {
        clearTimeout(timeout);
        this.terminateWorker(workerName, worker);
        reject(error);
      });

      worker.postMessage(task);
    });
  }
}