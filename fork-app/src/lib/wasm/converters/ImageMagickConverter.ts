/**
 * ImageMagick图像处理器
 * 使用ImageMagick WASM进行图像处理和转换
 */

import { WasmLoader, WasmModuleConfig } from '../WasmLoader';
import { WorkerManager } from '../WorkerManager';

export interface ImageFormat {
  extension: string;
  name: string;
  mimeType: string;
}

// 支持的图像格式
export const IMAGE_FORMATS: ImageFormat[] = [
  { extension: 'jpg', name: 'JPEG', mimeType: 'image/jpeg' },
  { extension: 'jpeg', name: 'JPEG', mimeType: 'image/jpeg' },
  { extension: 'png', name: 'PNG', mimeType: 'image/png' },
  { extension: 'gif', name: 'GIF', mimeType: 'image/gif' },
  { extension: 'bmp', name: 'BMP', mimeType: 'image/bmp' },
  { extension: 'webp', name: 'WebP', mimeType: 'image/webp' },
  { extension: 'tiff', name: 'TIFF', mimeType: 'image/tiff' },
  { extension: 'ico', name: 'ICO', mimeType: 'image/x-icon' },
  { extension: 'svg', name: 'SVG', mimeType: 'image/svg+xml' },
  { extension: 'psd', name: 'Photoshop', mimeType: 'image/vnd.adobe.photoshop' },
];

export interface ImageProcessOptions {
  format?: string;           // 输出格式
  width?: number;            // 宽度
  height?: number;           // 高度
  quality?: number;          // 质量 (1-100)
  resize?: 'fit' | 'fill' | 'cover' | 'contain'; // 调整模式
  rotate?: number;           // 旋转角度
  flip?: 'horizontal' | 'vertical'; // 翻转
  grayscale?: boolean;       // 转灰度
  blur?: number;            // 模糊程度
  sharpen?: number;         // 锐化程度
  brightness?: number;      // 亮度 (-100 到 100)
  contrast?: number;        // 对比度 (-100 到 100)
  watermark?: {             // 水印
    text?: string;
    position?: 'center' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
    fontSize?: number;
    color?: string;
    opacity?: number;
  };
}

export interface ProcessResult {
  data: Blob;
  filename: string;
  mimeType: string;
  width?: number;
  height?: number;
  size?: number;
}

export class ImageMagickConverter {
  private static instance: ImageMagickConverter;
  private wasmModule: ArrayBuffer | null = null;
  private ready = false;

  private constructor() {}

  public static getInstance(): ImageMagickConverter {
    if (!this.instance) {
      this.instance = new ImageMagickConverter();
    }
    return this.instance;
  }

  /**
   * 初始化ImageMagick WASM模块
   */
  public async initialize(onProgress?: (progress: number) => void): Promise<void> {
    if (this.ready) return;

    const config: WasmModuleConfig = {
      name: 'imagemagick',
      url: '/wasm/magick.wasm',
      size: 8 * 1024 * 1024 // 约8MB
    };

    this.wasmModule = await WasmLoader.load(config, (progress) => {
      if (onProgress) {
        onProgress(progress.percentage);
      }
    });

    // 注册Worker
    WorkerManager.register({
      name: 'imagemagick',
      url: '/workers/imagemagick.worker.js',
      maxInstances: 4
    });

    this.ready = true;
  }

  /**
   * 检查格式是否支持
   */
  public isFormatSupported(format: string): boolean {
    return IMAGE_FORMATS.some(f => f.extension === format.toLowerCase());
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(filename: string): string {
    const parts = filename.split('.');
    return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : '';
  }

  /**
   * 处理单个图像
   */
  public async process(
    file: File,
    options: ImageProcessOptions = {},
    onProgress?: (progress: number) => void
  ): Promise<ProcessResult> {
    if (!this.ready) {
      await this.initialize();
    }

    // 自动检测输入格式
    const inputFormat = this.getFileExtension(file.name);
    const outputFormat = options.format || inputFormat;

    if (!this.isFormatSupported(inputFormat)) {
      throw new Error(`不支持的输入格式: ${inputFormat}`);
    }

    if (!this.isFormatSupported(outputFormat)) {
      throw new Error(`不支持的输出格式: ${outputFormat}`);
    }

    // 读取文件内容
    const fileBuffer = await this.readFileAsArrayBuffer(file);

    // 构建ImageMagick命令参数
    const commands = this.buildCommands(options);

    // 执行处理任务
    const result = await WorkerManager.executeTask<{
      data: ArrayBuffer;
      width: number;
      height: number;
    }>('imagemagick', {
      type: 'process',
      wasmModule: this.wasmModule,
      input: fileBuffer,
      inputFormat,
      outputFormat,
      commands,
      options
    }, onProgress);

    // 获取输出格式信息
    const outputFormatInfo = IMAGE_FORMATS.find(
      f => f.extension === outputFormat
    );

    // 生成输出文件名
    const baseName = file.name.substring(0, file.name.lastIndexOf('.'));
    const suffix = this.getProcessingSuffix(options);
    const outputFilename = `${baseName}${suffix}.${outputFormat}`;

    return {
      data: new Blob([result.data], { 
        type: outputFormatInfo?.mimeType || 'image/jpeg' 
      }),
      filename: outputFilename,
      mimeType: outputFormatInfo?.mimeType || 'image/jpeg',
      width: result.width,
      height: result.height,
      size: result.data.byteLength
    };
  }

  /**
   * 批量处理图像
   */
  public async processBatch(
    files: File[],
    options: ImageProcessOptions = {},
    onProgress?: (current: number, total: number) => void
  ): Promise<ProcessResult[]> {
    const results: ProcessResult[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      try {
        const result = await this.process(file, options, (progress) => {
          if (onProgress) {
            const overallProgress = (i + progress / 100) / files.length;
            onProgress(Math.round(overallProgress * 100), 100);
          }
        });
        
        results.push(result);
      } catch (error) {
        console.error(`处理图像失败: ${file.name}`, error);
        throw error;
      }
    }

    return results;
  }

  /**
   * 构建ImageMagick命令参数
   */
  private buildCommands(options: ImageProcessOptions): string[] {
    const commands: string[] = [];

    // 调整大小
    if (options.width || options.height) {
      const width = options.width || '';
      const height = options.height || '';
      const mode = options.resize || 'fit';
      
      switch (mode) {
        case 'fit':
          commands.push('-resize', `${width}x${height}`);
          break;
        case 'fill':
          commands.push('-resize', `${width}x${height}!`);
          break;
        case 'cover':
          commands.push('-resize', `${width}x${height}^`);
          commands.push('-gravity', 'center');
          commands.push('-crop', `${width}x${height}+0+0`);
          break;
        case 'contain':
          commands.push('-resize', `${width}x${height}>`);
          break;
      }
    }

    // 质量设置
    if (options.quality) {
      commands.push('-quality', options.quality.toString());
    }

    // 旋转
    if (options.rotate) {
      commands.push('-rotate', options.rotate.toString());
    }

    // 翻转
    if (options.flip) {
      if (options.flip === 'horizontal') {
        commands.push('-flop');
      } else if (options.flip === 'vertical') {
        commands.push('-flip');
      }
    }

    // 灰度
    if (options.grayscale) {
      commands.push('-colorspace', 'Gray');
    }

    // 模糊
    if (options.blur) {
      commands.push('-blur', `0x${options.blur}`);
    }

    // 锐化
    if (options.sharpen) {
      commands.push('-sharpen', `0x${options.sharpen}`);
    }

    // 亮度和对比度
    if (options.brightness !== undefined || options.contrast !== undefined) {
      const brightness = options.brightness || 0;
      const contrast = options.contrast || 0;
      commands.push('-brightness-contrast', `${brightness}x${contrast}`);
    }

    // 水印
    if (options.watermark?.text) {
      const { text, position = 'bottom-right', fontSize = 20, color = 'white', opacity = 0.5 } = options.watermark;
      
      commands.push('-gravity', this.getGravity(position));
      commands.push('-pointsize', fontSize.toString());
      commands.push('-fill', color);
      commands.push('-annotate', '+10+10', text);
      
      if (opacity < 1) {
        commands.push('-alpha', 'set');
        commands.push('-channel', 'A');
        commands.push('-evaluate', 'multiply', opacity.toString());
      }
    }

    return commands;
  }

  /**
   * 获取重力参数
   */
  private getGravity(position: string): string {
    const gravityMap: Record<string, string> = {
      'center': 'Center',
      'top-left': 'NorthWest',
      'top-right': 'NorthEast',
      'bottom-left': 'SouthWest',
      'bottom-right': 'SouthEast'
    };
    return gravityMap[position] || 'Center';
  }

  /**
   * 获取处理后缀
   */
  private getProcessingSuffix(options: ImageProcessOptions): string {
    const suffixes: string[] = [];
    
    if (options.width || options.height) {
      suffixes.push(`${options.width || 'auto'}x${options.height || 'auto'}`);
    }
    
    if (options.grayscale) {
      suffixes.push('gray');
    }
    
    if (options.watermark?.text) {
      suffixes.push('watermark');
    }
    
    return suffixes.length > 0 ? '_' + suffixes.join('_') : '';
  }

  /**
   * 读取文件为ArrayBuffer
   */
  private readFileAsArrayBuffer(file: File): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as ArrayBuffer);
      reader.onerror = reject;
      reader.readAsArrayBuffer(file);
    });
  }

  /**
   * 获取图像信息
   */
  public async getImageInfo(file: File): Promise<{
    width: number;
    height: number;
    format: string;
    size: number;
  }> {
    if (!this.ready) {
      await this.initialize();
    }

    const fileBuffer = await this.readFileAsArrayBuffer(file);
    const inputFormat = this.getFileExtension(file.name);

    const info = await WorkerManager.executeTask<{
      width: number;
      height: number;
      format: string;
    }>('imagemagick', {
      type: 'info',
      wasmModule: this.wasmModule,
      input: fileBuffer,
      inputFormat
    });

    return {
      ...info,
      size: file.size
    };
  }

  /**
   * 下载处理结果
   */
  public downloadResult(result: ProcessResult): void {
    const url = URL.createObjectURL(result.data);
    const a = document.createElement('a');
    a.href = url;
    a.download = result.filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    WorkerManager.terminateAll('imagemagick');
    this.wasmModule = null;
    this.ready = false;
  }
}