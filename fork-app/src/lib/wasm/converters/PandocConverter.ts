/**
 * Pandoc文档转换器
 * 使用Pandoc WASM进行文档格式转换
 */

import { WorkerManager } from '../WorkerManager';

export interface PandocFormat {
  extension: string;
  name: string;
  mimeType: string;
}

// 支持的输入格式
export const PANDOC_INPUT_FORMATS: PandocFormat[] = [
  { extension: 'docx', name: 'Microsoft Word', mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' },
  { extension: 'doc', name: 'Microsoft Word (旧版)', mimeType: 'application/msword' },
  { extension: 'md', name: 'Markdown', mimeType: 'text/markdown' },
  { extension: 'html', name: 'HTML', mimeType: 'text/html' },
  { extension: 'rtf', name: 'Rich Text Format', mimeType: 'application/rtf' },
  { extension: 'csv', name: 'CSV', mimeType: 'text/csv' },
  { extension: 'tsv', name: 'TSV', mimeType: 'text/tab-separated-values' },
  { extension: 'json', name: 'JSO<PERSON>', mimeType: 'application/json' },
  { extension: 'rst', name: 'reStructuredText', mimeType: 'text/x-rst' },
  { extension: 'epub', name: 'EPUB', mimeType: 'application/epub+zip' },
  { extension: 'odt', name: 'OpenDocument Text', mimeType: 'application/vnd.oasis.opendocument.text' },
  { extension: 'tex', name: 'LaTeX', mimeType: 'text/x-tex' },
];

// 支持的输出格式
export const PANDOC_OUTPUT_FORMATS: PandocFormat[] = [
  { extension: 'docx', name: 'Microsoft Word', mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' },
  { extension: 'pdf', name: 'PDF', mimeType: 'application/pdf' },
  { extension: 'md', name: 'Markdown', mimeType: 'text/markdown' },
  { extension: 'html', name: 'HTML', mimeType: 'text/html' },
  { extension: 'rtf', name: 'Rich Text Format', mimeType: 'application/rtf' },
  { extension: 'epub', name: 'EPUB', mimeType: 'application/epub+zip' },
  { extension: 'odt', name: 'OpenDocument Text', mimeType: 'application/vnd.oasis.opendocument.text' },
  { extension: 'tex', name: 'LaTeX', mimeType: 'text/x-tex' },
  { extension: 'txt', name: '纯文本', mimeType: 'text/plain' },
];

export interface ConversionOptions {
  inputFormat?: string;
  outputFormat: string;
  standalone?: boolean;
  metadata?: Record<string, any>;
}

export interface ConversionResult {
  data: Blob;
  filename: string;
  mimeType: string;
}

export class PandocConverter {
  private static instance: PandocConverter;
  private ready = false;

  private constructor() {}

  public static getInstance(): PandocConverter {
    if (!this.instance) {
      this.instance = new PandocConverter();
    }
    return this.instance;
  }

  /**
   * 检查是否已初始化
   */
  public isInitialized(): boolean {
    return this.ready;
  }

  /**
   * 初始化Pandoc WASM模块
   */
  public async initialize(onProgress?: (progress: number) => void): Promise<void> {
    if (this.ready) return;

    // 注册Worker（使用真实的 Pandoc WASM）
    WorkerManager.register({
      name: 'pandoc',
      url: '/workers/pandoc.worker.js',
      maxInstances: 2
    });

    // Worker会自己初始化，这里只是模拟进度
    if (onProgress) {
      for (let i = 0; i <= 100; i += 20) {
        onProgress(i);
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    this.ready = true;
  }

  /**
   * 检查格式是否支持
   */
  public isFormatSupported(format: string, type: 'input' | 'output'): boolean {
    const formats = type === 'input' ? PANDOC_INPUT_FORMATS : PANDOC_OUTPUT_FORMATS;
    return formats.some(f => f.extension === format.toLowerCase());
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(filename: string): string {
    const parts = filename.split('.');
    return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : '';
  }

  /**
   * 转换文档
   */
  public async convert(
    file: File,
    options: ConversionOptions,
    onProgress?: (progress: number) => void
  ): Promise<ConversionResult> {
    if (!this.ready) {
      await this.initialize();
    }

    // 自动检测输入格式
    const inputFormat = options.inputFormat || this.getFileExtension(file.name);
    
    if (!this.isFormatSupported(inputFormat, 'input')) {
      throw new Error(`不支持的输入格式: ${inputFormat}`);
    }

    if (!this.isFormatSupported(options.outputFormat, 'output')) {
      throw new Error(`不支持的输出格式: ${options.outputFormat}`);
    }

    // 读取文件内容
    const fileBuffer = await this.readFileAsArrayBuffer(file);

    // 执行转换任务（Worker会自己加载WASM）
    const result = await WorkerManager.executeTask<ArrayBuffer>('pandoc', {
      type: 'convert',
      input: fileBuffer,
      inputFormat,
      outputFormat: options.outputFormat,
      options: {
        standalone: options.standalone !== false,
        metadata: options.metadata
      }
    }, onProgress);

    // 获取输出格式信息
    let actualOutputFormat = options.outputFormat;
    
    // PDF 在浏览器中会转换为 HTML
    if (options.outputFormat === 'pdf') {
      actualOutputFormat = 'html';
    }
    
    const outputFormatInfo = PANDOC_OUTPUT_FORMATS.find(
      f => f.extension === actualOutputFormat
    );

    // 生成输出文件名
    const baseName = file.name.substring(0, file.name.lastIndexOf('.'));
    let outputFilename = `${baseName}.${actualOutputFormat}`;
    
    // 如果原本要转 PDF，在文件名中标注
    if (options.outputFormat === 'pdf') {
      outputFilename = `${baseName}_as_html.html`;
    }

    return {
      data: new Blob([result], { type: outputFormatInfo?.mimeType || 'application/octet-stream' }),
      filename: outputFilename,
      mimeType: outputFormatInfo?.mimeType || 'application/octet-stream'
    };
  }

  /**
   * 批量转换文档
   */
  public async convertBatch(
    files: File[],
    options: ConversionOptions,
    onProgress?: (current: number, total: number) => void
  ): Promise<ConversionResult[]> {
    const results: ConversionResult[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      try {
        const result = await this.convert(file, options, (progress) => {
          if (onProgress) {
            const overallProgress = (i + progress / 100) / files.length;
            onProgress(Math.round(overallProgress * 100), 100);
          }
        });
        
        results.push(result);
      } catch (error) {
        // 改进错误日志记录
        let errorDetails = '未知错误';

        if (error instanceof Error) {
          errorDetails = error.message;
        } else if (typeof error === 'string') {
          errorDetails = error;
        } else if (error && typeof error === 'object') {
          const errObj = error as { message?: string; error?: string; stack?: string };
          errorDetails = errObj.message || errObj.error || JSON.stringify(error) || '空错误对象';
        }

        // 如果错误信息为空或无用，提供更具体的信息
        if (!errorDetails || errorDetails === '{}' || errorDetails === '[object Object]') {
          errorDetails = `文件格式可能不受支持或文件已损坏。文件类型: ${file.type || '未知'}, 大小: ${file.size} bytes`;
        }

        console.error(`转换文件失败: ${file.name}`, {
          error: errorDetails,
          fileType: file.type,
          fileSize: file.size,
          originalError: error
        });

        // 如果单个文件失败，创建一个错误报告文件
        const errorMessage = `转换失败: ${file.name}\n\n错误信息: ${errorDetails}\n\n文件信息:\n- 类型: ${file.type || '未知'}\n- 大小: ${file.size} bytes\n- 最后修改: ${file.lastModified ? new Date(file.lastModified).toLocaleString() : '未知'}\n\n建议:\n1. 检查文件格式是否受支持\n2. 尝试其他输出格式\n3. 检查文件是否损坏\n4. 如果是HTML文件，确保编码正确`;
        const encoder = new TextEncoder();
        const errorBuffer = encoder.encode(errorMessage);

        results.push({
          data: new Blob([errorBuffer], { type: 'text/plain' }),
          filename: `${file.name.replace(/\.[^/.]+$/, '')}_转换错误报告.txt`,
          mimeType: 'text/plain'
        });

        // 继续处理其他文件，不中断整个批处理
        console.log(`跳过失败的文件: ${file.name}，继续处理其他文件`);
      }
    }

    return results;
  }

  /**
   * 读取文件为ArrayBuffer
   */
  private readFileAsArrayBuffer(file: File): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as ArrayBuffer);
      reader.onerror = reject;
      reader.readAsArrayBuffer(file);
    });
  }

  /**
   * 下载转换结果
   */
  public downloadResult(result: ConversionResult): void {
    const url = URL.createObjectURL(result.data);
    const a = document.createElement('a');
    a.href = url;
    a.download = result.filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    WorkerManager.terminateAll('pandoc');
    this.ready = false;
  }
}