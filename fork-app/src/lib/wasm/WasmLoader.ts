/**
 * WASM模块加载器基类
 * 负责加载、缓存和管理WebAssembly模块
 */

export interface WasmModuleConfig {
  name: string;
  url: string;
  size?: number; // 文件大小（字节）
  version?: string;
}

export interface LoadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export class WasmLoader {
  private static cache = new Map<string, ArrayBuffer>();
  private static dbName = 'toolForkWasmCache';
  private static dbVersion = 1;

  /**
   * 初始化IndexedDB用于缓存WASM模块
   */
  private static async initDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains('wasmModules')) {
          db.createObjectStore('wasmModules');
        }
      };
    });
  }

  /**
   * 从IndexedDB获取缓存的WASM模块
   */
  private static async getCachedModule(name: string): Promise<ArrayBuffer | null> {
    try {
      const db = await this.initDB();
      const transaction = db.transaction(['wasmModules'], 'readonly');
      const store = transaction.objectStore('wasmModules');
      
      return new Promise((resolve, reject) => {
        const request = store.get(name);
        request.onsuccess = () => resolve(request.result || null);
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error('获取缓存模块失败:', error);
      return null;
    }
  }

  /**
   * 将WASM模块缓存到IndexedDB
   */
  private static async cacheModule(name: string, data: ArrayBuffer): Promise<void> {
    try {
      const db = await this.initDB();
      const transaction = db.transaction(['wasmModules'], 'readwrite');
      const store = transaction.objectStore('wasmModules');
      
      return new Promise((resolve, reject) => {
        const request = store.put(data, name);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error('缓存模块失败:', error);
    }
  }

  /**
   * 加载WASM模块
   * @param config 模块配置
   * @param onProgress 进度回调
   * @returns WASM模块的ArrayBuffer
   */
  public static async load(
    config: WasmModuleConfig,
    onProgress?: (progress: LoadProgress) => void
  ): Promise<ArrayBuffer> {
    // 检查内存缓存
    if (this.cache.has(config.name)) {
      console.log(`从内存缓存加载: ${config.name}`);
      return this.cache.get(config.name)!;
    }

    // 检查IndexedDB缓存
    const cached = await this.getCachedModule(config.name);
    if (cached) {
      console.log(`从IndexedDB缓存加载: ${config.name}`);
      this.cache.set(config.name, cached);
      return cached;
    }

    // 从网络加载
    console.log(`从网络加载: ${config.name}`);
    return this.fetchModule(config, onProgress);
  }

  /**
   * 从网络获取WASM模块
   */
  private static async fetchModule(
    config: WasmModuleConfig,
    onProgress?: (progress: LoadProgress) => void
  ): Promise<ArrayBuffer> {
    const response = await fetch(config.url);
    
    if (!response.ok) {
      throw new Error(`加载WASM模块失败: ${response.statusText}`);
    }

    const contentLength = response.headers.get('content-length');
    const total = contentLength ? parseInt(contentLength, 10) : config.size || 0;
    
    if (!response.body) {
      const buffer = await response.arrayBuffer();
      this.cache.set(config.name, buffer);
      await this.cacheModule(config.name, buffer);
      return buffer;
    }

    // 支持进度跟踪的流式下载
    const reader = response.body.getReader();
    const chunks: Uint8Array[] = [];
    let loaded = 0;

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;
      
      chunks.push(value);
      loaded += value.length;
      
      if (onProgress && total > 0) {
        onProgress({
          loaded,
          total,
          percentage: Math.round((loaded / total) * 100)
        });
      }
    }

    // 合并所有chunks
    const buffer = new ArrayBuffer(loaded);
    const uint8Array = new Uint8Array(buffer);
    let position = 0;
    
    for (const chunk of chunks) {
      uint8Array.set(chunk, position);
      position += chunk.length;
    }

    // 缓存模块
    this.cache.set(config.name, buffer);
    await this.cacheModule(config.name, buffer);
    
    return buffer;
  }

  /**
   * 预加载多个WASM模块
   */
  public static async preload(configs: WasmModuleConfig[]): Promise<void> {
    const promises = configs.map(config => this.load(config));
    await Promise.all(promises);
  }

  /**
   * 清除缓存
   */
  public static async clearCache(): Promise<void> {
    this.cache.clear();
    
    try {
      const db = await this.initDB();
      const transaction = db.transaction(['wasmModules'], 'readwrite');
      const store = transaction.objectStore('wasmModules');
      
      await new Promise<void>((resolve, reject) => {
        const request = store.clear();
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error('清除缓存失败:', error);
    }
  }

  /**
   * 获取缓存大小
   */
  public static async getCacheSize(): Promise<number> {
    let totalSize = 0;
    
    // 计算内存缓存大小
    for (const buffer of this.cache.values()) {
      totalSize += buffer.byteLength;
    }
    
    // 计算IndexedDB缓存大小
    try {
      const db = await this.initDB();
      const transaction = db.transaction(['wasmModules'], 'readonly');
      const store = transaction.objectStore('wasmModules');
      
      const keys = await new Promise<IDBValidKey[]>((resolve, reject) => {
        const request = store.getAllKeys();
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      });
      
      for (const key of keys) {
        const data = await new Promise<ArrayBuffer>((resolve, reject) => {
          const request = store.get(key);
          request.onsuccess = () => resolve(request.result);
          request.onerror = () => reject(request.error);
        });
        
        if (data) {
          totalSize += data.byteLength;
        }
      }
    } catch (error) {
      console.error('获取缓存大小失败:', error);
    }
    
    return totalSize;
  }
}