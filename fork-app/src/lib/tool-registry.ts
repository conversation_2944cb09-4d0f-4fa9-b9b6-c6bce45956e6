/**
 * 工具注册系统
 * 用于动态加载和管理工具组件
 */

import { ComponentType } from 'react';
import dynamic from 'next/dynamic';

// 工具组件的通用接口
export interface ToolComponentProps {
  className?: string;
}

// 工具组件注册表类型
type ToolComponentRegistry = {
  [key: string]: () => Promise<{ default: ComponentType<ToolComponentProps> }>;
};

// 注册的工具组件映射
// 使用动态导入实现代码分割和按需加载
export const toolComponents: ToolComponentRegistry = {
  // 现有工具
  'cutout': () => import('@/components/tools/ImageCutout').then(mod => ({ 
    default: mod.ImageCutout as ComponentType<ToolComponentProps> 
  })),
  'ui-generator': () => import('@/components/tools/UIGenerator').then(mod => ({ 
    default: mod.UIGenerator as ComponentType<ToolComponentProps> 
  })),
  
  // WASM工具
  'doc-converter': () => import('@/components/tools/DocConverter').then(mod => ({ 
    default: mod.DocConverter as ComponentType<ToolComponentProps> 
  })),
  'image-processor': () => import('@/components/tools/ImageProcessor').then(mod => ({ 
    default: mod.ImageProcessor as ComponentType<ToolComponentProps> 
  })),
  
  // 新工具会自动添加到这里
  // 'new-tool-id': () => import('@/components/tools/NewTool').then(mod => ({ 
  //   default: mod.NewTool as ComponentType<ToolComponentProps> 
  // })),
};

// 动态加载工具组件
export const loadToolComponent = (toolId: string) => {
  const loader = toolComponents[toolId];
  
  if (!loader) {
    // 如果工具组件未注册，返回默认组件
    return dynamic(() => 
      Promise.resolve({ 
        default: DefaultToolComponent as ComponentType<ToolComponentProps> 
      }), 
      { 
        loading: () => <LoadingComponent />,
        ssr: false 
      }
    );
  }

  return dynamic(loader, {
    loading: () => <LoadingComponent />,
    ssr: false
  });
};

// 检查工具是否已注册组件
export const isToolRegistered = (toolId: string): boolean => {
  return toolId in toolComponents;
};

// 获取所有已注册的工具ID
export const getRegisteredToolIds = (): string[] => {
  return Object.keys(toolComponents);
};

// 注册新工具（运行时动态注册）
export const registerTool = (
  toolId: string, 
  loader: () => Promise<{ default: ComponentType<ToolComponentProps> }>
) => {
  if (toolComponents[toolId]) {
    console.warn(`工具 ${toolId} 已经注册，将被覆盖`);
  }
  toolComponents[toolId] = loader;
};

// 加载中组件
function LoadingComponent() {
  return (
    <div className="flex items-center justify-center p-12">
      <div className="text-center">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mb-4"></div>
        <p className="text-gray-600">加载工具中...</p>
      </div>
    </div>
  );
}

// 默认工具组件（未实现的工具）
function DefaultToolComponent({ className }: ToolComponentProps) {
  return (
    <div className={`text-center py-12 ${className || ''}`}>
      <div className="text-6xl mb-4">🔧</div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">工具开发中</h3>
      <p className="text-gray-600">
        此工具正在开发中，敬请期待！
      </p>
      <div className="mt-8 p-4 bg-yellow-50 rounded-lg max-w-md mx-auto">
        <p className="text-sm text-yellow-800">
          💡 提示：如果您是开发者，请运行 <code className="bg-yellow-200 px-1 rounded">pnpm create-tool</code> 来创建这个工具。
        </p>
      </div>
    </div>
  );
}

// 工具组件错误边界
export class ToolErrorBoundary extends Error {
  constructor(public toolId: string, public originalError: Error) {
    super(`工具 ${toolId} 加载失败: ${originalError.message}`);
    this.name = 'ToolErrorBoundary';
  }
}