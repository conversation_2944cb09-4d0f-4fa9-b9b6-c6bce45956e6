import { Tool, ToolCategory } from '@/types/tool';

export const TOOLS_DATA: Tool[] = [
  {
    id: 'cutout',
    name: '一键抠图',
    description: '一键去除图片背景，证件照蓝底换背景',
    category: ToolCategory.IMAGE,
    tags: ['图像类'],
    icon: '🖼️',
    url: '/tool/cutout',
    featured: true,
    usageCount: 15420
  },
  {
    id: 'ui-generator',
    name: 'UI代码生成',
    description: '用 AI 生成 Tailwind CSS 页面代码',
    category: ToolCategory.DEVELOPER,
    tags: ['开发类'],
    icon: '🧩',
    url: '/tool/ui-generator',
    featured: true,
    usageCount: 8930
  },
  {
    id: 'file-rename',
    name: '文件批量重命名',
    description: '批量重命名文件',
    category: ToolCategory.UTILITY,
    tags: ['文档类'],
    icon: '📝',
    url: '/tool/file-rename',
    usageCount: 5670
  },
  {
    id: 'coin-flip',
    name: '抛硬币',
    description: '抛硬币反正面',
    category: ToolCategory.UTILITY,
    tags: ['其它'],
    icon: '🪙',
    url: '/tool/coin-flip',
    usageCount: 12340
  },
  {
    id: 'pdf-merge',
    name: '合并图片为PDF',
    description: '将图片合并、转为PDF',
    category: ToolCategory.CONVERTER,
    tags: ['文档类'],
    icon: '📄',
    url: '/tool/pdf-merge',
    usageCount: 7890
  },
  {
    id: 'image-upscaler',
    name: '图片清晰放大',
    description: '使用AI将图片放大4倍',
    category: ToolCategory.IMAGE,
    tags: ['图像类'],
    icon: '🔍',
    url: '/tool/image-upscaler',
    usageCount: 9120
  },
  {
    id: 'doc-printer',
    name: '证件照打印版',
    description: '排版打印不浪费',
    category: ToolCategory.IMAGE,
    tags: ['图像类'],
    icon: '🖨️',
    url: '/tool/doc-printer',
    usageCount: 6780
  },
  {
    id: 'metronome',
    name: '节拍器',
    description: '在线打拍工具',
    category: ToolCategory.AUDIO,
    tags: ['音频类'],
    icon: '🎵',
    url: '/tool/metronome',
    usageCount: 4560
  },
  {
    id: 'latex-ocr',
    name: '图片转LaTeX',
    description: '数学公式识别，图片转LaTeX',
    category: ToolCategory.IMAGE,
    tags: ['图像类'],
    icon: '📐',
    url: '/tool/latex-ocr',
    usageCount: 3450
  },
  {
    id: 'guitar-tuner',
    name: '吉他调音器',
    description: '吉他调音工具',
    category: ToolCategory.AUDIO,
    tags: ['音频类'],
    icon: '🎸',
    url: '/tool/guitar-tuner',
    usageCount: 5430
  },
  {
    id: 'code-deobfuscator',
    name: '代码反混淆',
    description: '反混淆 Java, JS, PHP, Python 等多种编程语言',
    category: ToolCategory.DEVELOPER,
    tags: ['开发类'],
    icon: '🔓',
    url: '/tool/code-deobfuscator',
    usageCount: 8760
  },
  {
    id: 'smart-writer',
    name: '智能文案提炼',
    description: '编写小红书、微博、朋友圈、热点评论文案',
    category: ToolCategory.TEXT,
    tags: ['文本类'],
    icon: '✍️',
    url: '/tool/smart-writer',
    usageCount: 7650
  },
  {
    id: 'js-tools',
    name: 'JavaScript工具',
    description: '在线JS美化、解压缩、混淆',
    category: ToolCategory.DEVELOPER,
    tags: ['开发类'],
    icon: '📜',
    url: '/tool/js-tools',
    usageCount: 9870
  },
  {
    id: 'css-tools',
    name: 'CSS工具',
    description: '在线CSS美化、格式化、压缩',
    category: ToolCategory.DEVELOPER,
    tags: ['开发类'],
    icon: '🎨',
    url: '/tool/css-tools',
    usageCount: 6540
  },
  {
    id: 'ip-lookup',
    name: 'IP地址查询',
    description: 'IP归属地查询，多套数据源，查询更准确',
    category: ToolCategory.UTILITY,
    tags: ['查询类'],
    icon: '🌐',
    url: '/tool/ip-lookup',
    usageCount: 11230
  },
  {
    id: 'python-tools',
    name: 'Python工具',
    description: 'pyc文件反编译，python美化、格式化',
    category: ToolCategory.DEVELOPER,
    tags: ['开发类'],
    icon: '🐍',
    url: '/tool/python-tools',
    usageCount: 4320
  },
  {
    id: 'doc-converter',
    name: '文档格式转换',
    description: '使用Pandoc WASM转换Word、PDF、Markdown等文档格式',
    category: ToolCategory.CONVERTER,
    tags: ['转换类', 'WASM'],
    icon: '📄',
    url: '/tool/doc-converter',
    featured: true,
    usageCount: 0,
    status: 'stable',
    complexity: 'medium',
    requirements: {
      webAssembly: true,
      offline: true
    }
  },
  {
    id: 'image-processor',
    name: '图像处理工具',
    description: '使用ImageMagick WASM处理图像，支持格式转换、调整大小、添加滤镜',
    category: ToolCategory.IMAGE,
    tags: ['图像类', 'WASM'],
    icon: '🎨',
    url: '/tool/image-processor',
    featured: true,
    usageCount: 0,
    status: 'stable',
    complexity: 'medium',
    requirements: {
      webAssembly: true,
      offline: true
    }
  }
];

// 获取特色工具
export const getFeaturedTools = (): Tool[] => {
  return TOOLS_DATA.filter(tool => tool.featured).sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0));
};

// 按分类获取工具
export const getToolsByCategory = (category: ToolCategory): Tool[] => {
  return TOOLS_DATA.filter(tool => tool.category === category);
};

// 搜索工具
export const searchTools = (query: string): Tool[] => {
  const lowercaseQuery = query.toLowerCase();
  return TOOLS_DATA.filter(tool => 
    tool.name.toLowerCase().includes(lowercaseQuery) ||
    tool.description.toLowerCase().includes(lowercaseQuery) ||
    tool.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  );
};

// 获取热门工具
export const getPopularTools = (): Tool[] => {
  return [...TOOLS_DATA].sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0)).slice(0, 8);
};