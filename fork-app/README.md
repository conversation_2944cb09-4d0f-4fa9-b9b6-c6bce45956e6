# ToolFork 应用

这是 ToolFork 项目的主应用，基于 [Next.js](https://nextjs.org) 构建的现代化在线工具聚合平台。

## 技术栈

- **Next.js 15.5.0** - React 全栈框架
- **React 19.1.0** - 用户界面库
- **TypeScript 5.x** - 类型安全的 JavaScript
- **Tailwind CSS 4.x** - 原子化 CSS 框架
- **Lucide React** - 现代图标库

## 快速开始

### 环境要求
- Node.js 18.0 或更高版本
- pnpm 8.0 或更高版本（推荐）

### 安装和运行

1. **安装依赖**
```bash
pnpm install
```

2. **启动开发服务器**
```bash
pnpm dev
```

3. **访问应用**
打开浏览器访问 [http://localhost:3000](http://localhost:3000)

### 其他命令

```bash
# 构建生产版本
pnpm build

# 启动生产服务器
pnpm start

# 代码检查
pnpm lint
```

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   ├── page.tsx           # 首页
│   └── tool/              # 工具页面
├── components/            # React 组件
│   ├── layout/           # 布局组件
│   ├── tools/            # 工具相关组件
│   └── ui/               # 基础 UI 组件
├── data/                 # 数据定义
│   └── tools.ts          # 工具数据配置
├── hooks/                # React Hooks
├── lib/                  # 工具函数
└── types/                # TypeScript 类型定义
```

## 开发指南

### 添加新工具

1. 在 `src/data/tools.ts` 中添加工具定义
2. 在 `src/app/tool/[tool-id]/` 创建工具页面
3. 实现工具的具体功能

### 代码规范

- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint 配置的代码规范
- 组件名使用 PascalCase
- 文件名使用 kebab-case

## 了解更多

- [项目主 README](../README.md) - 查看完整的项目文档
- [Next.js 文档](https://nextjs.org/docs) - 学习 Next.js 特性和 API
- [React 文档](https://react.dev/) - 学习 React 开发
- [Tailwind CSS 文档](https://tailwindcss.com/docs) - 学习样式框架
