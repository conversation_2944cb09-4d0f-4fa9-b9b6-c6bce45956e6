/**
 * Pandoc Web Worker - 基于 vert.sh 的实现
 * 使用 WASI Shim 运行原生 Pandoc WASM
 */

// 导入 WASI Shim
importScripts('https://unpkg.com/@bjorn3/browser_wasi_shim@0.4.2/dist/browserWasiShim.min.js');

let wasmModule = null;
let initialized = false;

// 格式映射
const FORMAT_MAP = {
  'md': 'markdown',
  'markdown': 'markdown',
  'doc': 'docx',
  'docx': 'docx',
  'html': 'html',
  'htm': 'html',
  'rtf': 'rtf',
  'csv': 'csv',
  'tsv': 'tsv',
  'json': 'json',
  'rst': 'rst',
  'epub': 'epub',
  'odt': 'odt',
  'tex': 'latex',
  'latex': 'latex',
  'txt': 'plain',
  'docbook': 'docbook'
};

// 初始化 WASM
async function initializeWasm() {
  if (initialized) return;
  
  try {
    console.log('Loading Pandoc WASM (vert.sh version)...');
    const response = await fetch('/wasm/pandoc-vert.wasm');
    wasmModule = await response.arrayBuffer();
    initialized = true;
    console.log('Pandoc WASM loaded successfully (50MB)');
  } catch (error) {
    console.error('Failed to load WASM:', error);
    throw error;
  }
}

// 执行 Pandoc 转换
async function runPandoc(args_str, in_data, in_name, out_ext) {
  if (!wasmModule) throw new Error("WASM not loaded");
  
  let stderr = "";
  const args = ["pandoc.wasm", "+RTS", "-H64m", "-RTS"];
  const env = [];
  
  // 创建虚拟文件系统
  const BrowserWASIShim = self.BrowserWASIShim || globalThis.BrowserWASIShim;
  if (!BrowserWASIShim) {
    throw new Error('WASI Shim not loaded');
  }
  
  const in_file = new BrowserWASIShim.File(in_data, { readonly: true });
  const out_file = new BrowserWASIShim.File(new Uint8Array(), { readonly: false });
  
  const map = new Map([
    ["in", in_file],
    ["out", out_file]
  ]);
  
  const root = new BrowserWASIShim.PreopenDirectory("/", map);
  
  const fds = [
    new BrowserWASIShim.OpenFile(
      new BrowserWASIShim.File(new Uint8Array(), { readonly: true })
    ),
    BrowserWASIShim.ConsoleStdout.lineBuffered((msg) => {
      console.log(`[WASI stdout] ${msg}`);
    }),
    BrowserWASIShim.ConsoleStdout.lineBuffered((msg) => {
      console.warn(`[WASI stderr] ${msg}`);
      stderr += msg + "\n";
    }),
    root,
    new BrowserWASIShim.PreopenDirectory("/tmp", new Map())
  ];
  
  const wasi = new BrowserWASIShim.WASI(args, env, fds, { debug: false });
  
  // 实例化 WASM
  const { instance } = await WebAssembly.instantiate(wasmModule, {
    wasi_snapshot_preview1: wasi.wasiImport
  });
  
  wasi.initialize(instance);
  instance.exports.__wasm_call_ctors();
  
  function memory_data_view() {
    return new DataView(instance.exports.memory.buffer);
  }
  
  // 设置参数
  const argc_ptr = instance.exports.malloc(4);
  memory_data_view().setUint32(argc_ptr, args.length, true);
  const argv = instance.exports.malloc(4 * (args.length + 1));
  
  for (let i = 0; i < args.length; ++i) {
    const arg = instance.exports.malloc(args[i].length + 1);
    new TextEncoder().encodeInto(
      args[i],
      new Uint8Array(instance.exports.memory.buffer, arg, args[i].length)
    );
    memory_data_view().setUint8(arg + args[i].length, 0);
    memory_data_view().setUint32(argv + 4 * i, arg, true);
  }
  
  memory_data_view().setUint32(argv + 4 * args.length, 0, true);
  const argv_ptr = instance.exports.malloc(4);
  memory_data_view().setUint32(argv_ptr, argv, true);
  
  instance.exports.hs_init_with_rtsopts(argc_ptr, argv_ptr);
  
  // 设置转换参数
  const args_ptr = instance.exports.malloc(args_str.length);
  new TextEncoder().encodeInto(
    args_str,
    new Uint8Array(instance.exports.memory.buffer, args_ptr, args_str.length)
  );
  
  // 执行转换
  instance.exports.wasm_main(args_ptr, args_str.length);
  
  return [out_file.data, stderr];
}

// 转换文档
async function convertDocument(inputBuffer, inputFormat, outputFormat, options = {}) {
  if (!initialized) {
    await initializeWasm();
  }
  
  try {
    const inputData = new Uint8Array(inputBuffer);
    const mappedInputFormat = FORMAT_MAP[inputFormat] || inputFormat;
    const mappedOutputFormat = FORMAT_MAP[outputFormat] || outputFormat;
    
    console.log(`Converting from ${mappedInputFormat} to ${mappedOutputFormat}`);
    
    // 准备转换参数
    const args = `-f ${mappedInputFormat} -t ${mappedOutputFormat} --extract-media=.`;
    
    // 执行转换
    const [result, stderr] = await runPandoc(args, inputData, 'input', outputFormat);
    
    // 检查错误
    if (result.length === 0 && stderr) {
      console.error('Pandoc stderr:', stderr);
      
      // 解析错误类型
      if (stderr.includes('PandocUnknownReaderError')) {
        throw new Error(`${inputFormat} 不是支持的输入格式`);
      }
      if (stderr.includes('PandocUnknownWriterError')) {
        throw new Error(`${outputFormat} 不是支持的输出格式`);
      }
      if (stderr.includes('PandocParseError')) {
        throw new Error(`解析文件失败：文件可能已损坏或格式不正确`);
      }
      
      throw new Error(stderr || '转换失败');
    }
    
    return result.buffer;
  } catch (error) {
    console.error('Conversion error:', error);
    throw error;
  }
}

// 处理消息
self.onmessage = async (event) => {
  const { type, id, input, inputFormat, outputFormat, options } = event.data;
  
  try {
    switch (type) {
      case 'convert':
        // 初始化
        if (!initialized) {
          self.postMessage({ type: 'progress', progress: 10 });
          await initializeWasm();
          self.postMessage({ type: 'progress', progress: 30 });
        }
        
        // 执行转换
        self.postMessage({ type: 'progress', progress: 50 });
        const result = await convertDocument(
          input,
          inputFormat,
          outputFormat,
          options
        );
        self.postMessage({ type: 'progress', progress: 90 });
        
        // 发送结果
        self.postMessage({
          type: 'complete',
          id,
          data: result
        });
        self.postMessage({ type: 'progress', progress: 100 });
        break;
        
      case 'initialize':
        await initializeWasm();
        self.postMessage({
          type: 'initialized',
          id
        });
        break;
        
      default:
        throw new Error(`未知的消息类型: ${type}`);
    }
  } catch (error) {
    console.error('Worker error:', error);
    self.postMessage({
      type: 'error',
      id,
      error: error.message || '转换失败'
    });
  }
};

// Worker 就绪
console.log('Pandoc worker (vert.sh version) ready');
self.postMessage({ type: 'ready' });