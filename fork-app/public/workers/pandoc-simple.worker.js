/**
 * 简化版 Pandoc Web Worker
 * 用于测试和基本转换
 */

// 存储初始化状态
let initialized = false;

// 格式映射
const FORMAT_MAP = {
  'md': 'markdown',
  'markdown': 'markdown',
  'html': 'html',
  'txt': 'plain',
  'json': 'json'
};

// 模拟初始化
async function initializePandoc() {
  if (initialized) return;
  
  console.log('Initializing simple Pandoc worker...');
  
  // 模拟初始化延迟
  await new Promise(resolve => setTimeout(resolve, 100));
  
  initialized = true;
  console.log('Simple Pandoc worker initialized');
}

// 简单的文本转换（Markdown 到 HTML）
function markdownToHtml(markdown) {
  // 非常基础的 Markdown 到 HTML 转换
  let html = markdown;
  
  // 标题
  html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
  html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
  html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');
  
  // 粗体
  html = html.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');
  html = html.replace(/__(.+?)__/g, '<strong>$1</strong>');
  
  // 斜体
  html = html.replace(/\*(.+?)\*/g, '<em>$1</em>');
  html = html.replace(/_(.+?)_/g, '<em>$1</em>');
  
  // 代码块
  html = html.replace(/`(.+?)`/g, '<code>$1</code>');
  
  // 链接
  html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');
  
  // 换行
  html = html.replace(/\n\n/g, '</p><p>');
  html = '<p>' + html + '</p>';
  
  return html;
}

// 转换文档
async function convertDocument(inputBuffer, inputFormat, outputFormat, options = {}) {
  if (!initialized) {
    await initializePandoc();
  }
  
  try {
    const decoder = new TextDecoder('utf-8', { fatal: false });
    const inputText = decoder.decode(new Uint8Array(inputBuffer));
    
    if (!inputText || inputText.trim().length === 0) {
      throw new Error('输入文件为空');
    }
    
    let result = inputText;
    
    // 简单的格式转换
    if (inputFormat === 'md' || inputFormat === 'markdown') {
      if (outputFormat === 'html') {
        result = markdownToHtml(inputText);
      }
    }
    
    // 如果没有实际转换，添加说明
    if (result === inputText && inputFormat !== outputFormat) {
      result = `<!-- 简化版转换器：从 ${inputFormat} 到 ${outputFormat} -->\n` + result;
    }
    
    const encoder = new TextEncoder();
    return encoder.encode(result).buffer;
  } catch (error) {
    console.error('Conversion error:', error);
    throw error;
  }
}

// 处理消息
self.onmessage = async (event) => {
  const { type, id, input, inputFormat, outputFormat, options } = event.data;
  
  console.log('Worker received message:', type);
  
  try {
    switch (type) {
      case 'convert':
        // 初始化
        if (!initialized) {
          self.postMessage({ type: 'progress', progress: 10 });
          await initializePandoc();
          self.postMessage({ type: 'progress', progress: 30 });
        }
        
        // 执行转换
        self.postMessage({ type: 'progress', progress: 50 });
        const result = await convertDocument(
          input,
          inputFormat,
          outputFormat,
          options
        );
        self.postMessage({ type: 'progress', progress: 90 });
        
        // 发送结果
        self.postMessage({
          type: 'complete',
          id,
          data: result
        });
        self.postMessage({ type: 'progress', progress: 100 });
        break;
        
      case 'initialize':
        await initializePandoc();
        self.postMessage({
          type: 'initialized',
          id
        });
        break;
        
      default:
        throw new Error(`未知的消息类型: ${type}`);
    }
  } catch (error) {
    console.error('Worker error:', error);
    self.postMessage({
      type: 'error',
      id,
      error: error.message || '转换失败'
    });
  }
};

// Worker 就绪
console.log('Simple Pandoc worker ready');
self.postMessage({ type: 'ready' });