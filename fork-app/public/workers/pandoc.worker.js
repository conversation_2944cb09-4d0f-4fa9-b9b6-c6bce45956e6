/**
 * Pandoc Web Worker
 * 处理文档转换任务
 */

// 导入 pandoc-wasm (会设置 globalThis.Pandoc)
importScripts('/wasm/pandoc.js');

// 存储Pandoc实例
let pandocInstance = null;
let initialized = false;

// 格式映射
const FORMAT_MAP = {
  // 输入格式
  'docx': 'docx',
  'doc': 'doc', 
  'md': 'markdown',
  'markdown': 'markdown',
  'html': 'html',
  'htm': 'html',
  'rtf': 'rtf',
  'csv': 'csv',
  'tsv': 'tsv',
  'json': 'json',
  'rst': 'rst',
  'epub': 'epub',
  'odt': 'odt',
  'tex': 'latex',
  'latex': 'latex',
  'txt': 'plain',
  
  // 输出格式特殊处理
  'pdf': 'html'  // PDF 在浏览器中不支持，转为 HTML
};

// 初始化Pandoc WASM
async function initializePandoc() {
  if (initialized) return;

  try {
    console.log('Initializing Pandoc WASM...');

    // 检查 Pandoc 是否可用 (通过 globalThis)
    const PandocClass = globalThis.Pandoc || self.Pandoc || Pandoc;
    if (!PandocClass) {
      throw new Error('Pandoc WASM 库未正确加载');
    }

    // 创建 Pandoc 实例
    const pandoc = new PandocClass();

    // 初始化（会自动下载 WASM 和数据文件）
    pandocInstance = await pandoc.init();

    initialized = true;
    console.log('Pandoc WASM initialized successfully');

    // 获取版本信息
    try {
      const version = await pandocInstance.getVersion();
      console.log('Pandoc version:', version);
    } catch (versionError) {
      console.log('Could not get Pandoc version:', versionError);
    }
  } catch (error) {
    console.error('Failed to initialize Pandoc:', error);
    throw new Error(`Pandoc 初始化失败: ${error.message || error}`);
  }
}

// 转换文档
async function convertDocument(inputBuffer, inputFormat, outputFormat, options = {}) {
  if (!initialized) {
    await initializePandoc();
  }

  try {
    // 映射格式名称
    const mappedInputFormat = FORMAT_MAP[inputFormat] || inputFormat;
    let mappedOutputFormat = FORMAT_MAP[outputFormat] || outputFormat;
    
    console.log(`Converting from ${mappedInputFormat} to ${mappedOutputFormat}`);
    
    // 将 ArrayBuffer 转换为字符串或保持二进制
    let inputText;
    const inputData = new Uint8Array(inputBuffer);
    
    // 二进制格式需要特殊处理
    if (['docx', 'doc', 'epub', 'odt'].includes(inputFormat)) {
      // 对于二进制格式，需要以 base64 编码传递
      try {
        // 尝试转换为 base64（对于大文件可能失败）
        let base64;
        if (inputData.length > 50 * 1024 * 1024) { // 50MB 限制
          throw new Error('文件太大，无法在浏览器中处理');
        }
        
        // 分块转换以避免堆栈溢出
        const chunkSize = 8192;
        let base64Parts = [];
        for (let i = 0; i < inputData.length; i += chunkSize) {
          const chunk = inputData.slice(i, i + chunkSize);
          base64Parts.push(btoa(String.fromCharCode.apply(null, chunk)));
        }
        base64 = base64Parts.join('');
        
        // 创建临时文件映射
        const filename = `input.${inputFormat}`;
        const files = {};
        files[filename] = base64;
        
        // 使用文件引用作为输入
        inputText = `[Document: ${filename}]`;
        
        // 运行 Pandoc
        const result = await pandocInstance.run({
          text: inputText,
          options: {
            from: mappedInputFormat,
            to: mappedOutputFormat,
            'embed-resources': true,
            ...options
          },
          files: files
        });
        
        // 返回结果
        const encoder = new TextEncoder();
        return encoder.encode(result).buffer;
      } catch (error) {
        console.error('处理二进制格式失败:', error);
        throw new Error(`无法处理 ${inputFormat} 格式: ${error.message}`);
      }
    } else {
      // 文本格式直接解码
      const decoder = new TextDecoder('utf-8', { fatal: false });
      inputText = decoder.decode(inputData);
      
      // 检查是否为空或无效
      if (!inputText || inputText.trim().length === 0) {
        throw new Error('输入文件为空或无法解码');
      }
    }
    
    // 构建Pandoc参数
    const pandocParams = {
      text: inputText,
      options: {
        from: mappedInputFormat,
        to: mappedOutputFormat,
        'embed-resources': true,
        'standalone': true,
        ...options
      }
    };
    
    // 特殊处理PDF输出
    if (outputFormat === 'pdf') {
      // PDF需要LaTeX，浏览器不支持，改为生成独立HTML
      pandocParams.options.to = 'html';
      pandocParams.options['self-contained'] = true;
    }
    
    // 执行转换
    console.log('Running Pandoc with params:', {
      textLength: pandocParams.text.length,
      from: pandocParams.options.from,
      to: pandocParams.options.to
    });
    
    let result;
    try {
      result = await pandocInstance.run(pandocParams);
    } catch (runError) {
      console.error('Pandoc run error:', runError);
      
      // 尝试简化选项重试
      if (runError.message && runError.message.includes('Unknown')) {
        console.log('尝试使用简化选项重试...');
        const simplifiedParams = {
          text: inputText,
          options: {
            from: mappedInputFormat,
            to: mappedOutputFormat
          }
        };
        result = await pandocInstance.run(simplifiedParams);
      } else {
        throw runError;
      }
    }
    
    // 检查结果
    if (!result) {
      throw new Error('转换结果为空');
    }
    
    // 将结果转换为 ArrayBuffer
    const encoder = new TextEncoder();
    const outputBuffer = encoder.encode(result).buffer;
    
    console.log('转换成功，输出大小:', outputBuffer.byteLength);
    return outputBuffer;
  } catch (error) {
    console.error('Document conversion failed:', error);
    
    // 如果转换失败，返回原始文本
    if (error.message && error.message.includes('Unknown')) {
      const decoder = new TextDecoder();
      const text = decoder.decode(new Uint8Array(inputBuffer));
      const encoder = new TextEncoder();
      return encoder.encode(`转换失败: ${error.message}\n\n原始内容:\n${text}`).buffer;
    }
    
    throw error;
  }
}

// 处理消息
self.onmessage = async (event) => {
  const { type, id, input, inputFormat, outputFormat, options } = event.data;

  try {
    switch (type) {
      case 'convert':
        // 初始化（如果需要）
        if (!initialized) {
          self.postMessage({ type: 'progress', progress: 10 });
          await initializePandoc();
          self.postMessage({ type: 'progress', progress: 30 });
        }

        // 执行转换
        self.postMessage({ type: 'progress', progress: 50 });
        const result = await convertDocument(
          input,
          inputFormat,
          outputFormat,
          options
        );
        self.postMessage({ type: 'progress', progress: 90 });

        // 发送完成消息
        self.postMessage({
          type: 'complete',
          id,
          data: result
        });
        self.postMessage({ type: 'progress', progress: 100 });
        break;

      case 'initialize':
        // 仅初始化
        await initializePandoc();
        self.postMessage({
          type: 'initialized',
          id
        });
        break;

      default:
        throw new Error(`未知的消息类型: ${type}`);
    }
  } catch (error) {
    // 发送错误消息
    console.error('Worker error:', error);

    // 更详细的错误信息
    let errorMessage = '转换失败';

    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else if (error && typeof error === 'object') {
      errorMessage = error.message || error.error || JSON.stringify(error) || '未知错误';
    }

    // 如果错误信息为空，提供默认信息
    if (!errorMessage || errorMessage === '{}' || errorMessage === '[object Object]') {
      errorMessage = 'Pandoc WASM 初始化或转换失败，请检查文件格式是否支持';
    }

    self.postMessage({
      type: 'error',
      id,
      error: errorMessage
    });
  }
};

// Worker准备就绪
console.log('Pandoc worker ready');
self.postMessage({ type: 'ready' });