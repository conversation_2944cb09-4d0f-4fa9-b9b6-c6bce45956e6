# WASM 模块目录

此目录用于存放WebAssembly模块文件。

## 需要下载的WASM文件

### 1. Pandoc WASM (pandoc.wasm)
- **大小**: 约15MB
- **功能**: 文档格式转换
- **下载方式**: 
  - 从Pandoc WASM项目获取
  - 或使用Emscripten编译Pandoc源码

### 2. ImageMagick WASM (magick.wasm)
- **大小**: 约8MB
- **功能**: 图像处理和转换
- **下载选项**:
  - [WASM-ImageMagick](https://github.com/KnicKnic/WASM-ImageMagick)
  - [magick-wasm](https://github.com/dlemstra/magick-wasm)
  - 从npm包 `wasm-imagemagick` 提取

### 3. FFmpeg WASM (ffmpeg.wasm) 
- **大小**: 约25MB
- **功能**: 音视频处理
- **下载选项**:
  - [ffmpeg.wasm](https://github.com/ffmpegwasm/ffmpeg.wasm)
  - 从npm包 `@ffmpeg/ffmpeg` 提取

## 获取方法

### 方法1: 从NPM包提取

```bash
# 安装npm包
npm install wasm-imagemagick @ffmpeg/ffmpeg

# 文件通常位于:
# node_modules/wasm-imagemagick/dist/magick.wasm
# node_modules/@ffmpeg/core/dist/ffmpeg-core.wasm
```

### 方法2: 从CDN下载

```bash
# ImageMagick
wget https://unpkg.com/wasm-imagemagick@latest/dist/magick.wasm

# FFmpeg
wget https://unpkg.com/@ffmpeg/core@0.12.4/dist/ffmpeg-core.wasm
```

### 方法3: 自行编译

使用Emscripten将原生代码编译为WebAssembly。

## 注意事项

1. **文件大小**: WASM文件较大，首次加载需要时间
2. **缓存策略**: 已实现IndexedDB缓存，减少重复加载
3. **兼容性**: 需要浏览器支持WebAssembly
4. **许可证**: 请遵守各个项目的开源许可证

## 占位文件

在开发环境中，如果暂时没有WASM文件，工具会显示模拟功能。要启用完整功能，请下载相应的WASM文件并放置在此目录中。