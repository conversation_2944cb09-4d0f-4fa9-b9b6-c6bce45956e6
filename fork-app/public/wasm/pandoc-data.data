-- This Lua script is run every time the Lua interpreter is started when running
-- a Lua filter. It can be customized to load additional modules or to alter the
-- default modules.
# This script enables bash autocompletion for pandoc.  To enable
# bash completion, add this to your .bashrc:
# eval "$(pandoc --bash-completion)"

_pandoc()
{
    local cur prev opts lastc informats outformats highlight_styles datafiles
    COMPREPLY=()
    cur="${COMP_WORDS[COMP_CWORD]}"
    prev="${COMP_WORDS[COMP_CWORD-1]}"

    # These should be filled in by pandoc:
    opts="%s"
    informats="%s"
    outformats="%s"
    highlight_styles="%s"
    datafiles="%s"

    case "${prev}" in
         --from|-f|--read|-r)
             COMPREPLY=( $(compgen -W "${informats}" -- ${cur}) )
             return 0
             ;;
         --to|-t|--write|-w|-D|--print-default-template)
             COMPREPLY=( $(compgen -W "${outformats}" -- ${cur}) )
             return 0
             ;;
         --email-obfuscation)
             COMPREPLY=( $(compgen -W "references javascript none" -- ${cur}) )
             return 0
             ;;
         --ipynb-output)
             COMPREPLY=( $(compgen -W "all none best" -- ${cur}) )
             return 0
             ;;
         --pdf-engine)
             COMPREPLY=( $(compgen -W "pdflatex lualatex xelatex latexmk tectonic wkhtmltopdf weasyprint prince context pdfroff" -- ${cur}) )
             return 0
             ;;
         --print-default-data-file)
             COMPREPLY=( $(compgen -W "${datafiles}" -- ${cur}) )
             return 0
             ;;
         --wrap)
             COMPREPLY=( $(compgen -W "auto none preserve" -- ${cur}) )
             return 0
             ;;
         --track-changes)
             COMPREPLY=( $(compgen -W "accept reject all" -- ${cur}) )
             return 0
             ;;
         --reference-location)
             COMPREPLY=( $(compgen -W "block section document" -- ${cur}) )
             return 0
             ;;
         --top-level-division)
             COMPREPLY=( $(compgen -W "section chapter part" -- ${cur}) )
             return 0
             ;;
         --highlight-style|--print-highlight-style)
             COMPREPLY=( $(compgen -W "${highlight_styles}" -- ${cur}) )
             return 0
             ;;
         --eol)
             COMPREPLY=( $(compgen -W "crlf lf native" -- ${cur}) )
             return 0
             ;;
         --markdown-headings)
             COMPREPLY=( $(compgen -W "setext atx" -- ${cur}) )
             return 0
             ;;
         *)
             ;;
    esac

    case "${cur}" in
         -*)
             COMPREPLY=( $(compgen -W "${opts}" -- ${cur}) )
             return 0
             ;;
         *)
             local IFS=$'\n'
             COMPREPLY=( $(compgen -X '' -f "${cur}") )
             return 0
             ;;
    esac

}

complete -o filenames -o bashdefault -F _pandoc pandoc
<?xml version="1.0" encoding="UTF-8"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types"><Default Extension="xml" ContentType="application/xml" /><Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml" /><Override PartName="/word/webSettings.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.webSettings+xml" /><Override PartName="/word/numbering.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml" /><Override PartName="/word/settings.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml" /><Override PartName="/word/theme/theme1.xml" ContentType="application/vnd.openxmlformats-officedocument.theme+xml" /><Override PartName="/word/fontTable.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.fontTable+xml" /><Override PartName="/docProps/app.xml" ContentType="application/vnd.openxmlformats-officedocument.extended-properties+xml" /><Override PartName="/docProps/core.xml" ContentType="application/vnd.openxmlformats-package.core-properties+xml" /><Override PartName="/docProps/custom.xml" ContentType="application/vnd.openxmlformats-officedocument.custom-properties+xml"/><Override PartName="/word/styles.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml" /><Override PartName="/word/document.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml" /><Override PartName="/word/comments.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml" /><Override PartName="/word/footnotes.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml" /></Types>
<?xml version="1.0" encoding="UTF-8"?>
<w:fonts xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
&#9;<w:font w:name="Symbol">
&#9;&#9;<w:panose1 w:val="02000500000000000000" />
&#9;&#9;<w:charset w:val="02" />
&#9;&#9;<w:family w:val="auto" />
&#9;&#9;<w:pitch w:val="variable" />
&#9;&#9;<w:sig w:usb0="00000000" w:usb1="00000000" w:usb2="00010000" w:usb3="00000000" w:csb0="80000000" w:csb1="00000000" />
&#9;</w:font>
&#9;<w:font w:name="Times New Roman">
&#9;&#9;<w:panose1 w:val="02020603050405020304" />
&#9;&#9;<w:charset w:val="00" />
&#9;&#9;<w:family w:val="auto" />
&#9;&#9;<w:pitch w:val="variable" />
&#9;&#9;<w:sig w:usb0="00000003" w:usb1="00000000" w:usb2="00000000" w:usb3="00000000" w:csb0="00000001" w:csb1="00000000" />
&#9;</w:font>
&#9;<w:font w:name="Courier New">
&#9;&#9;<w:panose1 w:val="02070309020205020404" />
&#9;&#9;<w:charset w:val="00" />
&#9;&#9;<w:family w:val="auto" />
&#9;&#9;<w:pitch w:val="variable" />
&#9;&#9;<w:sig w:usb0="00000003" w:usb1="00000000" w:usb2="00000000" w:usb3="00000000" w:csb0="00000001" w:csb1="00000000" />
&#9;</w:font>
&#9;<w:font w:name="Wingdings">
&#9;&#9;<w:panose1 w:val="05020102010804080708" />
&#9;&#9;<w:charset w:val="02" />
&#9;&#9;<w:family w:val="auto" />
&#9;&#9;<w:pitch w:val="variable" />
&#9;&#9;<w:sig w:usb0="00000000" w:usb1="00000000" w:usb2="00010000" w:usb3="00000000" w:csb0="80000000" w:csb1="00000000" />
&#9;</w:font>
&#9;<w:font w:name="Cambria">
&#9;&#9;<w:panose1 w:val="02040503050406030204" />
&#9;&#9;<w:charset w:val="00" />
&#9;&#9;<w:family w:val="auto" />
&#9;&#9;<w:pitch w:val="variable" />
&#9;&#9;<w:sig w:usb0="00000003" w:usb1="00000000" w:usb2="00000000" w:usb3="00000000" w:csb0="00000001" w:csb1="00000000" />
&#9;</w:font>
&#9;<w:font w:name="Calibri">
&#9;&#9;<w:panose1 w:val="020F0502020204030204" />
&#9;&#9;<w:charset w:val="00" />
&#9;&#9;<w:family w:val="auto" />
&#9;&#9;<w:pitch w:val="variable" />
&#9;&#9;<w:sig w:usb0="00000003" w:usb1="00000000" w:usb2="00000000" w:usb3="00000000" w:csb0="00000001" w:csb1="00000000" />
&#9;</w:font>
&#9;<w:font w:name="Arial">
&#9;&#9;<w:panose1 w:val="020B0604020202020204" />
&#9;&#9;<w:charset w:val="00" />
&#9;&#9;<w:family w:val="auto" />
&#9;&#9;<w:pitch w:val="variable" />
&#9;&#9;<w:sig w:usb0="00000003" w:usb1="00000000" w:usb2="00000000" w:usb3="00000000" w:csb0="00000001" w:csb1="00000000" />
&#9;</w:font>
</w:fonts><?xml version="1.0" encoding="UTF-8"?>
<w:styles xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
  <w:docDefaults>
    <w:rPrDefault>
      <w:rPr>
        <w:rFonts w:asciiTheme="minorHAnsi" w:eastAsiaTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi" />
        <w:sz w:val="24" />
        <w:szCs w:val="24" />
        <w:lang w:val="en-US" w:eastAsia="en-US" w:bidi="ar-SA" />
      </w:rPr>
    </w:rPrDefault>
    <w:pPrDefault>
      <w:pPr>
        <w:spacing w:after="200" />
      </w:pPr>
    </w:pPrDefault>
  </w:docDefaults>
  <w:latentStyles w:defLockedState="0" w:defUIPriority="0" w:defSemiHidden="0" w:defUnhideWhenUsed="0" w:defQFormat="0" w:count="276" />
  <w:style w:type="paragraph" w:default="1" w:styleId="Normal">
    <w:name w:val="Normal" />
    <w:qFormat />
  </w:style>
    <w:style w:type="paragraph" w:styleId="BodyText">
    <w:name w:val="Body Text" />
    <w:basedOn w:val="Normal" />
    <w:link w:val="BodyTextChar" />
    <w:qFormat />
    <w:pPr>
      <w:spacing w:before="180" w:after="180" />
    </w:pPr>
  </w:style>
  <w:style w:type="paragraph" w:customStyle="1" w:styleId="FirstParagraph">
    <w:name w:val="First Paragraph" />
    <w:basedOn w:val="BodyText" />
    <w:next w:val="BodyText" />
    <w:qFormat />
  </w:style>
  <w:style w:type="paragraph" w:customStyle="1" w:styleId="Compact">
    <w:name w:val="Compact" />
    <w:basedOn w:val="BodyText" />
    <w:qFormat />
    <w:pPr>
      <w:spacing w:before="36" w:after="36" />
    </w:pPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Title">
    <w:name w:val="Title" />
    <w:basedOn w:val="Normal" />
    <w:next w:val="BodyText" />
    <w:qFormat />
    <w:pPr>
      <w:keepNext />
      <w:keepLines />
      <w:spacing w:before="480" w:after="240" />
      <w:jc w:val="center" />
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi" />
      <w:b />
      <w:bCs />
      <w:color w:val="345A8A" w:themeColor="accent1" w:themeShade="B5" />
      <w:sz w:val="36" />
      <w:szCs w:val="36" />
    </w:rPr>
  </w:style>
 <w:style w:type="paragraph" w:styleId="Subtitle">
    <w:name w:val="Subtitle" />
    <w:basedOn w:val="Title" />
    <w:next w:val="BodyText" />
    <w:qFormat />
    <w:pPr>
      <w:keepNext />
      <w:keepLines />
      <w:spacing w:before="240" w:after="240" />
      <w:jc w:val="center" />
    </w:pPr>
    <w:rPr>
      <w:sz w:val="30" />
      <w:szCs w:val="30" />
    </w:rPr>
 </w:style>
  <w:style w:type="paragraph" w:customStyle="1" w:styleId="Author">
    <w:name w:val="Author" />
    <w:next w:val="BodyText" />
    <w:qFormat />
    <w:pPr>
      <w:keepNext />
      <w:keepLines />
      <w:jc w:val="center" />
    </w:pPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Date">
    <w:name w:val="Date" />
    <w:next w:val="BodyText" />
    <w:qFormat />
    <w:pPr>
      <w:keepNext />
      <w:keepLines />
      <w:jc w:val="center" />
    </w:pPr>
  </w:style>
  <w:style w:type="paragraph" w:customStyle="1" w:styleId="AbstractTitle">
    <w:name w:val="Abstract Title" />
    <w:basedOn w:val="Normal" />
    <w:next w:val="Abstract" />
    <w:qFormat />
    <w:pPr>
      <w:keepNext />
      <w:keepLines />
      <w:spacing w:before="300" w:after="0" />
      <w:jc w:val="center" />
    </w:pPr>
    <w:rPr>
      <w:b />
      <w:color w:val="345A8A" />
      <w:sz w:val="20" />
      <w:szCs w:val="20" />
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:customStyle="1" w:styleId="Abstract">
    <w:name w:val="Abstract" />
    <w:basedOn w:val="Normal" />
    <w:next w:val="BodyText" />
    <w:qFormat />
    <w:pPr>
      <w:keepNext />
      <w:keepLines />
      <w:spacing w:before="100" w:after="300" />
    </w:pPr>
    <w:rPr>
      <w:sz w:val="20" />
      <w:szCs w:val="20" />
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Bibliography">
    <w:name w:val="Bibliography" />
    <w:basedOn w:val="Normal" />
    <w:next w:val="Bibliography" />
    <w:qFormat />
    <w:pPr />
    <w:rPr />
  </w:style>
  <w:style w:type="paragraph" w:styleId="Heading1">
    <w:name w:val="Heading 1" />
    <w:basedOn w:val="Normal" />
    <w:next w:val="BodyText" />
    <w:uiPriority w:val="9" />
    <w:qFormat />
    <w:pPr>
      <w:keepNext />
      <w:keepLines />
      <w:spacing w:before="480" w:after="0" />
      <w:outlineLvl w:val="0" />
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi" />
      <w:b />
      <w:bCs />
      <w:color w:val="4F81BD" w:themeColor="accent1" />
      <w:sz w:val="32" />
      <w:szCs w:val="32" />
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Heading2">
    <w:name w:val="Heading 2" />
    <w:basedOn w:val="Normal" />
    <w:next w:val="BodyText" />
    <w:uiPriority w:val="9" />
    <w:unhideWhenUsed />
    <w:qFormat />
    <w:pPr>
      <w:keepNext />
      <w:keepLines />
      <w:spacing w:before="200" w:after="0" />
      <w:outlineLvl w:val="1" />
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi" />
      <w:b />
      <w:bCs />
      <w:color w:val="4F81BD" w:themeColor="accent1" />
      <w:sz w:val="28" />
      <w:szCs w:val="28" />
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Heading3">
    <w:name w:val="Heading 3" />
    <w:basedOn w:val="Normal" />
    <w:next w:val="BodyText" />
    <w:uiPriority w:val="9" />
    <w:unhideWhenUsed />
    <w:qFormat />
    <w:pPr>
      <w:keepNext />
      <w:keepLines />
      <w:spacing w:before="200" w:after="0" />
      <w:outlineLvl w:val="2" />
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi" />
      <w:b />
      <w:bCs />
      <w:color w:val="4F81BD" w:themeColor="accent1" />
      <w:sz w:val="24" />
      <w:szCs w:val="24" />
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Heading4">
    <w:name w:val="Heading 4" />
    <w:basedOn w:val="Normal" />
    <w:next w:val="BodyText" />
    <w:uiPriority w:val="9" />
    <w:unhideWhenUsed />
    <w:qFormat />
    <w:pPr>
      <w:keepNext />
      <w:keepLines />
      <w:spacing w:before="200" w:after="0" />
      <w:outlineLvl w:val="3" />
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi" />
      <w:bCs />
      <w:i />
      <w:color w:val="4F81BD" w:themeColor="accent1" />
      <w:sz w:val="24" />
      <w:szCs w:val="24" />
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Heading5">
    <w:name w:val="Heading 5" />
    <w:basedOn w:val="Normal" />
    <w:next w:val="BodyText" />
    <w:uiPriority w:val="9" />
    <w:unhideWhenUsed />
    <w:qFormat />
    <w:pPr>
      <w:keepNext />
      <w:keepLines />
      <w:spacing w:before="200" w:after="0" />
      <w:outlineLvl w:val="4" />
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi" />
      <w:iCs />
      <w:color w:val="4F81BD" w:themeColor="accent1" />
      <w:sz w:val="24" />
      <w:szCs w:val="24" />
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Heading6">
    <w:name w:val="Heading 6" />
    <w:basedOn w:val="Normal" />
    <w:next w:val="BodyText" />
    <w:uiPriority w:val="9" />
    <w:unhideWhenUsed />
    <w:qFormat />
    <w:pPr>
      <w:keepNext />
      <w:keepLines />
      <w:spacing w:before="200" w:after="0" />
      <w:outlineLvl w:val="5" />
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi" />
      <w:color w:val="4F81BD" w:themeColor="accent1" />
      <w:sz w:val="24" />
      <w:szCs w:val="24" />
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Heading7">
    <w:name w:val="Heading 7" />
    <w:basedOn w:val="Normal" />
    <w:next w:val="BodyText" />
    <w:uiPriority w:val="9" />
    <w:unhideWhenUsed />
    <w:qFormat />
    <w:pPr>
      <w:keepNext />
      <w:keepLines />
      <w:spacing w:before="200" w:after="0" />
      <w:outlineLvl w:val="6" />
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi" />
      <w:color w:val="4F81BD" w:themeColor="accent1" />
      <w:sz w:val="24" />
      <w:szCs w:val="24" />
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Heading8">
    <w:name w:val="Heading 8" />
    <w:basedOn w:val="Normal" />
    <w:next w:val="BodyText" />
    <w:uiPriority w:val="9" />
    <w:unhideWhenUsed />
    <w:qFormat />
    <w:pPr>
      <w:keepNext />
      <w:keepLines />
      <w:spacing w:before="200" w:after="0" />
      <w:outlineLvl w:val="7" />
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi" />
      <w:color w:val="4F81BD" w:themeColor="accent1" />
      <w:sz w:val="24" />
      <w:szCs w:val="24" />
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Heading9">
    <w:name w:val="Heading 9" />
    <w:basedOn w:val="Normal" />
    <w:next w:val="BodyText" />
    <w:uiPriority w:val="9" />
    <w:unhideWhenUsed />
    <w:qFormat />
    <w:pPr>
      <w:keepNext />
      <w:keepLines />
      <w:spacing w:before="200" w:after="0" />
      <w:outlineLvl w:val="8" />
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi" />
      <w:color w:val="4F81BD" w:themeColor="accent1" />
      <w:sz w:val="24" />
      <w:szCs w:val="24" />
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="BlockText">
    <w:name w:val="Block Text" />
    <w:basedOn w:val="BodyText" />
    <w:next w:val="BodyText" />
    <w:uiPriority w:val="9" />
    <w:unhideWhenUsed />
    <w:qFormat />
    <w:pPr>
      <w:spacing w:before="100" w:after="100" />
      <w:ind w:firstLine="0" w:left="480" w:right="480" />
    </w:pPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="FootnoteText">
    <w:name w:val="Footnote Text" />
    <w:basedOn w:val="Normal" />
    <w:next w:val="FootnoteText" />
    <w:uiPriority w:val="9" />
    <w:unhideWhenUsed />
    <w:qFormat />
  </w:style>
  <w:style w:type="paragraph" w:styleId="FootnoteBlockText">
    <w:name w:val="Footnote Block Text" />
    <w:basedOn w:val="Footnote Text" />
    <w:next w:val="Footnote Text" />
    <w:uiPriority w:val="9" />
    <w:unhideWhenUsed />
    <w:qFormat />
    <w:pPr>
      <w:spacing w:before="100" w:after="100" />
      <w:ind w:firstLine="0" w:left="480" w:right="480" />
    </w:pPr>
  </w:style>
  <w:style w:type="character" w:default="1" w:styleId="DefaultParagraphFont">
    <w:name w:val="Default Paragraph Font" />
    <w:semiHidden />
    <w:unhideWhenUsed />
  </w:style>
  <w:style w:type="table" w:default="1" w:styleId="Table">
    <w:name w:val="Table" />
    <w:basedOn w:val="TableNormal" />
    <w:semiHidden />
    <w:unhideWhenUsed />
    <w:qFormat />
    <w:tblPr>
      <w:tblInd w:w="0" w:type="dxa" />
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa" />
        <w:left w:w="108" w:type="dxa" />
        <w:bottom w:w="0" w:type="dxa" />
        <w:right w:w="108" w:type="dxa" />
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:tblPr>
        <w:jc w:val="left"/>
        <w:tblInd w:w="0" w:type="dxa"/>
      </w:tblPr>
      <w:trPr>
        <w:jc w:val="left"/>
      </w:trPr>
      <w:tcPr>
        <w:tcBorders>
          <w:bottom w:val="single"/>
        </w:tcBorders>
        <w:vAlign w:val="bottom"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="paragraph" w:customStyle="1" w:styleId="DefinitionTerm">
    <w:name w:val="Definition Term" />
    <w:basedOn w:val="Normal" />
    <w:next w:val="Definition" />
    <w:pPr>
      <w:keepNext />
      <w:keepLines />
      <w:spacing w:after="0" />
    </w:pPr>
    <w:rPr>
      <w:b />
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:customStyle="1" w:styleId="Definition">
    <w:name w:val="Definition" />
    <w:basedOn w:val="Normal" />
  </w:style>
  <w:style w:type="paragraph" w:styleId="Caption">
    <w:name w:val="Caption" />
    <w:basedOn w:val="Normal" />
    <w:link w:val="BodyTextChar" />
    <w:pPr>
      <w:spacing w:before="0" w:after="120" />
    </w:pPr>
    <w:rPr>
      <w:i />
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:customStyle="1" w:styleId="TableCaption">
    <w:name w:val="Table Caption" />
    <w:basedOn w:val="Caption" />
    <w:pPr>
      <w:keepNext />
    </w:pPr>
  </w:style>
  <w:style w:type="paragraph" w:customStyle="1" w:styleId="ImageCaption">
    <w:name w:val="Image Caption" />
    <w:basedOn w:val="Caption" />
  </w:style>
  <w:style w:type="paragraph" w:customStyle="1" w:styleId="Figure">
    <w:name w:val="Figure" />
    <w:basedOn w:val="Normal" />
  </w:style>
  <w:style w:type="paragraph" w:customStyle="1" w:styleId="CaptionedFigure">
    <w:name w:val="Captioned Figure" />
    <w:basedOn w:val="Figure" />
    <w:pPr>
      <w:keepNext />
    </w:pPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="BodyTextChar">
    <w:name w:val="Body Text Char" />
    <w:basedOn w:val="DefaultParagraphFont" />
    <w:link w:val="BodyText" />
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="VerbatimChar">
    <w:name w:val="Verbatim Char" />
    <w:basedOn w:val="BodyTextChar" />
    <w:rPr>
      <w:rFonts w:ascii="Consolas" w:hAnsi="Consolas" />
      <w:sz w:val="22" />
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="SectionNumber">
    <w:name w:val="Section Number" />
    <w:basedOn w:val="BodyTextChar" />
  </w:style>
  <w:style w:type="character" w:styleId="FootnoteReference">
    <w:name w:val="Footnote Reference" />
    <w:basedOn w:val="BodyTextChar" />
    <w:rPr>
      <w:vertAlign w:val="superscript" />
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:styleId="Hyperlink">
    <w:name w:val="Hyperlink" />
    <w:basedOn w:val="BodyTextChar" />
    <w:rPr>
      <w:color w:val="4F81BD" w:themeColor="accent1" />
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="TOCHeading">
    <w:name w:val="TOC Heading" />
    <w:basedOn w:val="Heading1" />
    <w:next w:val="BodyText" />
    <w:uiPriority w:val="39" />
    <w:unhideWhenUsed />
    <w:qFormat />
    <w:pPr>
      <w:spacing w:before="240" w:line="259" w:lineRule="auto" />
      <w:outlineLvl w:val="9" />
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi" />
      <w:b w:val="0" />
      <w:bCs w:val="0" />
      <w:color w:val="365F91" w:themeColor="accent1" w:themeShade="BF" />
    </w:rPr>
  </w:style>
</w:styles>
<?xml version="1.0" encoding="UTF-8"?>
<ns0:webSettings xmlns:ns0="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
  <ns0:allowPNG />
  <ns0:doNotSaveAsSingleFile />
</ns0:webSettings><?xml version="1.0" encoding="UTF-8"?>
<w:footnotes xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w10="urn:schemas-microsoft-com:office:word" xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:pic="http://schemas.openxmlformats.org/drawingml/2006/picture" xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing"><w:footnote w:type="continuationSeparator" w:id="0"><w:p><w:r><w:continuationSeparator /></w:r></w:p></w:footnote><w:footnote w:type="separator" w:id="-1"><w:p><w:r><w:separator /></w:r></w:p></w:footnote><w:footnote w:id="31"><w:p><w:pPr><w:pStyle w:val="FootnoteText" /></w:pPr><w:r>
  <w:rPr>
    <w:rStyle w:val="FootnoteReference" />
  </w:rPr>
  <w:footnoteRef />
</w:r><w:r><w:t xml:space="preserve"> </w:t></w:r><w:r><w:t xml:space="preserve">Footnote Text.</w:t></w:r></w:p></w:footnote></w:footnotes><?xml version="1.0" encoding="UTF-8"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/numbering" Id="rId1" Target="numbering.xml" /><Relationship Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles" Id="rId2" Target="styles.xml" /><Relationship Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/settings" Id="rId3" Target="settings.xml" /><Relationship Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/webSettings" Id="rId4" Target="webSettings.xml" /><Relationship Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/fontTable" Id="rId5" Target="fontTable.xml" /><Relationship Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme" Id="rId6" Target="theme/theme1.xml" /><Relationship Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/footnotes" Id="rId7" Target="footnotes.xml" /><Relationship Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments" Id="rId8" Target="comments.xml" /><Relationship Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink" Id="rId30" Target="http://example.com" TargetMode="External" /></Relationships>
<?xml version="1.0" encoding="UTF-8"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink" Id="rId30" Target="http://example.com" TargetMode="External" /></Relationships><?xml version="1.0" encoding="UTF-8"?>
<w:settings xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w10="urn:schemas-microsoft-com:office:word" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" xmlns:sl="http://schemas.openxmlformats.org/schemaLibrary/2006/main">
  <w:zoom w:percent="100" />
  <w:embedSystemFonts />
  <w:proofState w:spelling="clean" w:grammar="clean" />
  <w:stylePaneFormatFilter w:val="0004" />
  <w:doNotTrackMoves />
  <w:defaultTabStop w:val="720" />
  <w:drawingGridHorizontalSpacing w:val="360" />
  <w:drawingGridVerticalSpacing w:val="360" />
  <w:displayHorizontalDrawingGridEvery w:val="0" />
  <w:displayVerticalDrawingGridEvery w:val="0" />
  <w:characterSpacingControl w:val="doNotCompress" />
  <w:savePreviewPicture />
  <w:footnotePr>
    <w:footnote w:id="-1" />
    <w:footnote w:id="0" />
  </w:footnotePr>
  <w:rsids>
  </w:rsids>
  <m:mathPr>
    <m:mathFont m:val="Cambria Math" />
    <m:brkBin m:val="before" />
    <m:brkBinSub m:val="--" />
    <m:smallFrac m:val="0" />
    <m:dispDef />
    <m:lMargin m:val="0" />
    <m:rMargin m:val="0" />
    <m:wrapRight />
    <m:intLim m:val="subSup" />
    <m:naryLim m:val="undOvr" />
  </m:mathPr>
  <w:themeFontLang w:val="en-US" />
  <w:clrSchemeMapping w:bg1="light1" w:t1="dark1" w:bg2="light2" w:t2="dark2" w:accent1="accent1" w:accent2="accent2" w:accent3="accent3" w:accent4="accent4" w:accent5="accent5" w:accent6="accent6" w:hyperlink="hyperlink" w:followedHyperlink="followedHyperlink" />
  <w:decimalSymbol w:val="." />
  <w:listSeparator w:val="," />
</w:settings>
<?xml version="1.0" encoding="UTF-8"?>
<w:numbering xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"><w:abstractNum w:abstractNumId="990"><w:nsid w:val="170cd2de" /><w:multiLevelType w:val="multilevel" /><w:lvl w:ilvl="0"><w:numFmt w:val="bullet" /><w:lvlText w:val=" " /><w:lvlJc w:val="left" /><w:pPr><w:tabs><w:tab w:val="num" w:pos="0" /></w:tabs><w:ind w:left="480" w:hanging="480" /></w:pPr></w:lvl><w:lvl w:ilvl="1"><w:numFmt w:val="bullet" /><w:lvlText w:val=" " /><w:lvlJc w:val="left" /><w:pPr><w:tabs><w:tab w:val="num" w:pos="720" /></w:tabs><w:ind w:left="1200" w:hanging="480" /></w:pPr></w:lvl><w:lvl w:ilvl="2"><w:numFmt w:val="bullet" /><w:lvlText w:val=" " /><w:lvlJc w:val="left" /><w:pPr><w:tabs><w:tab w:val="num" w:pos="1440" /></w:tabs><w:ind w:left="1920" w:hanging="480" /></w:pPr></w:lvl><w:lvl w:ilvl="3"><w:numFmt w:val="bullet" /><w:lvlText w:val=" " /><w:lvlJc w:val="left" /><w:pPr><w:tabs><w:tab w:val="num" w:pos="2160" /></w:tabs><w:ind w:left="2640" w:hanging="480" /></w:pPr></w:lvl><w:lvl w:ilvl="4"><w:numFmt w:val="bullet" /><w:lvlText w:val=" " /><w:lvlJc w:val="left" /><w:pPr><w:tabs><w:tab w:val="num" w:pos="2880" /></w:tabs><w:ind w:left="3360" w:hanging="480" /></w:pPr></w:lvl><w:lvl w:ilvl="5"><w:numFmt w:val="bullet" /><w:lvlText w:val=" " /><w:lvlJc w:val="left" /><w:pPr><w:tabs><w:tab w:val="num" w:pos="3600" /></w:tabs><w:ind w:left="4080" w:hanging="480" /></w:pPr></w:lvl><w:lvl w:ilvl="6"><w:numFmt w:val="bullet" /><w:lvlText w:val=" " /><w:lvlJc w:val="left" /><w:pPr><w:tabs><w:tab w:val="num" w:pos="4320" /></w:tabs><w:ind w:left="4800" w:hanging="480" /></w:pPr></w:lvl><w:lvl w:ilvl="7"><w:numFmt w:val="bullet" /><w:lvlText w:val=" " /><w:lvlJc w:val="left" /><w:pPr><w:tabs><w:tab w:val="num" w:pos="5040" /></w:tabs><w:ind w:left="5520" w:hanging="480" /></w:pPr></w:lvl><w:lvl w:ilvl="8"><w:numFmt w:val="bullet" /><w:lvlText w:val=" " /><w:lvlJc w:val="left" /><w:pPr><w:tabs><w:tab w:val="num" w:pos="5760" /></w:tabs><w:ind w:left="6240" w:hanging="480" /></w:pPr></w:lvl></w:abstractNum><w:num w:numId="1000"><w:abstractNumId w:val="990" /></w:num></w:numbering><?xml version="1.0" encoding="utf-8"?>
<w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math"
xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
xmlns:o="urn:schemas-microsoft-com:office:office"
xmlns:v="urn:schemas-microsoft-com:vml"
xmlns:w10="urn:schemas-microsoft-com:office:word"
xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main"
xmlns:pic="http://schemas.openxmlformats.org/drawingml/2006/picture"
xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing">

  <w:body>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="Title" />
      </w:pPr>
      <w:r>
        <w:t xml:space="preserve">
Title
</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="Subtitle" />
      </w:pPr>
      <w:r>
        <w:t xml:space="preserve">
Subtitle
</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="Author" />
      </w:pPr>
      <w:r>
        <w:t xml:space="preserve">
Author
</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="Date" />
      </w:pPr>
      <w:r>
        <w:t xml:space="preserve">
Date
</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="Abstract" />
      </w:pPr>
      <w:r>
        <w:t xml:space="preserve">
Abstract
</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="Heading1" />
      </w:pPr>
      <w:bookmarkStart w:id="21" w:name="heading-1" />
      <w:r>
        <w:t xml:space="preserve">
Heading 1
</w:t>
      </w:r>
      <w:bookmarkEnd w:id="21" />
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="Heading2" />
      </w:pPr>
      <w:bookmarkStart w:id="22" w:name="heading-2" />
      <w:r>
        <w:t xml:space="preserve">
Heading 2
</w:t>
      </w:r>
      <w:bookmarkEnd w:id="22" />
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="Heading3" />
      </w:pPr>
      <w:bookmarkStart w:id="23" w:name="heading-3" />
      <w:r>
        <w:t xml:space="preserve">
Heading 3
</w:t>
      </w:r>
      <w:bookmarkEnd w:id="23" />
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="Heading4" />
      </w:pPr>
      <w:bookmarkStart w:id="24" w:name="heading-4" />
      <w:r>
        <w:t xml:space="preserve">
Heading 4
</w:t>
      </w:r>
      <w:bookmarkEnd w:id="24" />
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="Heading5" />
      </w:pPr>
      <w:bookmarkStart w:id="25" w:name="heading-5" />
      <w:r>
        <w:t xml:space="preserve">
Heading 5
</w:t>
      </w:r>
      <w:bookmarkEnd w:id="25" />
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="Heading6" />
      </w:pPr>
      <w:bookmarkStart w:id="26" w:name="heading-6" />
      <w:r>
        <w:t xml:space="preserve">
Heading 6
</w:t>
      </w:r>
      <w:bookmarkEnd w:id="26" />
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="Heading7" />
      </w:pPr>
      <w:bookmarkStart w:id="27" w:name="heading-7" />
      <w:r>
        <w:t xml:space="preserve">
Heading 7
</w:t>
      </w:r>
      <w:bookmarkEnd w:id="27" />
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="Heading8" />
      </w:pPr>
      <w:bookmarkStart w:id="28" w:name="heading-8" />
      <w:r>
        <w:t xml:space="preserve">
Heading 8
</w:t>
      </w:r>
      <w:bookmarkEnd w:id="28" />
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="Heading9" />
      </w:pPr>
      <w:bookmarkStart w:id="29" w:name="heading-9" />
      <w:r>
        <w:t xml:space="preserve">
Heading 9
</w:t>
      </w:r>
      <w:bookmarkEnd w:id="29" />
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="FirstParagraph" />
      </w:pPr>
      <w:r>
        <w:t xml:space="preserve">
First Paragraph.
</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="BodyText" />
      </w:pPr>
      <w:r>
        <w:t xml:space="preserve">
Body Text. Body Text Char.
</w:t>
      </w:r>
      <w:r>
        <w:t xml:space="preserve">
 
</w:t>
      </w:r>
      <w:r>
        <w:rPr>
          <w:rStyle w:val="VerbatimChar" />
        </w:rPr>
        <w:t xml:space="preserve">
Verbatim Char
</w:t>
      </w:r>
      <w:r>
        <w:t xml:space="preserve">
.
</w:t>
      </w:r>
      <w:r>
        <w:t xml:space="preserve">
 
</w:t>
      </w:r>
      <w:hyperlink r:id="rId30">
        <w:r>
          <w:rPr>
            <w:rStyle w:val="Hyperlink" />
          </w:rPr>
          <w:t xml:space="preserve">
Hyperlink
</w:t>
        </w:r>
      </w:hyperlink>
      <w:r>
        <w:t xml:space="preserve">
.
</w:t>
      </w:r>
      <w:r>
        <w:t xml:space="preserve">
 
</w:t>
      </w:r>
      <w:r>
        <w:t xml:space="preserve">
Footnote.
</w:t>
      </w:r>
      <w:r>
        <w:rPr>
          <w:rStyle w:val="FootnoteReference" />
        </w:rPr>
        <w:footnoteReference w:id="31" />
      </w:r>
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="BlockText" />
      </w:pPr>
      <w:r>
        <w:t xml:space="preserve">
Block Text.
</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="TableCaption" />
      </w:pPr>
      <w:r>
        <w:t xml:space="preserve">
Table caption.
</w:t>
      </w:r>
    </w:p>
    <w:tbl>
      <w:tblPr>
        <w:tblStyle w:val="Table" />
        <w:tblLook w:firstRow="1" />
        <w:tblCaption w:val="Table caption." />
      </w:tblPr>
      <w:tblGrid />
      <w:tr>
        <w:trPr>
          <w:cnfStyle w:firstRow="1" w:val="100000000000" />
        </w:trPr>
        <w:tc>
          <w:tcPr>
            <w:tcBorders>
              <w:bottom w:val="single" />
            </w:tcBorders>
            <w:vAlign w:val="bottom" />
          </w:tcPr>
          <w:p>
            <w:pPr>
              <w:pStyle w:val="Compact" />
              <w:jc w:val="left" />
            </w:pPr>
            <w:r>
              <w:t xml:space="preserve">
Table
</w:t>
            </w:r>
          </w:p>
        </w:tc>
        <w:tc>
          <w:tcPr>
            <w:tcBorders>
              <w:bottom w:val="single" />
            </w:tcBorders>
            <w:vAlign w:val="bottom" />
          </w:tcPr>
          <w:p>
            <w:pPr>
              <w:pStyle w:val="Compact" />
              <w:jc w:val="left" />
            </w:pPr>
            <w:r>
              <w:t xml:space="preserve">
Table
</w:t>
            </w:r>
          </w:p>
        </w:tc>
      </w:tr>
      <w:tr>
        <w:tc>
          <w:p>
            <w:pPr>
              <w:pStyle w:val="Compact" />
              <w:jc w:val="left" />
            </w:pPr>
            <w:r>
              <w:t xml:space="preserve">
1
</w:t>
            </w:r>
          </w:p>
        </w:tc>
        <w:tc>
          <w:p>
            <w:pPr>
              <w:pStyle w:val="Compact" />
              <w:jc w:val="left" />
            </w:pPr>
            <w:r>
              <w:t xml:space="preserve">
2
</w:t>
            </w:r>
          </w:p>
        </w:tc>
      </w:tr>
    </w:tbl>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="ImageCaption" />
      </w:pPr>
      <w:r>
        <w:t xml:space="preserve">
Image Caption
</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="DefinitionTerm" />
      </w:pPr>
      <w:r>
        <w:t xml:space="preserve">
DefinitionTerm
</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="Definition" />
      </w:pPr>
      <w:r>
        <w:t xml:space="preserve">
Definition
</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="DefinitionTerm" />
      </w:pPr>
      <w:r>
        <w:t xml:space="preserve">
DefinitionTerm
</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:pPr>
        <w:pStyle w:val="Definition" />
      </w:pPr>
      <w:r>
        <w:t xml:space="preserve">
Definition
</w:t>
      </w:r>
    </w:p>
    <w:sectPr />
  </w:body>
</w:document>
<?xml version="1.0" encoding="UTF-8"?>
<w:comments xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w10="urn:schemas-microsoft-com:office:word" xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:pic="http://schemas.openxmlformats.org/drawingml/2006/picture" xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing" /><?xml version="1.0" encoding="UTF-8"?>
<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme"><a:themeElements><a:clrScheme name="Office"><a:dk1><a:sysClr val="windowText" lastClr="000000" /></a:dk1><a:lt1><a:sysClr val="window" lastClr="FFFFFF" /></a:lt1><a:dk2><a:srgbClr val="1F497D" /></a:dk2><a:lt2><a:srgbClr val="EEECE1" /></a:lt2><a:accent1><a:srgbClr val="4F81BD" /></a:accent1><a:accent2><a:srgbClr val="C0504D" /></a:accent2><a:accent3><a:srgbClr val="9BBB59" /></a:accent3><a:accent4><a:srgbClr val="8064A2" /></a:accent4><a:accent5><a:srgbClr val="4BACC6" /></a:accent5><a:accent6><a:srgbClr val="F79646" /></a:accent6><a:hlink><a:srgbClr val="0000FF" /></a:hlink><a:folHlink><a:srgbClr val="800080" /></a:folHlink></a:clrScheme><a:fontScheme name="Office"><a:majorFont><a:latin typeface="Calibri" /><a:ea typeface="" /><a:cs typeface="" /><a:font script="Jpan" typeface="ＭＳ ゴシック" /><a:font script="Hang" typeface="맑은 고딕" /><a:font script="Hans" typeface="宋体" /><a:font script="Hant" typeface="新細明體" /><a:font script="Arab" typeface="Times New Roman" /><a:font script="Hebr" typeface="Times New Roman" /><a:font script="Thai" typeface="Angsana New" /><a:font script="Ethi" typeface="Nyala" /><a:font script="Beng" typeface="Vrinda" /><a:font script="Gujr" typeface="Shruti" /><a:font script="Khmr" typeface="MoolBoran" /><a:font script="Knda" typeface="Tunga" /><a:font script="Guru" typeface="Raavi" /><a:font script="Cans" typeface="Euphemia" /><a:font script="Cher" typeface="Plantagenet Cherokee" /><a:font script="Yiii" typeface="Microsoft Yi Baiti" /><a:font script="Tibt" typeface="Microsoft Himalaya" /><a:font script="Thaa" typeface="MV Boli" /><a:font script="Deva" typeface="Mangal" /><a:font script="Telu" typeface="Gautami" /><a:font script="Taml" typeface="Latha" /><a:font script="Syrc" typeface="Estrangelo Edessa" /><a:font script="Orya" typeface="Kalinga" /><a:font script="Mlym" typeface="Kartika" /><a:font script="Laoo" typeface="DokChampa" /><a:font script="Sinh" typeface="Iskoola Pota" /><a:font script="Mong" typeface="Mongolian Baiti" /><a:font script="Viet" typeface="Times New Roman" /><a:font script="Uigh" typeface="Microsoft Uighur" /></a:majorFont><a:minorFont><a:latin typeface="Cambria" /><a:ea typeface="" /><a:cs typeface="" /><a:font script="Jpan" typeface="ＭＳ 明朝" /><a:font script="Hang" typeface="맑은 고딕" /><a:font script="Hans" typeface="宋体" /><a:font script="Hant" typeface="新細明體" /><a:font script="Arab" typeface="Arial" /><a:font script="Hebr" typeface="Arial" /><a:font script="Thai" typeface="Cordia New" /><a:font script="Ethi" typeface="Nyala" /><a:font script="Beng" typeface="Vrinda" /><a:font script="Gujr" typeface="Shruti" /><a:font script="Khmr" typeface="DaunPenh" /><a:font script="Knda" typeface="Tunga" /><a:font script="Guru" typeface="Raavi" /><a:font script="Cans" typeface="Euphemia" /><a:font script="Cher" typeface="Plantagenet Cherokee" /><a:font script="Yiii" typeface="Microsoft Yi Baiti" /><a:font script="Tibt" typeface="Microsoft Himalaya" /><a:font script="Thaa" typeface="MV Boli" /><a:font script="Deva" typeface="Mangal" /><a:font script="Telu" typeface="Gautami" /><a:font script="Taml" typeface="Latha" /><a:font script="Syrc" typeface="Estrangelo Edessa" /><a:font script="Orya" typeface="Kalinga" /><a:font script="Mlym" typeface="Kartika" /><a:font script="Laoo" typeface="DokChampa" /><a:font script="Sinh" typeface="Iskoola Pota" /><a:font script="Mong" typeface="Mongolian Baiti" /><a:font script="Viet" typeface="Arial" /><a:font script="Uigh" typeface="Microsoft Uighur" /></a:minorFont></a:fontScheme><a:fmtScheme name="Office"><a:fillStyleLst><a:solidFill><a:schemeClr val="phClr" /></a:solidFill><a:gradFill rotWithShape="1"><a:gsLst><a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000" /><a:satMod val="300000" /></a:schemeClr></a:gs><a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000" /><a:satMod val="300000" /></a:schemeClr></a:gs><a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000" /><a:satMod val="350000" /></a:schemeClr></a:gs></a:gsLst><a:lin ang="16200000" scaled="1" /></a:gradFill><a:gradFill rotWithShape="1"><a:gsLst><a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000" /><a:shade val="100000" /><a:satMod val="130000" /></a:schemeClr></a:gs><a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000" /><a:shade val="100000" /><a:satMod val="350000" /></a:schemeClr></a:gs></a:gsLst><a:lin ang="16200000" scaled="0" /></a:gradFill></a:fillStyleLst><a:lnStyleLst><a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000" /><a:satMod val="105000" /></a:schemeClr></a:solidFill><a:prstDash val="solid" /></a:ln><a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr" /></a:solidFill><a:prstDash val="solid" /></a:ln><a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr" /></a:solidFill><a:prstDash val="solid" /></a:ln></a:lnStyleLst><a:effectStyleLst><a:effectStyle><a:effectLst><a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000" /></a:srgbClr></a:outerShdw></a:effectLst></a:effectStyle><a:effectStyle><a:effectLst><a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000" /></a:srgbClr></a:outerShdw></a:effectLst></a:effectStyle><a:effectStyle><a:effectLst><a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000" /></a:srgbClr></a:outerShdw></a:effectLst><a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0" /></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000" /></a:lightRig></a:scene3d><a:sp3d><a:bevelT w="63500" h="25400" /></a:sp3d></a:effectStyle></a:effectStyleLst><a:bgFillStyleLst><a:solidFill><a:schemeClr val="phClr" /></a:solidFill><a:gradFill rotWithShape="1"><a:gsLst><a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000" /><a:satMod val="350000" /></a:schemeClr></a:gs><a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000" /><a:shade val="99000" /><a:satMod val="350000" /></a:schemeClr></a:gs><a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000" /><a:satMod val="255000" /></a:schemeClr></a:gs></a:gsLst><a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000" /></a:path></a:gradFill><a:gradFill rotWithShape="1"><a:gsLst><a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000" /><a:satMod val="300000" /></a:schemeClr></a:gs><a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000" /><a:satMod val="200000" /></a:schemeClr></a:gs></a:gsLst><a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000" /></a:path></a:gradFill></a:bgFillStyleLst></a:fmtScheme></a:themeElements><a:objectDefaults><a:spDef><a:spPr /><a:bodyPr /><a:lstStyle /><a:style><a:lnRef idx="1"><a:schemeClr val="accent1" /></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1" /></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1" /></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1" /></a:fontRef></a:style></a:spDef><a:lnDef><a:spPr /><a:bodyPr /><a:lstStyle /><a:style><a:lnRef idx="2"><a:schemeClr val="accent1" /></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1" /></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1" /></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1" /></a:fontRef></a:style></a:lnDef></a:objectDefaults><a:extraClrSchemeLst /></a:theme><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Properties xmlns="http://schemas.openxmlformats.org/officeDocument/2006/custom-properties" xmlns:vt="http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes"></Properties>
<?xml version="1.0" encoding="UTF-8"?>
<cp:coreProperties xmlns:cp="http://schemas.openxmlformats.org/package/2006/metadata/core-properties" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:dcmitype="http://purl.org/dc/dcmitype/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><dc:title>Title</dc:title><dc:creator>Author</dc:creator><cp:keywords></cp:keywords><dcterms:created xsi:type="dcterms:W3CDTF">2017-12-27T05:22:50Z</dcterms:created><dcterms:modified xsi:type="dcterms:W3CDTF">2017-12-27T05:22:50Z</dcterms:modified></cp:coreProperties><?xml version="1.0" encoding="UTF-8"?>
<Properties xmlns="http://schemas.openxmlformats.org/officeDocument/2006/extended-properties" xmlns:vt="http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes">
  <Words>83</Words>
  <SharedDoc>false</SharedDoc>
  <HyperlinksChanged>false</HyperlinksChanged>
  <Lines>12</Lines>
  <AppVersion>12.0000</AppVersion>
  <LinksUpToDate>false</LinksUpToDate>
  <Application>Microsoft Word 12.0.0</Application>
  <CharactersWithSpaces>583</CharactersWithSpaces>
  <Template>Normal.dotm</Template>
  <DocSecurity>0</DocSecurity>
  <TotalTime>6</TotalTime>
  <ScaleCrop>false</ScaleCrop>
  <Characters>475</Characters>
  <Paragraphs>8</Paragraphs>
  <Pages>1</Pages>
</Properties><?xml version="1.0" encoding="UTF-8"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="word/document.xml" /><Relationship Id="rId4" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties" Target="docProps/app.xml" /><Relationship Id="rId3" Type="http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties" Target="docProps/core.xml" /><Relationship Id="rId5" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties" Target="docProps/custom.xml"/>
</Relationships>
<?xml version="1.0" encoding="utf-8" ?>
$if(xml-stylesheet)$
<?xml-stylesheet type="text/xsl" href="$xml-stylesheet$"?>
$endif$
<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN"
                  "JATS-publishing1.dtd">
${ article.jats_publishing() }
$if(titleblock)$
$titleblock$

$endif$
$for(header-includes)$
$header-includes$

$endfor$
$for(include-before)$
$include-before$

$endfor$
$if(toc)$
$table-of-contents$

$endif$
$body$
$for(include-after)$

$include-after$
$endfor$
$for(include-before)$
$include-before$

$endfor$
$if(toc)$
{{toc /}}

$endif$
$body$
$for(include-after)$

$include-after$
$endfor$
$if(article.type)$
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" dtd-version="1.2" article-type="$article.type$">
$else$
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" dtd-version="1.2" article-type="other">
$endif$
<front>
<journal-meta>
$if(journal.publisher-id)$
<journal-id journal-id-type="publisher-id">$journal.publisher-id$</journal-id>
$endif$
$if(journal.nlm-ta)$
<journal-id journal-id-type="nlm-ta">$journal.nlm-ta$</journal-id>
$endif$
$if(journal.pmc)$
<journal-id journal-id-type="pmc">$journal.pmc$</journal-id>
$endif$
$-- Fallback: an empty journal-id in case none is available.
$if(journal.publisher-id)$
$elseif(journal.nlm-ta)$
$elseif(journal.pmc)$
$else$
<journal-id></journal-id>
$endif$
<journal-title-group>
$if(journal.title)$
<journal-title>$journal.title$</journal-title>
$endif$
$if(journal.abbrev-title)$
<abbrev-journal-title>$journal.abbrev-title$</abbrev-journal-title>
$endif$
</journal-title-group>
$if(journal.pissn)$
<issn publication-format="print">$journal.pissn$</issn>
$endif$
$if(journal.eissn)$
<issn publication-format="electronic">$journal.eissn$</issn>
$endif$
$-- At least one issn element is required; use empty issn as fallback
$if(journal.pissn)$
$elseif(journal.eissn)$
$else$
<issn></issn>
$endif$
<publisher>
<publisher-name>$journal.publisher-name$</publisher-name>
$if(journal.publisher-loc)$
<publisher-loc>$journal.publisher-loc$</publisher-loc>
$endif$
</publisher>
</journal-meta>
<article-meta>
$if(article.publisher-id)$
<article-id pub-id-type="publisher-id">$article.publisher-id$</article-id>
$endif$
$if(article.doi)$
<article-id pub-id-type="doi">$article.doi$</article-id>
$endif$
$if(article.pmid)$
<article-id pub-id-type="pmid">$article.pmid$</article-id>
$endif$
$if(article.pmcid)$
<article-id pub-id-type="pmcid">$article.pmcid$</article-id>
$endif$
$if(article.art-access-id)$
<article-id pub-id-type="art-access-id">$article.art-access-id$</article-id>
$endif$
$if(article.heading)$
<article-categories>
<subj-group subj-group-type="heading">
<subject>$article.heading$</subject>
</subj-group>
$if(article.categories)$
<subj-group subj-group-type="categories">
$for(article.categories)$
<subject>$article.categories$</subject>
$endfor$
</subj-group>
$endif$
</article-categories>
$endif$
$if(title)$
<title-group>
<article-title>$title$</article-title>
$if(subtitle)$
<subtitle>${subtitle}</subtitle>
$endif$
</title-group>
$endif$
$if(author)$
<contrib-group>
$for(author)$
<contrib contrib-type="author"$if(author.equal-contrib)$ equal-contrib="yes"$endif$$if(author.cor-id)$ corresp="yes"$endif$>
$if(author.orcid)$
<contrib-id contrib-id-type="orcid">$author.orcid$</contrib-id>
$endif$
$if(author.surname)$
<name>
<surname>$if(author.non-dropping-particle)$${author.non-dropping-particle} $endif$$author.surname$</surname>
<given-names>$author.given-names$$if(author.dropping-particle)$ ${author.dropping-particle}$endif$</given-names>
$if(author.prefix)$
<prefix>${author.suffix}</prefix>
$endif$
$if(author.suffix)$
<suffix>${author.suffix}</suffix>
$endif$
</name>
$elseif(author.name)$
<string-name>$author.name$</string-name>
$else$
<string-name>$author$</string-name>
$endif$
$if(author.email)$
<email>$author.email$</email>
$endif$
$-- if affiliations are listed separately, then create links. Otherwise
$-- include them here.
$if(affiliation)$
$for(author.affiliation)$
<xref ref-type="aff" rid="aff-$author.affiliation$"/>
$endfor$
$else$
$for(author.affiliation)$
${ it:affiliations.jats() }
$endfor$
$endif$
$if(author.cor-id)$
<xref ref-type="corresp" rid="cor-$author.cor-id$"><sup>*</sup></xref>
$endif$
</contrib>
$endfor$
$for(affiliation)$
${ it:affiliations.jats() }
$endfor$
</contrib-group>
$endif$
$if(article.author-notes)$
<author-notes>
$if(article.author-notes.corresp)$
$for(article.author-notes.corresp)$
<corresp id="cor-$article.author-notes.corresp.id$">* E-mail: <email>$article.author-notes.corresp.email$</email></corresp>
$endfor$
$endif$
$if(article.author-notes.conflict)$
<fn fn-type="conflict"><p>$article.author-notes.conflict$</p></fn>
$endif$
$if(article.author-notes.con)$
<fn fn-type="con"><p>$article.author-notes.con$</p></fn>
$endif$
</author-notes>
$endif$
$if(date)$
<pub-date date-type="$if(date.type)$$date.type$$else$pub$endif$" publication-format="electronic"$if(date.iso-8601)$ iso-8601-date="$date.iso-8601$"$endif$>
$if(date.day)$
<day>$date.day$</day>
$endif$
$if(date.month)$
<month>$date.month$</month>
$endif$
<year>$date.year$</year>
</pub-date>
$endif$
$if(article.volume)$
<volume>$article.volume$</volume>
$endif$
$if(article.issue)$
<issue>$article.issue$</issue>
$endif$
$if(article.fpage)$
<fpage>$article.fpage$</fpage>
$endif$
$if(article.lpage)$
<lpage>$article.lpage$</lpage>
$endif$
$if(article.elocation-id)$
<elocation-id>$article.elocation-id$</elocation-id>
$endif$
$if(history)$
<history>
</history>
$endif$
<permissions>
$for(copyright.statement)$
<copyright-statement>$copyright.statement$</copyright-statement>
$endfor$
$for(copyright.year)$
<copyright-year>$copyright.year$</copyright-year>
$endfor$
$for(copyright.holder)$
<copyright-holder>$copyright.holder$</copyright-holder>
$endfor$
$if(copyright.text)$
<license license-type="$copyright.type$" xlink:href="$copyright.link$">
<license-p>$copyright.text$</license-p>
</license>
$endif$
$for(license)$
<license$if(it.type)$ license-type="${it.type}"$endif$>
$if(it.link)$
<ali:license_ref xmlns:ali="http://www.niso.org/schemas/ali/1.0/">${it.link}</ali:license_ref>
$endif$
<license-p>$if(it.text)$${it.text}$else$${it}$endif$</license-p>
</license>
$endfor$
</permissions>
$if(abstract)$
<abstract>
$abstract$
</abstract>
$endif$
$if(tags)$
<kwd-group kwd-group-type="author">
$for(tags)$
<kwd>$tags$</kwd>
$endfor$
</kwd-group>
$endif$
$if(article.funding-statement)$
<funding-group>
<funding-statement>$article.funding-statement$</funding-statement>
</funding-group>
$endif$
</article-meta>
$if(notes)$
<notes>$notes$</notes>
$endif$
</front>
<body>
$body$
</body>
<back>
$if(back)$
$back$
$endif$
</back>
</article>

$for(header-includes)$
$header-includes$
$endfor$
$for(include-before)$
$include-before$
$endfor$
$body$
$for(include-after)$
$include-after$
$endfor$
/* CSS for citations */
div.csl-bib-body { }
div.csl-entry {
  clear: both;
$if(csl-entry-spacing)$
  margin-bottom: $csl-entry-spacing$;
$endif$
}
.hanging-indent div.csl-entry {
  margin-left:2em;
  text-indent:-2em;
}
div.csl-left-margin {
  min-width:2em;
  float:left;
}
div.csl-right-inline {
  margin-left:2em;
  padding-left:1em;
}
div.csl-indent {
  margin-left: 2em;
}
$definitions.typst()$

#show terms: it => {
  it.children
    .map(child => [
      #strong[#child.term]
      #block(inset: (left: 1.5em, top: -0.4em))[#child.description]
      ])
    .join()
}

$if(template)$
#import "$template$": conf
$else$
$template.typst()$
$endif$

#show: doc => conf(
$if(title)$
  title: [$title$],
$endif$
$if(author)$
  authors: (
$for(author)$
$if(author.name)$
    ( name: "$author.name$",
      affiliation: "$author.affiliation$",
      email: "$author.email$" ),
$else$
    ( name: "$author$",
      affiliation: "",
      email: "" ),
$endif$
$endfor$
    ),
$endif$
$if(keywords)$
  keywords: ($for(keywords)$$keyword$$sep$,$endfor$),
$endif$
$if(date)$
  date: "$date$",
$endif$
$if(lang)$
  lang: "$lang$",
$endif$
$if(region)$
  region: "$region$",
$endif$
$if(abstract)$
  abstract: [$abstract$],
$endif$
$if(margin)$
  margin: ($for(margin/pairs)$$margin.key$: $margin.value$,$endfor$),
$endif$
$if(papersize)$
  paper: "$papersize$",
$endif$
$if(mainfont)$
  font: ("$mainfont$",),
$endif$
$if(fontsize)$
  fontsize: $fontsize$,
$endif$
$if(section-numbering)$
  sectionnumbering: "$section-numbering$",
$endif$
  cols: $if(columns)$$columns$$else$1$endif$,
  doc,
)

$for(header-includes)$
$header-includes$

$endfor$
$for(include-before)$
$include-before$

$endfor$
$if(toc)$
#outline(
  title: auto,
  depth: $toc-depth$
);
$endif$

$body$

$if(citations)$
$if(csl)$

#set bibliography(style: "$csl$")
$elseif(bibliographystyle)$

#set bibliography(style: "$bibliographystyle$")
$endif$
$if(bibliography)$

#bibliography($for(bibliography)$"$bibliography$"$sep$,$endfor$)
$endif$
$endif$
$for(include-after)$

$include-after$
$endfor$
Content-Type: text/x-zim-wiki
Wiki-Format: zim 0.4

$for(include-before)$
$include-before$

$endfor$
$if(toc)$
__TOC__

$endif$
$body$
$for(include-after)$

$include-after$
$endfor$
<?xml version="1.0" encoding="utf-8" ?>
$if(xml-stylesheet)$
<?xml-stylesheet type="text/xsl" href="$xml-stylesheet$"?>
$endif$
<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.2 20190208//EN"
                  "JATS-archivearticle1.dtd">
${ article.jats_publishing() }
\input texinfo
@documentencoding UTF-8
$for(header-includes)$
$header-includes$
$endfor$

$if(strikeout)$
@macro textstrikeout{text}
~~\text\~~
@end macro

$endif$
@ifnottex
@paragraphindent 0
@end ifnottex
$if(titlepage)$
@titlepage
@title $title$
$for(author)$
<AUTHOR>
$endfor$
$if(date)$
$date$
$endif$
@end titlepage

$endif$
$for(include-before)$
$include-before$

$endfor$
$if(toc)$
@contents

$endif$
$body$
$for(include-after)$

$include-after$
$endfor$

@bye
$for(include-before)$
$include-before$

$endfor$
$if(toc)$
__TOC__

$endif$
$body$
$for(include-after)$

$include-after$
$endfor$
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="$lang$" xml:lang="$lang$"$if(dir)$ dir="$dir$"$endif$>
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
$for(author-meta)$
  <meta name="author" content="$author-meta$" />
$endfor$
$if(date-meta)$
  <meta name="dcterms.date" content="$date-meta$" />
$endif$
$if(keywords)$
  <meta name="keywords" content="$for(keywords)$$keywords$$sep$, $endfor$" />
$endif$
$if(description-meta)$
  <meta name="description" content="$description-meta$" />
$endif$
  <title>$if(title-prefix)$$title-prefix$ – $endif$$pagetitle$</title>
  <style>
    div.sitenav { display: flex; flex-direction: row; flex-wrap: wrap; }
    span.navlink { flex: 1; }
    span.navlink-label { display: inline-block; min-width: 4em; }
    $styles.html()$
  </style>
$for(css)$
  <link rel="stylesheet" href="$css$" />
$endfor$
$for(header-includes)$
  $header-includes$
$endfor$
$if(math)$
  $math$
$endif$
</head>
<body>
$for(include-before)$
$include-before$
$endfor$
<nav id="sitenav">
<div class="sitenav">
<span class="navlink">
$if(up.url)$
<span class="navlink-label">Up:</span> <a href="$up.url$" accesskey="u" rel="up">$up.title$</a>
$endif$
</span>
<span class="navlink">
$if(top)$
<span class="navlink-label">Top:</span> <a href="$top.url$" accesskey="t" rel="top">$top.title$</a>
$endif$
</span>
</div>
<div class="sitenav">
<span class="navlink">
$if(next.url)$
<span class="navlink-label">Next:</span> <a href="$next.url$" accesskey="n" rel="next">$next.title$</a>
$endif$
</span>
<span class="navlink">
$if(previous.url)$
<span class="navlink-label">Previous:</span> <a href="$previous.url$" accesskey="p" rel="previous">$previous.title$</a>
$endif$
</span>
</div>
</nav>
$if(top)$
$-- only print title block if this is NOT the top page
$else$
$if(title)$
<header id="title-block-header">
<h1 class="title">$title$</h1>
$if(subtitle)$
<p class="subtitle">$subtitle$</p>
$endif$
$for(author)$
<p class="author">$author$</p>
$endfor$
$if(date)$
<p class="date">$date$</p>
$endif$
$if(abstract)$
<div class="abstract">
<div class="abstract-title">$abstract-title$</div>
$abstract$
</div>
$endif$
$endif$
</header>
$endif$
$if(toc)$
<nav id="$idprefix$TOC" role="doc-toc">
$if(toc-title)$
<h2 id="$idprefix$toc-title">$toc-title$</h2>
$endif$
$table-of-contents$
</nav>
$endif$
$body$
$for(include-after)$
$include-after$
$endfor$
</body>
</html>
<?xml version="1.0" encoding="utf-8" ?>
$if(mathml)$
<!DOCTYPE article PUBLIC "-//OASIS//DTD DocBook EBNF Module V1.1CR1//EN"
                  "http://www.oasis-open.org/docbook/xml/mathml/1.1CR1/dbmathml.dtd">
$else$
<!DOCTYPE article PUBLIC "-//OASIS//DTD DocBook XML V4.5//EN"
                  "http://www.oasis-open.org/docbook/xml/4.5/docbookx.dtd">
$endif$
<article>
  <articleinfo>
    <title>$title$</title>
$if(author)$
    <authorgroup>
$for(author)$
      <author>
        $author$
      </author>
$endfor$
    </authorgroup>
$endif$
$if(date)$
    <date>$date$</date>
$endif$
  </articleinfo>
$for(include-before)$
  $include-before$
$endfor$
  $body$
$for(include-after)$
  $include-after$
$endfor$
</article>
<!DOCTYPE html>
<head$if(lang)$ lang="$lang$"$endif$$if(dir)$ dir="$dir$"$endif$>
  <meta charset="utf-8">
  <meta name="generator" content="pandoc">
$for(author-meta)$
  <meta name="author" content="$author-meta$">
$endfor$
$if(date-meta)$
  <meta name="dcterms.date" content="$date-meta$">
$endif$
$if(keywords)$
  <meta name="keywords" content="$for(keywords)$$keywords$$sep$, $endfor$">
$endif$
  <title>$if(title-prefix)$$title-prefix$ – $endif$$pagetitle$</title>
  <style>
    $styles.html()$
  </style>
$if(css)$
$for(css)$
  <link rel="stylesheet" href="$css$">
$endfor$
$else$
<link href='https://fonts.googleapis.com/css?family=Oswald' rel='stylesheet'>

<style>
  html, .view body { background-color: black; counter-reset: slideidx; }
  body, .view section { background-color: white; border-radius: 12px }
  /* A section is a slide. It's size is 800x600, and this will never change */
  section, .view head > title {
      /* The font from Google */
      font-family: 'Oswald', arial, serif;
      font-size: 30px;
  }

  .view section:after {
    counter-increment: slideidx;
    content: counter(slideidx, decimal-leading-zero);
    position: absolute; bottom: -80px; right: 100px;
    color: white;
  }

  .view head > title {
    color: white;
    text-align: center;
    margin: 1em 0 1em 0;
  }

  h1, h2 {
    margin-top: 200px;
    text-align: center;
    font-size: 80px;
  }
  h3 {
    margin: 100px 0 50px 100px;
  }

  ul {
      margin: 50px 200px;
  }
  li > ul {
      margin: 15px 50px;
  }

  p {
    margin: 75px;
    font-size: 50px;
  }

  blockquote {
    height: 100%;
    background-color: black;
    color: white;
    font-size: 60px;
    padding: 50px;
  }
  blockquote:before {
    content: open-quote;
  }
  blockquote:after {
    content: close-quote;
  }

  /* Figures are displayed full-page, with the caption
     on top of the image/video */
  figure {
    background-color: black;
    width: 100%;
    height: 100%;
  }
  figure > * {
    position: absolute;
  }
  figure > img, figure > video {
    width: 100%; height: 100%;
  }
  figcaption {
    margin: 70px;
    font-size: 50px;
  }

  footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 40px;
    text-align: right;
    background-color: #F3F4F8;
    border-top: 1px solid #CCC;
  }

  /* Transition effect */
  /* Feel free to change the transition effect for original
     animations. See here:
     https://developer.mozilla.org/en/CSS/CSS_transitions
     How to use CSS3 Transitions: */
  section {
    -moz-transition: left 400ms linear 0s;
    -webkit-transition: left 400ms linear 0s;
    -ms-transition: left 400ms linear 0s;
    transition: left 400ms linear 0s;
  }
  .view section {
    -moz-transition: none;
    -webkit-transition: none;
    -ms-transition: none;
    transition: none;
  }

  .view section[aria-selected] {
    border: 5px red solid;
  }

  /* Before */
  section { left: -150%; }
  /* Now */
  section[aria-selected] { left: 0; }
  /* After */
  section[aria-selected] ~ section { left: +150%; }

  /* Incremental elements */

  /* By default, visible */
  .incremental > * { opacity: 1; }

  /* The current item */
  .incremental > *[aria-selected] { opacity: 1; }

  /* The items to-be-selected */
  .incremental > *[aria-selected] ~ * { opacity: 0; }

  /* The progressbar, at the bottom of the slides, show the global
     progress of the presentation. */
  #progress-bar {
    height: 2px;
    background: #AAA;
  }
</style>
$endif$
$if(math)$
  $math$
$endif$
$for(header-includes)$
  $header-includes$
$endfor$
</head>
<body>
$if(title)$
<section class="title">
  <h1 class="title">$title$</h1>
$if(subtitle)$
  <h1 class="subtitle">$subtitle$</h1>
$endif$
  <footer>
    $if(author)$<span class="author">$for(author)$$author$$sep$, $endfor$</span> · $endif$$if(institute)$<span class="institute">$for(institute)$$institute$$sep$, $endfor$</span> · $endif$$if(date)$<span class="date">$date$</span>$endif$
  </footer>
</section>
$endif$
$if(toc)$
<section id="$idprefix$TOC">
$table-of-contents$
</section>
$endif$
$for(include-before)$
$include-before$
$endfor$
$body$
$for(include-after)$
$include-after$
$endfor$
$dzslides-core$
</body>
</html>
$for(header-includes)$
$header-includes$
$endfor$
$for(include-before)$
$include-before$
$endfor$
$body$
$for(include-after)$
$include-after$
$endfor$
// Some definitions presupposed by pandoc's typst output.
#let horizontalrule = [
  #line(start: (25%,0%), end: (75%,0%))
]

#let endnote(num, contents) = [
  #stack(dir: ltr, spacing: 3pt, super[#num], contents)
]
$if(document-css)$
html {
$if(mainfont)$
  font-family: $mainfont$;
$endif$
$if(fontsize)$
  font-size: $fontsize$;
$endif$
$if(linestretch)$
  line-height: $linestretch$;
$endif$
  color: $if(fontcolor)$$fontcolor$$else$#1a1a1a$endif$;
  background-color: $if(backgroundcolor)$$backgroundcolor$$else$#fdfdfd$endif$;
}
body {
  margin: 0 auto;
  max-width: $if(maxwidth)$$maxwidth$$else$36em$endif$;
  padding-left: $if(margin-left)$$margin-left$$else$50px$endif$;
  padding-right: $if(margin-right)$$margin-right$$else$50px$endif$;
  padding-top: $if(margin-top)$$margin-top$$else$50px$endif$;
  padding-bottom: $if(margin-bottom)$$margin-bottom$$else$50px$endif$;
  hyphens: auto;
  overflow-wrap: break-word;
  text-rendering: optimizeLegibility;
  font-kerning: normal;
}
@media (max-width: 600px) {
  body {
    font-size: 0.9em;
    padding: 12px;
  }
  h1 {
    font-size: 1.8em;
  }
}
@media print {
  html {
    background-color: $if(backgroundcolor)$$backgroundcolor$$else$white$endif$;
  }
  body {
    background-color: transparent;
    color: black;
    font-size: 12pt;
  }
  p, h2, h3 {
    orphans: 3;
    widows: 3;
  }
  h2, h3, h4 {
    page-break-after: avoid;
  }
}
p {
  margin: 1em 0;
}
a {
  color: $if(linkcolor)$$linkcolor$$else$#1a1a1a$endif$;
}
a:visited {
  color: $if(linkcolor)$$linkcolor$$else$#1a1a1a$endif$;
}
img {
  max-width: 100%;
}
svg {
  height: auto;
  max-width: 100%;
}
h1, h2, h3, h4, h5, h6 {
  margin-top: 1.4em;
}
h5, h6 {
  font-size: 1em;
  font-style: italic;
}
h6 {
  font-weight: normal;
}
ol, ul {
  padding-left: 1.7em;
  margin-top: 1em;
}
li > ol, li > ul {
  margin-top: 0;
}
blockquote {
  margin: 1em 0 1em 1.7em;
  padding-left: 1em;
  border-left: 2px solid #e6e6e6;
  color: #606060;
}
$if(abstract)$
div.abstract {
  margin: 2em 2em 2em 2em;
  text-align: left;
  font-size: 85%;
}
div.abstract-title {
  font-weight: bold;
  text-align: center;
  padding: 0;
  margin-bottom: 0.5em;
}
$endif$
code {
  font-family: $if(monofont)$$monofont$$else$Menlo, Monaco, Consolas, 'Lucida Console', monospace$endif$;
$if(monobackgroundcolor)$
  background-color: $monobackgroundcolor$;
  padding: .2em .4em;
$endif$
  font-size: 85%;
  margin: 0;
  hyphens: manual;
}
pre {
  margin: 1em 0;
$if(monobackgroundcolor)$
  background-color: $monobackgroundcolor$;
  padding: 1em;
$endif$
  overflow: auto;
}
pre code {
  padding: 0;
  overflow: visible;
  overflow-wrap: normal;
}
.sourceCode {
 background-color: transparent;
 overflow: visible;
}
hr {
  background-color: #1a1a1a;
  border: none;
  height: 1px;
  margin: 1em 0;
}
table {
  margin: 1em 0;
  border-collapse: collapse;
  width: 100%;
  overflow-x: auto;
  display: block;
  font-variant-numeric: lining-nums tabular-nums;
}
table caption {
  margin-bottom: 0.75em;
}
tbody {
  margin-top: 0.5em;
  border-top: 1px solid $if(fontcolor)$$fontcolor$$else$#1a1a1a$endif$;
  border-bottom: 1px solid $if(fontcolor)$$fontcolor$$else$#1a1a1a$endif$;
}
th {
  border-top: 1px solid $if(fontcolor)$$fontcolor$$else$#1a1a1a$endif$;
  padding: 0.25em 0.5em 0.25em 0.5em;
}
td {
  padding: 0.125em 0.5em 0.25em 0.5em;
}
header {
  margin-bottom: 4em;
  text-align: center;
}
#TOC li {
  list-style: none;
}
#TOC ul {
  padding-left: 1.3em;
}
#TOC > ul {
  padding-left: 0;
}
#TOC a:not(:hover) {
  text-decoration: none;
}
$endif$
code{white-space: pre-wrap;}
span.smallcaps{font-variant: small-caps;}
div.columns{display: flex; gap: min(4vw, 1.5em);}
div.column{flex: auto; overflow-x: auto;}
div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
/* The extra [class] is a hack that increases specificity enough to
   override a similar rule in reveal.js */
ul.task-list[class]{list-style: none;}
ul.task-list li input[type="checkbox"] {
  font-size: inherit;
  width: 0.8em;
  margin: 0 0.8em 0.2em -1.6em;
  vertical-align: middle;
}
$if(quotes)$
q { quotes: "“" "”" "‘" "’"; }
$endif$
$if(displaymath-css)$
.display.math{display: block; text-align: center; margin: 0.5rem auto;}
$endif$
$if(highlighting-css)$
/* CSS for syntax highlighting */
$highlighting-css$
$endif$
$if(csl-css)$
$styles.citations.html()$
$endif$
<!DOCTYPE html>
<html$if(lang)$ lang="$lang$"$endif$$if(dir)$ dir="$dir$"$endif$>
<head>
  <meta charset="utf-8">
  <meta name="generator" content="pandoc">
$for(author-meta)$
  <meta name="author" content="$author-meta$">
$endfor$
$if(date-meta)$
  <meta name="dcterms.date" content="$date-meta$">
$endif$
$if(keywords)$
  <meta name="keywords" content="$for(keywords)$$keywords$$sep$, $endfor$">
$endif$
  <title>$if(title-prefix)$$title-prefix$ – $endif$$pagetitle$</title>
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, minimal-ui">
  <link rel="stylesheet" href="$revealjs-url$/dist/reset.css">
  <link rel="stylesheet" href="$revealjs-url$/dist/reveal.css">
  <style>
    .reveal .sourceCode {  /* see #7635 */
      overflow: visible;
    }
    $styles.html()$
  </style>
$if(theme)$
  <link rel="stylesheet" href="$revealjs-url$/dist/theme/$theme$.css" id="theme">
$else$
  <link rel="stylesheet" href="$revealjs-url$/dist/theme/black.css" id="theme">
$endif$
$for(css)$
  <link rel="stylesheet" href="$css$"/>
$endfor$
$if(math)$
  $math$
$endif$
$for(header-includes)$
  $header-includes$
$endfor$
</head>
<body>
$for(include-before)$
$include-before$
$endfor$
  <div class="reveal">
    <div class="slides">

$if(title)$
<section id="$idprefix$title-slide"$for(title-slide-attributes/pairs)$ $it.key$="$it.value$"$endfor$>
  <h1 class="title">$title$</h1>
$if(subtitle)$
  <p class="subtitle">$subtitle$</p>
$endif$
$for(author)$
  <p class="author">$author$</p>
$endfor$
$for(institute)$
  <p class="institute">$institute$</p>
$endfor$
$if(date)$
  <p class="date">$date$</p>
$endif$
</section>
$endif$
$if(toc)$
<section id="$idprefix$TOC">
<nav role="doc-toc"> 
$if(toc-title)$
<h2 id="$idprefix$toc-title">$toc-title$</h2>
$endif$
$table-of-contents$
</nav>
</section>
$endif$

$body$
    </div>
  </div>

  <script src="$revealjs-url$/dist/reveal.js"></script>

  <!-- reveal.js plugins -->
  <script src="$revealjs-url$/plugin/notes/notes.js"></script>
  <script src="$revealjs-url$/plugin/search/search.js"></script>
  <script src="$revealjs-url$/plugin/zoom/zoom.js"></script>
$if(mathjax)$
  <script src="$revealjs-url$/plugin/math/math.js"></script>
$endif$

  <script>

      // Full list of configuration options available at:
      // https://revealjs.com/config/
      Reveal.initialize({
        // Display controls in the bottom right corner
        controls: $controls$,

        // Help the user learn the controls by providing hints, for example by
        // bouncing the down arrow when they first encounter a vertical slide
        controlsTutorial: $controlsTutorial$,

        // Determines where controls appear, "edges" or "bottom-right"
        controlsLayout: '$controlsLayout$',

        // Visibility rule for backwards navigation arrows; "faded", "hidden"
        // or "visible"
        controlsBackArrows: '$controlsBackArrows$',

        // Display a presentation progress bar
        progress: $progress$,

        // Display the page number of the current slide
        slideNumber: $slideNumber$,

        // 'all', 'print', or 'speaker'
        showSlideNumber: '$showSlideNumber$',

        // Add the current slide number to the URL hash so that reloading the
        // page/copying the URL will return you to the same slide
        hash: $hash$,

        // Start with 1 for the hash rather than 0
        hashOneBasedIndex: $hashOneBasedIndex$,

        // Flags if we should monitor the hash and change slides accordingly
        respondToHashChanges: $respondToHashChanges$,

        // Push each slide change to the browser history
        history: $history$,

        // Enable keyboard shortcuts for navigation
        keyboard: $keyboard$,

        // Enable the slide overview mode
        overview: $overview$,

        // Disables the default reveal.js slide layout (scaling and centering)
        // so that you can use custom CSS layout
        disableLayout: $disableLayout$,

        // Vertical centering of slides
        center: $center$,

        // Enables touch navigation on devices with touch input
        touch: $touch$,

        // Loop the presentation
        loop: $loop$,

        // Change the presentation direction to be RTL
        rtl: $rtl$,

        // see https://revealjs.com/vertical-slides/#navigation-mode
        navigationMode: '$navigationMode$',

        // Randomizes the order of slides each time the presentation loads
        shuffle: $shuffle$,

        // Turns fragments on and off globally
        fragments: $fragments$,

        // Flags whether to include the current fragment in the URL,
        // so that reloading brings you to the same fragment position
        fragmentInURL: $fragmentInURL$,

        // Flags if the presentation is running in an embedded mode,
        // i.e. contained within a limited portion of the screen
        embedded: $embedded$,

        // Flags if we should show a help overlay when the questionmark
        // key is pressed
        help: $help$,

        // Flags if it should be possible to pause the presentation (blackout)
        pause: $pause$,

        // Flags if speaker notes should be visible to all viewers
        showNotes: $showNotes$,

        // Global override for autoplaying embedded media (null/true/false)
        autoPlayMedia: $autoPlayMedia$,

        // Global override for preloading lazy-loaded iframes (null/true/false)
        preloadIframes: $preloadIframes$,

        // Number of milliseconds between automatically proceeding to the
        // next slide, disabled when set to 0, this value can be overwritten
        // by using a data-autoslide attribute on your slides
        autoSlide: $autoSlide$,

        // Stop auto-sliding after user input
        autoSlideStoppable: $autoSlideStoppable$,

        // Use this method for navigation when auto-sliding
        autoSlideMethod: $autoSlideMethod$,

        // Specify the average time in seconds that you think you will spend
        // presenting each slide. This is used to show a pacing timer in the
        // speaker view
        defaultTiming: $defaultTiming$,

        // Enable slide navigation via mouse wheel
        mouseWheel: $mouseWheel$,

        // The display mode that will be used to show slides
        display: '$display$',

        // Hide cursor if inactive
        hideInactiveCursor: $hideInactiveCursor$,

        // Time before the cursor is hidden (in ms)
        hideCursorTime: $hideCursorTime$,

        // Opens links in an iframe preview overlay
        previewLinks: $previewLinks$,

        // Transition style (none/fade/slide/convex/concave/zoom)
        transition: '$transition$',

        // Transition speed (default/fast/slow)
        transitionSpeed: '$transitionSpeed$',

        // Transition style for full page slide backgrounds
        // (none/fade/slide/convex/concave/zoom)
        backgroundTransition: '$backgroundTransition$',

        // Number of slides away from the current that are visible
        viewDistance: $viewDistance$,

        // Number of slides away from the current that are visible on mobile
        // devices. It is advisable to set this to a lower number than
        // viewDistance in order to save resources.
        mobileViewDistance: $mobileViewDistance$,
$if(parallaxBackgroundImage)$

        // Parallax background image
        parallaxBackgroundImage: '$parallaxBackgroundImage/nowrap$', // e.g. "'https://s3.amazonaws.com/hakim-static/reveal-js/reveal-parallax-1.jpg'"
$else$
$if(background-image)$

       // Parallax background image
       parallaxBackgroundImage: '$background-image/nowrap$', // e.g. "'https://s3.amazonaws.com/hakim-static/reveal-js/reveal-parallax-1.jpg'"
$endif$
$endif$
$if(parallaxBackgroundSize)$

        // Parallax background size
        parallaxBackgroundSize: '$parallaxBackgroundSize/nowrap$', // CSS syntax, e.g. "2100px 900px"
$endif$
$if(parallaxBackgroundHorizontal)$

        // Amount to move parallax background (horizontal and vertical) on slide change
        // Number, e.g. 100
        parallaxBackgroundHorizontal: $parallaxBackgroundHorizontal/nowrap$,
$endif$
$if(parallaxBackgroundVertical)$

        parallaxBackgroundVertical: $parallaxBackgroundVertical/nowrap$,
$endif$
$if(width)$

        // The "normal" size of the presentation, aspect ratio will be preserved
        // when the presentation is scaled to fit different resolutions. Can be
        // specified using percentage units.
        width: $width$,
$endif$
$if(height)$

        height: $height$,
$endif$
$if(margin)$

        // Factor of the display size that should remain empty around the content
        margin: $margin$,
$endif$
$if(minScale)$

        // Bounds for smallest/largest possible scale to apply to content
        minScale: $minScale$,
$endif$
$if(maxScale)$

        maxScale: $maxScale$,
$endif$
$if(mathjax)$

        math: {
          mathjax: '$mathjaxurl$',
          config: 'TeX-AMS_HTML-full',
          tex2jax: {
            inlineMath: [['\\(','\\)']],
            displayMath: [['\\[','\\]']],
            balanceBraces: true,
            processEscapes: false,
            processRefs: true,
            processEnvironments: true,
            preview: 'TeX',
            skipTags: ['script','noscript','style','textarea','pre','code'],
            ignoreClass: 'tex2jax_ignore',
            processClass: 'tex2jax_process'
          },
        },
$endif$

        // reveal.js plugins
        plugins: [
$if(mathjax)$
          RevealMath,
$endif$
          RevealNotes,
          RevealSearch,
          RevealZoom
        ]
      });
    </script>
  $for(include-after)$
  $include-after$
  $endfor$
  </body>
</html>
$if(titleblock)$
$titleblock$

$endif$
$for(header-includes)$
$header-includes$

$endfor$
$for(include-before)$
$include-before$

$endfor$
$if(toc)$
$table-of-contents$

$endif$
$body$
$for(include-after)$

$include-after$
$endfor$
$if(titleblock)$
$titleblock$

$endif$
$for(header-includes)$
$header-includes$

$endfor$
$for(include-before)$
$include-before$

$endfor$
$if(toc)$
$table-of-contents$

$endif$
$body$
$for(include-after)$

$include-after$
$endfor$
$if(titleblock)$
$titleblock$

$endif$
$for(header-includes)$
$header-includes$

$endfor$
$for(include-before)$
$include-before$

$endfor$
$if(toc)$
$table-of-contents$

$endif$
$body$
$for(include-after)$

$include-after$
$endfor$
<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
 "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"$if(lang)$ lang="$lang$" xml:lang="$lang$"$endif$$if(dir)$ dir="$dir$"$endif$>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta http-equiv="Content-Style-Type" content="text/css" />
  <meta name="generator" content="pandoc" />
$for(author-meta)$
  <meta name="author" content="$author-meta$" />
$endfor$
$if(date-meta)$
  <meta name="date" content="$date-meta$" />
$endif$
$if(keywords)$
  <meta name="keywords" content="$for(keywords)$$keywords$$sep$, $endfor$" />
$endif$
  <title>$if(title-prefix)$$title-prefix$ – $endif$$pagetitle$</title>
  <style type="text/css">
    $styles.html()$
  </style>
  <link rel="stylesheet" type="text/css" media="screen, projection, print"
    href="$slideous-url$/slideous.css" />
$for(css)$
  <link rel="stylesheet" type="text/css" media="screen, projection, print"
   href="$css$" />
$endfor$
$if(math)$
  $math$
$endif$
$for(header-includes)$
  $header-includes$
$endfor$
  <script src="$slideous-url$/slideous.js"
    charset="utf-8" type="text/javascript"></script>
$if(duration)$
  <meta name="duration" content="$duration$" />
$endif$
</head>
<body>
$for(include-before)$
$include-before$
$endfor$
<div id="statusbar">
<span style="float:right;">
<span style="margin-right:4em;font-weight:bold;"><span id="slideidx"></span> of {$$slidecount}</span>
<button id="homebutton" title="first slide">1</button>
<button id="prevslidebutton" title="previous slide">&laquo;</button>
<button id="previtembutton" title="previous item">&lsaquo;</button>
<button id="nextitembutton" title="next item">&rsaquo;</button>
<button id="nextslidebutton" title="next slide">&raquo;</button>
<button id="endbutton" title="last slide">{$$slidecount}</button>
<button id="incfontbutton" title="content">A+</button>
<button id="decfontbutton" title="first slide">A-</button>
<select id="tocbox" size="1"><option></option></select>
</span>
<span id="eos">&frac12;</span>
<span title="{$$location}, {$$date}">{$$title}, {$$author}</span>
</div>
$if(title)$
<div class="slide titlepage">
  <h1 class="title">$title$</h1>
$if(subtitle)$
  <h1 class="subtitle">$subtitle$</h1>
$endif$
$if(author)$
  <p class="author">
$for(author)$$author$$sep$<br/>$endfor$
  </p>
$endif$
$if(institute)$
  <p class="institute">
$for(institute)$$institute$$sep$<br/>$endfor$
  </p>
$endif$
$if(date)$
  <p class="date">$date$</p>
$endif$
</div>
$endif$
$if(toc)$
<div class="slide" id="$idprefix$TOC">
$table-of-contents$
</div>
$endif$
$body$
$for(include-after)$
$include-after$
$endfor$
</body>
</html>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"$if(lang)$ lang="$lang$" xml:lang="$lang$"$endif$$if(dir)$ dir="$dir$"$endif$>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta http-equiv="Content-Style-Type" content="text/css" />
  <meta name="generator" content="pandoc" />
$for(author-meta)$
  <meta name="author" content="$author-meta$" />
$endfor$
$if(date-meta)$
  <meta name="date" content="$date-meta$" />
$endif$
$if(keywords)$
  <meta name="keywords" content="$for(keywords)$$keywords$$sep$, $endfor$" />
$endif$
$if(description-meta)$
  <meta name="description" content="$description-meta$" />
$endif$
  <title>$if(title-prefix)$$title-prefix$ – $endif$$pagetitle$</title>
  <style type="text/css">
    $styles.html()$
  </style>
$for(css)$
  <link rel="stylesheet" href="$css$" type="text/css" />
$endfor$
$for(header-includes)$
  $header-includes$
$endfor$
$if(math)$
$if(mathjax)$
  <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
$endif$
  $math$
$endif$
</head>
<body>
$for(include-before)$
$include-before$
$endfor$
$if(title)$
<div id="$idprefix$header">
<h1 class="title">$title$</h1>
$if(subtitle)$
<h1 class="subtitle">$subtitle$</h1>
$endif$
$for(author)$
<h2 class="author">$author$</h2>
$endfor$
$if(date)$
<h3 class="date">$date$</h3>
$endif$
$if(abstract)$
<div class="abstract">
<div class="abstract-title">$abstract-title$</div>
$abstract$
</div>
$endif$
</div>
$endif$
$if(toc)$
<div id="$idprefix$TOC">
$if(toc-title)$
<h2 id="$idprefix$toc-title">$toc-title$</h2>
$endif$
$table-of-contents$
</div>
$endif$
$body$
$for(include-after)$
$include-after$
$endfor$
</body>
</html>
$if(titleblock)$
$titleblock$

$for(author)$
:Author: $^$$author$
$endfor$
$if(authors)$
:Authors: <AUTHORS>
$endif$
$if(date)$
:Date: $^$$date$
$endif$
$if(address)$
:Address: $^$$address$
$endif$
$if(contact)$
:Contact: $^$$contact$
$endif$
$if(copyright)$
:Copyright: $^$$copyright$
$endif$
$if(dedication)$
:Dedication: $^$$dedication$
$endif$
$if(organization)$
:Organization: $^$$organization$
$endif$
$if(revision)$
:Revision: $^$$revision$
$endif$
$if(status)$
:Status: $^$$status$
$endif$
$if(version)$
:Version: $^$$version$
$endif$
$if(abstract)$
:Abstract:
   $abstract$
$endif$

$endif$
$if(rawtex)$
.. role:: raw-latex(raw)
   :format: latex
..

$endif$
$for(include-before)$
$include-before$

$endfor$
$if(toc)$
.. contents::
   :depth: $toc-depth$
..

$endif$
$if(number-sections)$
.. section-numbering::

$endif$
$for(header-includes)$
$header-includes$

$endfor$
$body$
$for(include-after)$

$include-after$
$endfor$
$if(title)$
#+title: $title$

$endif$
$if(author)$
#+author: $for(author)$$author$$sep$; $endfor$
$endif$
$if(date)$
#+date: $date$

$endif$
$for(header-includes)$
$header-includes$

$endfor$
$if(abstract)$
#+begin_abstract
$abstract$
#+end_abstract
$endif$
$for(include-before)$
$include-before$

$endfor$
$body$
$for(include-after)$

$include-after$
$endfor$
$if(context-lang)$
\mainlanguage[$context-lang$]
$endif$
$if(context-dir)$
\setupalign[$context-dir$]
\setupdirections[bidi=on,method=two]
$endif$
% Enable hyperlinks
\setupinteraction
  [state=start,
$if(title)$
  title={$title$},
$endif$
$if(subtitle)$
  subtitle={$subtitle$},
$endif$
$if(author)$
  author={$for(author)$$author$$sep$; $endfor$},
$endif$
$if(keywords)$
  keyword={$for(keywords)$$keywords$$sep$; $endfor$},
$endif$
  style=$linkstyle$,
  color=$linkcolor$,
  contrastcolor=$linkcontrastcolor$]
\setupurl[style=$urlstyle$]

% make chapter, section bookmarks visible when opening document
\placebookmarks[chapter, section, subsection, subsubsection, subsubsubsection, subsubsubsubsection][chapter, section]
\setupinteractionscreen[option={bookmark,title}]

$if(papersize)$
\setuppapersize[$for(papersize)$$papersize$$sep$,$endfor$]
$endif$
$if(layout)$
\setuplayout[$for(layout)$$layout$$sep$,$endfor$]
$endif$
$if(pagenumbering)$
\setuppagenumbering[$for(pagenumbering)$$pagenumbering$$sep$,$endfor$]
$else$
\setuppagenumbering[location={footer,middle}]
$endif$
$if(pdfa)$
% attempt to generate PDF/A
\setupbackend
  [format=PDF/A-$pdfa$,
   profile={$if(pdfaiccprofile)$$for(pdfaiccprofile)$$pdfaiccprofile$$sep$,$endfor$$else$sRGB.icc$endif$},
   intent=$if(pdfaintent)$$pdfaintent$$else$sRGB IEC61966-2.1$endif$]
$endif$
\setupbackend[export=yes]
\setupstructure[state=start,method=auto]

% use microtypography
\definefontfeature[default][default][script=latn, protrusion=quality, expansion=quality, itlc=yes, textitalics=yes, onum=yes, pnum=yes]
\definefontfeature[default:tnum][default][tnum=yes, pnum=no]
\definefontfeature[smallcaps][script=latn, protrusion=quality, expansion=quality, smcp=yes, onum=yes, pnum=yes]
\setupalign[hz,hanging]
\setupitaliccorrection[global, always]

\setupbodyfontenvironment[default][em=italic] % use italic as em, not slanted

\definefallbackfamily[mainface][rm][CMU Serif][preset=range:greek, force=yes]
\definefontfamily[mainface][rm][$if(mainfont)$$mainfont$$else$Latin Modern Roman$endif$]
\definefontfamily[mainface][mm][$if(mathfont)$$mathfont$$else$Latin Modern Math$endif$]
\definefontfamily[mainface][ss][$if(sansfont)$$sansfont$$else$Latin Modern Sans$endif$]
\definefontfamily[mainface][tt][$if(monofont)$$monofont$$else$Latin Modern Typewriter$endif$][features=none]
\setupbodyfont[mainface$if(fontsize)$,$fontsize$$endif$]

\setupwhitespace[$if(whitespace)$$whitespace$$else$medium$endif$]
$if(indenting)$
\setupindenting[$for(indenting)$$indenting$$sep$,$endfor$]
$endif$
$if(interlinespace)$
\setupinterlinespace[$for(interlinespace)$$interlinespace$$sep$,$endfor$]
$endif$

\setuphead[chapter]            [style=\tfd\setupinterlinespace,header=empty]
\setuphead[section]            [style=\tfc\setupinterlinespace]
\setuphead[subsection]         [style=\tfb\setupinterlinespace]
\setuphead[subsubsection]      [style=\bf]
\setuphead[subsubsubsection]   [style=\sc]
\setuphead[subsubsubsubsection][style=\it]

\definesectionlevels
   [default]
   [section, subsection, subsubsection, subsubsubsection, subsubsubsubsection]

$if(headertext)$
\setupheadertexts$for(headertext)$[$headertext$]$endfor$
$endif$
$if(footertext)$
\setupfootertexts$for(footertext)$[$footertext$]$endfor$
$endif$
$if(number-sections)$
$else$
\setuphead[chapter, section, subsection, subsubsection, subsubsubsection, subsubsubsubsection][number=no]
$endif$

\definedescription
  [description]
  [headstyle=bold, style=normal, location=hanging, width=broad, margin=1cm, alternative=hanging]

\setupitemize[autointro]    % prevent orphan list intro
\setupitemize[indentnext=no]

\defineitemgroup[enumerate]
\setupenumerate[each][fit][itemalign=left,distance=.5em,style={\feature[+][default:tnum]}]

\setupfloat[figure][default={here,nonumber}]
\setupfloat[table][default={here,nonumber}]

\setupxtable[frame=off]
\setupxtable[head][topframe=on]
\setupxtable[body][]
\setupxtable[foot][]
\setupxtable[lastrow][bottomframe=on]

$if(emphasis-commands)$
$emphasis-commands$
$endif$
$if(highlighting-commands)$
$highlighting-commands$
$endif$
$if(csl-refs)$
\definemeasure[cslhangindent][1.5em]
\definenarrower[hangingreferences][left=\measure{cslhangindent}]
\definestartstop [cslreferences] [
	$if(csl-hanging-indent)$
	before={%
	  \starthangingreferences[left]
      \setupindenting[-\leftskip,yes,first]
      \doindentation
  	},
  	after=\stophangingreferences,
	$endif$
]
$endif$
$if(includesource)$
$for(sourcefile)$
\attachment[file=$curdir$/$sourcefile$,method=hidden]
$endfor$
$endif$
$for(header-includes)$
$header-includes$
$endfor$

\starttext
$if(title)$
\startalignment[middle]
  {\tfd\setupinterlinespace $title$}
$if(subtitle)$
  \smallskip
  {\tfa\setupinterlinespace $subtitle$}
$endif$
$if(author)$
  \smallskip
  {\tfa\setupinterlinespace $for(author)$$author$$sep$\crlf $endfor$}
$endif$
$if(date)$
  \smallskip
  {\tfa\setupinterlinespace $date$}
$endif$
  \bigskip
\stopalignment
$endif$
$if(abstract)$
\midaligned{\it Abstract}
\startnarrower[2*middle]
$abstract$
\stopnarrower
\blank[big]
$endif$
$for(include-before)$
$include-before$
$endfor$
$if(toc)$
\completecontent
$endif$
$if(lof)$
\completelistoffigures
$endif$
$if(lot)$
\completelistoftables
$endif$

$body$

$for(include-after)$
$include-after$
$endfor$
\stoptext
<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
 "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"$if(lang)$ lang="$lang$" xml:lang="$lang$"$endif$$if(dir)$ dir="$dir$"$endif$>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta http-equiv="Content-Style-Type" content="text/css" />
  <meta name="generator" content="pandoc" />
$for(author-meta)$
  <meta name="author" content="$author-meta$" />
$endfor$
$if(date-meta)$
  <meta name="date" content="$date-meta$" />
$endif$
$if(keywords)$
  <meta name="keywords" content="$for(keywords)$$keywords$$sep$, $endfor$" />
$endif$
  <title>$if(title-prefix)$$title-prefix$ – $endif$$pagetitle$</title>
  <style type="text/css">
    $styles.html()$
  </style>
  <link rel="stylesheet" type="text/css" media="screen, projection, print"
    href="$slidy-url$/styles/slidy.css" />
$for(css)$
  <link rel="stylesheet" type="text/css" media="screen, projection, print"
   href="$css$" />
$endfor$
$if(math)$
  $math$
$endif$
$for(header-includes)$
  $header-includes$
$endfor$
  <script src="$slidy-url$/scripts/slidy.js"
    charset="utf-8" type="text/javascript"></script>
$if(duration)$
  <meta name="duration" content="$duration$" />
$endif$
</head>
<body>
$for(include-before)$
$include-before$
$endfor$
$if(title)$
<div class="slide titlepage">
  <h1 class="title">$title$</h1>
$if(subtitle)$
  <p class="subtitle">$subtitle$</p>
$endif$
$if(author)$
  <p class="author">
$for(author)$$author$$sep$<br/>$endfor$
  </p>
$endif$
$if(institute)$
  <p class="institute">
$for(institute)$$institute$$sep$<br/>$endfor$
  </p>
$endif$
$if(date)$
  <p class="date">$date$</p>
$endif$
</div>
$endif$
$if(toc)$
<div class="slide" id="$idprefix$TOC">
$table-of-contents$
</div>
$endif$
$body$
$for(include-after)$
$include-after$
$endfor$
</body>
</html>
$for(include-before)$
$include-before$

$endfor$
$body$
$for(include-after)$

$include-after$
$endfor$
{\rtf1\ansi\deff0{\fonttbl{\f0 \fswiss Helvetica;}{\f1 \fmodern Courier;}}
{\colortbl;\red255\green0\blue0;\red0\green0\blue255;}
\widowctrl\hyphauto
$for(header-includes)$
$header-includes$
$endfor$

$if(title)$
{\pard \qc \f0 \sa180 \li0 \fi0 \b \fs36 $title$\par}
$endif$
$for(author)$
{\pard \qc \f0 \sa180 \li0 \fi0  $author$\par}
$endfor$
$if(date)$
{\pard \qc \f0 \sa180 \li0 \fi0  $date$\par}
$endif$
$if(spacer)$
{\pard \ql \f0 \sa180 \li0 \fi0 \par}
$endif$
$if(toc)$
$table-of-contents$
$endif$
$for(include-before)$
$include-before$
$endfor$
$body$
$for(include-after)$
$include-after$
$endfor$
}
% Options for packages loaded elsewhere
\PassOptionsToPackage{unicode$for(hyperrefoptions)$,$hyperrefoptions$$endfor$}{hyperref}
\PassOptionsToPackage{hyphens}{url}
$if(colorlinks)$
\PassOptionsToPackage{dvipsnames,svgnames,x11names}{xcolor}
$endif$
$if(CJKmainfont)$
\PassOptionsToPackage{space}{xeCJK}
$endif$
%
\documentclass[
$if(fontsize)$
  $fontsize$,
$endif$
$if(papersize)$
  $papersize$paper,
$endif$
$if(beamer)$
  ignorenonframetext,
$if(handout)$
  handout,
$endif$
$if(aspectratio)$
  aspectratio=$aspectratio$,
$endif$
$endif$
$for(classoption)$
  $classoption$$sep$,
$endfor$
]{$documentclass$}
$if(beamer)$
$if(background-image)$
\usebackgroundtemplate{%
  \includegraphics[width=\paperwidth]{$background-image$}%
}
% In beamer background-image does not work well when other images are used, so this is the workaround
\pgfdeclareimage[width=\paperwidth,height=\paperheight]{background}{$background-image$}
\usebackgroundtemplate{\pgfuseimage{background}}
$endif$
\usepackage{pgfpages}
\setbeamertemplate{caption}[numbered]
\setbeamertemplate{caption label separator}{: }
\setbeamercolor{caption name}{fg=normal text.fg}
\beamertemplatenavigationsymbols$if(navigation)$$navigation$$else$empty$endif$
$for(beameroption)$
\setbeameroption{$beameroption$}
$endfor$
% Prevent slide breaks in the middle of a paragraph
\widowpenalties 1 10000
\raggedbottom
$if(section-titles)$
\setbeamertemplate{part page}{
  \centering
  \begin{beamercolorbox}[sep=16pt,center]{part title}
    \usebeamerfont{part title}\insertpart\par
  \end{beamercolorbox}
}
\setbeamertemplate{section page}{
  \centering
  \begin{beamercolorbox}[sep=12pt,center]{part title}
    \usebeamerfont{section title}\insertsection\par
  \end{beamercolorbox}
}
\setbeamertemplate{subsection page}{
  \centering
  \begin{beamercolorbox}[sep=8pt,center]{part title}
    \usebeamerfont{subsection title}\insertsubsection\par
  \end{beamercolorbox}
}
\AtBeginPart{
  \frame{\partpage}
}
\AtBeginSection{
  \ifbibliography
  \else
    \frame{\sectionpage}
  \fi
}
\AtBeginSubsection{
  \frame{\subsectionpage}
}
$endif$
$endif$
$if(beamerarticle)$
\usepackage{beamerarticle} % needs to be loaded first
$endif$
\usepackage{amsmath,amssymb}
$if(linestretch)$
\usepackage{setspace}
$endif$
\usepackage{iftex}
\ifPDFTeX
  \usepackage[$if(fontenc)$$fontenc$$else$T1$endif$]{fontenc}
  \usepackage[utf8]{inputenc}
  \usepackage{textcomp} % provide euro and other symbols
\else % if luatex or xetex
$if(mathspec)$
  \ifXeTeX
    \usepackage{mathspec} % this also loads fontspec
  \else
    \usepackage{unicode-math} % this also loads fontspec
  \fi
$else$
  \usepackage{unicode-math} % this also loads fontspec
$endif$
  \defaultfontfeatures{Scale=MatchLowercase}$-- must come before Beamer theme
  \defaultfontfeatures[\rmfamily]{Ligatures=TeX,Scale=1}
\fi
$if(fontfamily)$
$else$
$-- Set default font before Beamer theme so the theme can override it
\usepackage{lmodern}
$endif$
$-- Set Beamer theme before user font settings so they can override theme
$if(beamer)$
$if(theme)$
\usetheme[$for(themeoptions)$$themeoptions$$sep$,$endfor$]{$theme$}
$endif$
$if(colortheme)$
\usecolortheme{$colortheme$}
$endif$
$if(fonttheme)$
\usefonttheme{$fonttheme$}
$endif$
$if(mainfont)$
\usefonttheme{serif} % use mainfont rather than sansfont for slide text
$endif$
$if(innertheme)$
\useinnertheme{$innertheme$}
$endif$
$if(outertheme)$
\useoutertheme{$outertheme$}
$endif$
$endif$
$-- User font settings (must come after default font and Beamer theme)
$if(fontfamily)$
\usepackage[$for(fontfamilyoptions)$$fontfamilyoptions$$sep$,$endfor$]{$fontfamily$}
$endif$
\ifPDFTeX\else
  % xetex/luatex font selection
$if(mainfont)$
  \setmainfont[$for(mainfontoptions)$$mainfontoptions$$sep$,$endfor$]{$mainfont$}
$endif$
$if(sansfont)$
  \setsansfont[$for(sansfontoptions)$$sansfontoptions$$sep$,$endfor$]{$sansfont$}
$endif$
$if(monofont)$
  \setmonofont[$for(monofontoptions)$$monofontoptions$$sep$,$endfor$]{$monofont$}
$endif$
$for(fontfamilies)$
  \newfontfamily{$fontfamilies.name$}[$for(fontfamilies.options)$$fontfamilies.options$$sep$,$endfor$]{$fontfamilies.font$}
$endfor$
$if(mathfont)$
$if(mathspec)$
  \ifXeTeX
    \setmathfont(Digits,Latin,Greek)[$for(mathfontoptions)$$mathfontoptions$$sep$,$endfor$]{$mathfont$}
  \else
    \setmathfont[$for(mathfontoptions)$$mathfontoptions$$sep$,$endfor$]{$mathfont$}
  \fi
$else$
  \setmathfont[$for(mathfontoptions)$$mathfontoptions$$sep$,$endfor$]{$mathfont$}
$endif$
$endif$
$if(CJKmainfont)$
  \ifXeTeX
    \usepackage{xeCJK}
    \setCJKmainfont[$for(CJKoptions)$$CJKoptions$$sep$,$endfor$]{$CJKmainfont$}
    $if(CJKsansfont)$
      \setCJKsansfont[$for(CJKoptions)$$CJKoptions$$sep$,$endfor$]{$CJKsansfont$}
    $endif$
    $if(CJKmonofont)$
      \setCJKmonofont[$for(CJKoptions)$$CJKoptions$$sep$,$endfor$]{$CJKmonofont$}
    $endif$
  \fi
$endif$
$if(luatexjapresetoptions)$
  \ifLuaTeX
    \usepackage[$for(luatexjapresetoptions)$$luatexjapresetoptions$$sep$,$endfor$]{luatexja-preset}
  \fi
$endif$
$if(CJKmainfont)$
  \ifLuaTeX
    \usepackage[$for(luatexjafontspecoptions)$$luatexjafontspecoptions$$sep$,$endfor$]{luatexja-fontspec}
    \setmainjfont[$for(CJKoptions)$$CJKoptions$$sep$,$endfor$]{$CJKmainfont$}
  \fi
$endif$
\fi
$if(zero-width-non-joiner)$
%% Support for zero-width non-joiner characters.
\makeatletter
\def\zerowidthnonjoiner{%
  % Prevent ligatures and adjust kerning, but still support hyphenating.
  \texorpdfstring{%
    \TextOrMath{\nobreak\discretionary{-}{}{\kern.03em}%
      \ifvmode\else\nobreak\hskip\z@skip\fi}{}%
  }{}%
}
\makeatother
\ifPDFTeX
  \DeclareUnicodeCharacter{200C}{\zerowidthnonjoiner}
\else
  \catcode`^^^^200c=\active
  \protected\def ^^^^200c{\zerowidthnonjoiner}
\fi
%% End of ZWNJ support
$endif$
% Use upquote if available, for straight quotes in verbatim environments
\IfFileExists{upquote.sty}{\usepackage{upquote}}{}
\IfFileExists{microtype.sty}{% use microtype if available
  \usepackage[$for(microtypeoptions)$$microtypeoptions$$sep$,$endfor$]{microtype}
  \UseMicrotypeSet[protrusion]{basicmath} % disable protrusion for tt fonts
}{}
$if(indent)$
$else$
\makeatletter
\@ifundefined{KOMAClassName}{% if non-KOMA class
  \IfFileExists{parskip.sty}{%
    \usepackage{parskip}
  }{% else
    \setlength{\parindent}{0pt}
    \setlength{\parskip}{6pt plus 2pt minus 1pt}}
}{% if KOMA class
  \KOMAoptions{parskip=half}}
\makeatother
$endif$
$if(verbatim-in-note)$
\usepackage{fancyvrb}
$endif$
\usepackage{xcolor}
$if(geometry)$
$if(beamer)$
\geometry{$for(geometry)$$geometry$$sep$,$endfor$}
$else$
\usepackage[$for(geometry)$$geometry$$sep$,$endfor$]{geometry}
$endif$
$endif$
$if(beamer)$
\newif\ifbibliography
$endif$
$if(listings)$
\usepackage{listings}
\newcommand{\passthrough}[1]{#1}
\lstset{defaultdialect=[5.3]Lua}
\lstset{defaultdialect=[x86masm]Assembler}
$endif$
$if(lhs)$
\lstnewenvironment{code}{\lstset{language=Haskell,basicstyle=\small\ttfamily}}{}
$endif$
$if(highlighting-macros)$
$highlighting-macros$
$endif$
$if(tables)$
\usepackage{longtable,booktabs,array}
$if(multirow)$
\usepackage{multirow}
$endif$
\usepackage{calc} % for calculating minipage widths
$if(beamer)$
\usepackage{caption}
% Make caption package work with longtable
\makeatletter
\def\fnum@table{\tablename~\thetable}
\makeatother
$else$
% Correct order of tables after \paragraph or \subparagraph
\usepackage{etoolbox}
\makeatletter
\patchcmd\longtable{\par}{\if@noskipsec\mbox{}\fi\par}{}{}
\makeatother
% Allow footnotes in longtable head/foot
\IfFileExists{footnotehyper.sty}{\usepackage{footnotehyper}}{\usepackage{footnote}}
\makesavenoteenv{longtable}
$endif$
$endif$
$if(graphics)$
\usepackage{graphicx}
\makeatletter
\def\maxwidth{\ifdim\Gin@nat@width>\linewidth\linewidth\else\Gin@nat@width\fi}
\def\maxheight{\ifdim\Gin@nat@height>\textheight\textheight\else\Gin@nat@height\fi}
\makeatother
% Scale images if necessary, so that they will not overflow the page
% margins by default, and it is still possible to overwrite the defaults
% using explicit options in \includegraphics[width, height, ...]{}
\setkeys{Gin}{width=\maxwidth,height=\maxheight,keepaspectratio}
% Set default figure placement to htbp
\makeatletter
\def\fps@figure{htbp}
\makeatother
$endif$
$if(svg)$
\usepackage{svg}
$endif$
$if(strikeout)$
$-- also used for underline
\ifLuaTeX
  \usepackage{luacolor}
  \usepackage[soul]{lua-ul}
\else
  \usepackage{soul}
$if(CJKmainfont)$
  \ifXeTeX
    % soul's \st doesn't work for CJK:
    \usepackage{xeCJKfntef}
    \renewcommand{\st}[1]{\sout{#1}}
  \fi
$endif$
\fi
$endif$
\setlength{\emergencystretch}{3em} % prevent overfull lines
\providecommand{\tightlist}{%
  \setlength{\itemsep}{0pt}\setlength{\parskip}{0pt}}
$if(numbersections)$
\setcounter{secnumdepth}{$if(secnumdepth)$$secnumdepth$$else$5$endif$}
$else$
\setcounter{secnumdepth}{-\maxdimen} % remove section numbering
$endif$
$if(subfigure)$
\usepackage{subcaption}
$endif$
$if(beamer)$
$else$
$if(block-headings)$
% Make \paragraph and \subparagraph free-standing
\ifx\paragraph\undefined\else
  \let\oldparagraph\paragraph
  \renewcommand{\paragraph}[1]{\oldparagraph{#1}\mbox{}}
\fi
\ifx\subparagraph\undefined\else
  \let\oldsubparagraph\subparagraph
  \renewcommand{\subparagraph}[1]{\oldsubparagraph{#1}\mbox{}}
\fi
$endif$
$endif$
$if(pagestyle)$
\pagestyle{$pagestyle$}
$endif$
$if(csl-refs)$
% definitions for citeproc citations
\NewDocumentCommand\citeproctext{}{}
\NewDocumentCommand\citeproc{mm}{%
  \begingroup\def\citeproctext{#2}\cite{#1}\endgroup}
\makeatletter
 % allow citations to break across lines
 \let\@cite@ofmt\@firstofone
 % avoid brackets around text for \cite:
 \def\@biblabel#1{}
 \def\@cite#1#2{{#1\if@tempswa , #2\fi}}
\makeatother
\newlength{\cslhangindent}
\setlength{\cslhangindent}{1.5em}
\newlength{\csllabelwidth}
\setlength{\csllabelwidth}{3em}
\newenvironment{CSLReferences}[2] % #1 hanging-indent, #2 entry-spacing
 {\begin{list}{}{%
  \setlength{\itemindent}{0pt}
  \setlength{\leftmargin}{0pt}
  \setlength{\parsep}{0pt}
  % turn on hanging indent if param 1 is 1
  \ifodd #1
   \setlength{\leftmargin}{\cslhangindent}
   \setlength{\itemindent}{-1\cslhangindent}
  \fi
  % set entry spacing
  \setlength{\itemsep}{#2\baselineskip}}}
 {\end{list}}
\usepackage{calc}
\newcommand{\CSLBlock}[1]{\hfill\break\parbox[t]{\linewidth}{\strut\ignorespaces#1\strut}}
\newcommand{\CSLLeftMargin}[1]{\parbox[t]{\csllabelwidth}{\strut#1\strut}}
\newcommand{\CSLRightInline}[1]{\parbox[t]{\linewidth - \csllabelwidth}{\strut#1\strut}}
\newcommand{\CSLIndent}[1]{\hspace{\cslhangindent}#1}
$endif$
$if(lang)$
\ifLuaTeX
\usepackage[bidi=basic]{babel}
\else
\usepackage[bidi=default]{babel}
\fi
$if(babel-lang)$
\babelprovide[main,import]{$babel-lang$}
$if(mainfont)$
\ifPDFTeX
\else
\babelfont{rm}[$for(mainfontoptions)$$mainfontoptions$$sep$,$endfor$]{$mainfont$}
\fi
$endif$
$endif$
$for(babel-otherlangs)$
\babelprovide[import]{$babel-otherlangs$}
$endfor$
$for(babelfonts/pairs)$
\babelfont[$babelfonts.key$]{rm}{$babelfonts.value$}
$endfor$
% get rid of language-specific shorthands (see #6817):
\let\LanguageShortHands\languageshorthands
\def\languageshorthands#1{}
$endif$
$for(header-includes)$
$header-includes$
$endfor$
\ifLuaTeX
  \usepackage{selnolig}  % disable illegal ligatures
\fi
$if(dir)$
\ifPDFTeX
  \TeXXeTstate=1
  \newcommand{\RL}[1]{\beginR #1\endR}
  \newcommand{\LR}[1]{\beginL #1\endL}
  \newenvironment{RTL}{\beginR}{\endR}
  \newenvironment{LTR}{\beginL}{\endL}
\fi
$endif$
$if(natbib)$
\usepackage[$natbiboptions$]{natbib}
\bibliographystyle{$if(biblio-style)$$biblio-style$$else$plainnat$endif$}
$endif$
$if(biblatex)$
\usepackage[$if(biblio-style)$style=$biblio-style$,$endif$$for(biblatexoptions)$$biblatexoptions$$sep$,$endfor$]{biblatex}
$for(bibliography)$
\addbibresource{$bibliography$}
$endfor$
$endif$
$if(nocite-ids)$
\nocite{$for(nocite-ids)$$it$$sep$, $endfor$}
$endif$
$if(csquotes)$
\usepackage{csquotes}
$endif$
\usepackage{bookmark}
\IfFileExists{xurl.sty}{\usepackage{xurl}}{} % add URL line breaks if available
\urlstyle{$if(urlstyle)$$urlstyle$$else$same$endif$}
$if(links-as-notes)$
% Make links footnotes instead of hotlinks:
\DeclareRobustCommand{\href}[2]{#2\footnote{\url{#1}}}
$endif$
$if(verbatim-in-note)$
\VerbatimFootnotes % allow verbatim text in footnotes
$endif$
\hypersetup{
$if(title-meta)$
  pdftitle={$title-meta$},
$endif$
$if(author-meta)$
  pdfauthor={$author-meta$},
$endif$
$if(lang)$
  pdflang={$lang$},
$endif$
$if(subject)$
  pdfsubject={$subject$},
$endif$
$if(keywords)$
  pdfkeywords={$for(keywords)$$keywords$$sep$, $endfor$},
$endif$
$if(colorlinks)$
  colorlinks=true,
  linkcolor={$if(linkcolor)$$linkcolor$$else$Maroon$endif$},
  filecolor={$if(filecolor)$$filecolor$$else$Maroon$endif$},
  citecolor={$if(citecolor)$$citecolor$$else$Blue$endif$},
  urlcolor={$if(urlcolor)$$urlcolor$$else$Blue$endif$},
$else$
$if(boxlinks)$
$else$
  hidelinks,
$endif$
$endif$
  pdfcreator={LaTeX via pandoc}}

$if(title)$
\title{$title$$if(thanks)$\thanks{$thanks$}$endif$}
$endif$
$if(subtitle)$
$if(beamer)$
$else$
\usepackage{etoolbox}
\makeatletter
\providecommand{\subtitle}[1]{% add subtitle to \maketitle
  \apptocmd{\@title}{\par {\large #1 \par}}{}{}
}
\makeatother
$endif$
\subtitle{$subtitle$}
$endif$
\author{$for(author)$$author$$sep$ \and $endfor$}
\date{$date$}
$if(beamer)$
$if(institute)$
\institute{$for(institute)$$institute$$sep$ \and $endfor$}
$endif$
$if(titlegraphic)$
\titlegraphic{\includegraphics{$titlegraphic$}}
$endif$
$if(logo)$
\logo{\includegraphics{$logo$}}
$endif$
$endif$

\begin{document}
$if(has-frontmatter)$
\frontmatter
$endif$
$if(title)$
$if(beamer)$
\frame{\titlepage}
$else$
\maketitle
$endif$
$if(abstract)$
\begin{abstract}
$abstract$
\end{abstract}
$endif$
$endif$

$for(include-before)$
$include-before$

$endfor$
$if(toc)$
$if(toc-title)$
\renewcommand*\contentsname{$toc-title$}
$endif$
$if(beamer)$
\begin{frame}[allowframebreaks]
$if(toc-title)$
  \frametitle{$toc-title$}
$endif$
  \tableofcontents[hideallsubsections]
\end{frame}
$else$
{
$if(colorlinks)$
\hypersetup{linkcolor=$if(toccolor)$$toccolor$$else$$endif$}
$endif$
\setcounter{tocdepth}{$toc-depth$}
\tableofcontents
}
$endif$
$endif$
$if(lof)$
\listoffigures
$endif$
$if(lot)$
\listoftables
$endif$
$if(linestretch)$
\setstretch{$linestretch$}
$endif$
$if(has-frontmatter)$
\mainmatter
$endif$
$body$

$if(has-frontmatter)$
\backmatter
$endif$
$if(natbib)$
$if(bibliography)$
$if(biblio-title)$
$if(has-chapters)$
\renewcommand\bibname{$biblio-title$}
$else$
\renewcommand\refname{$biblio-title$}
$endif$
$endif$
$if(beamer)$
\begin{frame}[allowframebreaks]{$biblio-title$}
  \bibliographytrue
$endif$
  \bibliography{$for(bibliography)$$bibliography$$sep$,$endfor$}
$if(beamer)$
\end{frame}
$endif$

$endif$
$endif$
$if(biblatex)$
$if(beamer)$
\begin{frame}[allowframebreaks]{$biblio-title$}
  \bibliographytrue
  \printbibliography[heading=none]
\end{frame}
$else$
\printbibliography$if(biblio-title)$[title=$biblio-title$]$endif$
$endif$

$endif$
$for(include-after)$
$include-after$

$endfor$
\end{document}
$for(include-before)$
$include-before$

$endfor$
$if(toc)$
__TOC__

$endif$
$body$
$for(include-after)$

$include-after$
$endfor$
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:epub="http://www.idpf.org/2007/ops"$if(lang)$ lang="$lang$" xml:lang="$lang$"$endif$>
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <title>$pagetitle$</title>
  <style>
$if(csl-css)$
    $styles.citations.html()$
$endif$
$if(highlighting-css)$
    /* CSS for syntax highlighting */
    $highlighting-css$
$endif$
  </style>
$for(css)$
  <link rel="stylesheet" type="text/css" href="$css$" />
$endfor$
$for(header-includes)$
  $header-includes$
$endfor$
</head>
<body$if(coverpage)$ id="cover"$endif$$if(body-type)$ epub:type="$body-type$"$endif$>
$if(titlepage)$
<section epub:type="titlepage" class="titlepage">
$for(title)$
$if(title.type)$
  <h1 class="$title.type$">$title.text$</h1>
$else$
  <h1 class="title">$title$</h1>
$endif$
$endfor$
$if(subtitle)$
  <p class="subtitle">$subtitle$</p>
$endif$
$for(author)$
  <p class="author">$author$</p>
$endfor$
$for(creator)$
  <p class="$creator.role$">$creator.text$</p>
$endfor$
$if(publisher)$
  <p class="publisher">$publisher$</p>
$endif$
$if(date)$
  <p class="date">$date$</p>
$endif$
$if(rights)$
  <div class="rights">$rights$</div>
$endif$
$if(abstract)$
<div class="abstract">
<div class="abstract-title">$abstract-title$</div>
$abstract$
</div>
$endif$
</section>
$else$
$if(coverpage)$
<div id="cover-image">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="100%" height="100%" viewBox="0 0 $cover-image-width$ $cover-image-height$" preserveAspectRatio="xMidYMid">
<image width="$cover-image-width$" height="$cover-image-height$" xlink:href="../media/$cover-image$" />
</svg>
</div>
$else$
$for(include-before)$
$include-before$
$endfor$
$body$
$for(include-after)$
$include-after$
$endfor$
$endif$
$endif$
</body>
</html>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"$if(lang)$ lang="$lang$" xml:lang="$lang$"$endif$>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta http-equiv="Content-Style-Type" content="text/css" />
  <meta name="generator" content="pandoc" />
  <title>$pagetitle$</title>
  <style type="text/css">
$if(csl-css)$
    $styles.citations.html()$
$endif$
$if(highlighting-css)$
    /* CSS for syntax highlighting */
    $highlighting-css$
$endif$
  </style>
$for(css)$
  <link rel="stylesheet" type="text/css" href="$css$" />
$endfor$
$for(header-includes)$
  $header-includes$
$endfor$
</head>
<body$if(coverpage)$ id="cover"$endif$>
$if(titlepage)$
$for(title)$
$if(title.text)$
  <h1 class="$title.type$">$title.text$</h1>
$else$
  <h1 class="title">$title$</h1>
$endif$
$endfor$
$if(subtitle)$
  <p class="subtitle">$subtitle$</p>
$endif$
$for(author)$
  <p class="author">$author$</p>
$endfor$
$for(creator)$
  <p class="$creator.role$">$creator.text$</p>
$endfor$
$if(publisher)$
  <p class="publisher">$publisher$</p>
$endif$
$if(date)$
  <p class="date">$date$</p>
$endif$
$if(rights)$
  <div class="rights">$rights$</div>
$endif$
$if(abstract)$
<div class="abstract">
<div class="abstract-title">$abstract-title$</div>
$abstract$
</div>
$endif$
$else$
$if(coverpage)$
<div id="cover-image">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="100%" height="100%" viewBox="0 0 $cover-image-width$ $cover-image-height$" preserveAspectRatio="xMidYMid">
<image width="$cover-image-width$" height="$cover-image-height$" xlink:href="../media/$cover-image$" />
</svg>
</div>
$else$
$for(include-before)$
$include-before$
$endfor$
$body$
$for(include-after)$
$include-after$
$endfor$
$endif$
$endif$
</body>
</html>

<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<?aid style="50" type="snippet" readerVersion="6.0" featureSet="513" product="8.0(370)" ?>
<?aid SnippetType="InCopyInterchange"?>
<Document DOMVersion="8.0" Self="pandoc_doc">
    <RootCharacterStyleGroup Self="pandoc_character_styles">
      <CharacterStyle Self="$$ID/NormalCharacterStyle" Name="Default" />
      $charStyles$
    </RootCharacterStyleGroup>
    <RootParagraphStyleGroup Self="pandoc_paragraph_styles">
      <ParagraphStyle Self="$$ID/NormalParagraphStyle" Name="$$ID/NormalParagraphStyle"
          SpaceBefore="6" SpaceAfter="6"> <!-- paragraph spacing -->
        <Properties>
          <TabList type="list">
            <ListItem type="record">
              <Alignment type="enumeration">LeftAlign</Alignment>
              <AlignmentCharacter type="string">.</AlignmentCharacter>
              <Leader type="string"></Leader>
              <Position type="unit">10</Position> <!-- first tab stop -->
            </ListItem>
          </TabList>
        </Properties>
      </ParagraphStyle>
      $parStyles$
    </RootParagraphStyleGroup>
    <RootTableStyleGroup Self="pandoc_table_styles">
      <TableStyle Self="TableStyle/Table" Name="Table" />
    </RootTableStyleGroup>
    <RootCellStyleGroup Self="pandoc_cell_styles">
      <CellStyle Self="CellStyle/Cell" AppliedParagraphStyle="ParagraphStyle/$$ID/[No paragraph style]" Name="Cell" />
    </RootCellStyleGroup>
  <Story Self="pandoc_story"
      TrackChanges="false"
      StoryTitle="$if(title-prefix)$$title-prefix$ – $endif$$pagetitle$"
      AppliedTOCStyle="n"
      AppliedNamedGrid="n" >
    <StoryPreference OpticalMarginAlignment="true" OpticalMarginSize="12" />

<!-- body needs to be non-indented, otherwise code blocks are indented too far -->
$body$

  </Story>
  $hyperlinks$
</Document>
$if(has-tables)$
'\" t
$endif$
$if(pandoc-version)$
.\" Automatically generated by Pandoc $pandoc-version$
.\"
$endif$
$if(adjusting)$
.ad $adjusting$
$endif$
.TH "$title/nowrap$" "$section/nowrap$" "$date/nowrap$" "$footer/nowrap$" "$header/nowrap$"
$for(header-includes)$
$header-includes$
$endfor$
$for(include-before)$
$include-before$
$endfor$
$body$
$for(include-after)$
$include-after$
$endfor$
$if(author)$
.SH AUTHORS
$for(author)$$author$$sep$; $endfor$.
$endif$
#let conf(
  title: none,
  authors: (),
  keywords: (),
  date: none,
  abstract: none,
  cols: 1,
  margin: (x: 1.25in, y: 1.25in),
  paper: "us-letter",
  lang: "en",
  region: "US",
  font: (),
  fontsize: 11pt,
  sectionnumbering: none,
  doc,
) = {
  set document(
    title: title,
    author: authors.map(author => author.name),
    keywords: keywords,
  )
  set page(
    paper: paper,
    margin: margin,
    numbering: "1",
  )
  set par(justify: true)
  set text(lang: lang,
           region: region,
           font: font,
           size: fontsize)
  set heading(numbering: sectionnumbering)

  if title != none {
    align(center)[#block(inset: 2em)[
      #text(weight: "bold", size: 1.5em)[#title]
    ]]
  }

  if authors != none and authors != [] {
    let count = authors.len()
    let ncols = calc.min(count, 3)
    grid(
      columns: (1fr,) * ncols,
      row-gutter: 1.5em,
      ..authors.map(author =>
          align(center)[
            #author.name \
            #author.affiliation \
            #author.email
          ]
      )
    )
  }

  if date != none {
    align(center)[#block(inset: 1em)[
      #date
    ]]
  }

  if abstract != none {
    block(inset: 2em)[
    #text(weight: "semibold")[Abstract] #h(1em) #abstract
    ]
  }

  if cols == 1 {
    doc
  } else {
    columns(cols, doc)
  }
}
$for(include-before)$
$include-before$

$endfor$
$body$
$for(include-after)$

$include-after$
$endfor$
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="$lang$" xml:lang="$lang$"$if(dir)$ dir="$dir$"$endif$>
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
$for(author-meta)$
  <meta name="author" content="$author-meta$" />
$endfor$
$if(date-meta)$
  <meta name="dcterms.date" content="$date-meta$" />
$endif$
$if(keywords)$
  <meta name="keywords" content="$for(keywords)$$keywords$$sep$, $endfor$" />
$endif$
$if(description-meta)$
  <meta name="description" content="$description-meta$" />
$endif$
  <title>$if(title-prefix)$$title-prefix$ – $endif$$pagetitle$</title>
  <style>
    $styles.html()$
  </style>
$for(css)$
  <link rel="stylesheet" href="$css$" />
$endfor$
$for(header-includes)$
  $header-includes$
$endfor$
$if(math)$
$if(mathjax)$
  <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
$endif$
  $math$
$endif$
</head>
<body>
$for(include-before)$
$include-before$
$endfor$
$if(title)$
<header id="title-block-header">
<h1 class="title">$title$</h1>
$if(subtitle)$
<p class="subtitle">$subtitle$</p>
$endif$
$for(author)$
<p class="author">$author$</p>
$endfor$
$if(date)$
<p class="date">$date$</p>
$endif$
$if(abstract)$
<div class="abstract">
<div class="abstract-title">$abstract-title$</div>
$abstract$
</div>
$endif$
</header>
$endif$
$if(toc)$
<nav id="$idprefix$TOC" role="doc-toc">
$if(toc-title)$
<h2 id="$idprefix$toc-title">$toc-title$</h2>
$endif$
$table-of-contents$
</nav>
$endif$
$body$
$for(include-after)$
$include-after$
$endfor$
</body>
</html>
<?xml version="1.0" encoding="utf-8" ?>
$if(xml-stylesheet)$
<?xml-stylesheet type="text/xsl" href="$xml-stylesheet$"?>
$endif$
<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Article Authoring DTD v1.2 20190208//EN"
                  "JATS-articleauthoring1.dtd">
$if(article.type)$
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" dtd-version="1.2" article-type="$article.type$">
$else$
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" dtd-version="1.2" article-type="other">
$endif$
<front>
<article-meta>
$if(title)$
<title-group>
<article-title>$title$</article-title>
$if(subtitle)$
<subtitle>${subtitle}</subtitle>
$endif$
</title-group>
$endif$
$if(author)$
<contrib-group>
$for(author)$
<contrib contrib-type="author"$if(author.equal-contrib)$ equal-contrib="yes"$endif$$if(author.cor-id)$ corresp="yes"$endif$>
$if(author.orcid)$
<contrib-id contrib-id-type="orcid">$author.orcid$</contrib-id>
$endif$
$if(author.surname)$
<name>
<surname>$if(author.non-dropping-particle)$${author.non-dropping-particle} $endif$${author.surname}</surname>
<given-names>${author.given-names}$if(author.dropping-particle)$ ${author.dropping-particle}$endif$</given-names>
$if(author.prefix)$
<prefix>${author.suffix}</prefix>
$endif$
$if(author.suffix)$
<suffix>${author.suffix}</suffix>
$endif$
</name>
$elseif(author.name)$
<string-name>$author.name$</string-name>
$else$
<string-name>$author$</string-name>
$endif$
$for(author.affiliation)$
${ it:affiliations.jats() }
$endfor$
$if(author.email)$
<email>$author.email$</email>
$endif$
$if(author.cor-id)$
<xref ref-type="corresp" rid="cor-$author.cor-id$"><sup>*</sup></xref>
$endif$
</contrib>
$endfor$
</contrib-group>
$endif$
<permissions>
$for(copyright.statement)$
<copyright-statement>$copyright.statement$</copyright-statement>
$endfor$
$for(copyright.year)$
<copyright-year>$copyright.year$</copyright-year>
$endfor$
$for(copyright.holder)$
<copyright-holder>$copyright.holder$</copyright-holder>
$endfor$
$if(copyright.text)$
<license license-type="$copyright.type$" xlink:href="$copyright.link$">
<license-p>$copyright.text$</license-p>
</license>
$endif$
$for(license)$
<license$if(it.type)$ license-type="${it.type}"$endif$$if(it.link)$ xlink:href="${it.link}"$endif$>
<license-p>$if(it.text)$${it.text}$else$${it}$endif$</license-p>
</license>
$endfor$
</permissions>
<abstract>
$abstract$
</abstract>
$if(tags)$
<kwd-group kwd-group-type="author">
$for(tags)$
<kwd>$tags$</kwd>
$endfor$
</kwd-group>
$endif$
$if(article.funding-statement)$
<funding-group>
<funding-statement>$article.funding-statement$</funding-statement>
</funding-group>
$endif$
</article-meta>
</front>
<body>
$body$
</body>
<back>
$if(back)$
$back$
$endif$
</back>
</article>
<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE article>
<article
$if(lang)$
  xml:lang="$lang$"
$endif$
  xmlns="http://docbook.org/ns/docbook" version="5.0"
$if(mathml)$
  xmlns:mml="http://www.w3.org/1998/Math/MathML"
$endif$
  xmlns:xlink="http://www.w3.org/1999/xlink" >
  <info>
    <title>$title$</title>
$if(subtitle)$
    <subtitle>$subtitle$</subtitle>
$endif$
$if(author)$
    <authorgroup>
$for(author)$
      <author>
        $author$
      </author>
$endfor$
    </authorgroup>
$endif$
$if(date)$
    <date>$date$</date>
$endif$
  </info>
$for(include-before)$
  $include-before$
$endfor$
  $body$
$for(include-after)$
  $include-after$
$endfor$
</article>
$if(pandoc-version)$
.\" Automatically generated by Pandoc $pandoc-version$
.\"
$endif$
.\" **** Custom macro definitions *********************************
.\" * Super/subscript
.\" (https://lists.gnu.org/archive/html/groff/2012-07/msg00046.html)
.ds { \v'-0.3m'\\s[\\n[.s]*9u/12u]
.ds } \s0\v'0.3m'
.ds < \v'0.3m'\s[\\n[.s]*9u/12u]
.ds > \s0\v'-0.3m'
.\" * Horizontal line
.de HLINE
.LP
.ce
\l'20'
..
$if(highlighting-macros)$
.\" * Syntax highlighting macros
$highlighting-macros$
$endif$
.\" **** Settings *************************************************
.\" text width
.nr LL 5.5i
.\" left margin
.nr PO 1.25i
.\" top margin
.nr HM 1.25i
.\" bottom margin
.nr FM 1.25i
.\" header/footer width
.nr LT \n[LL]
.\" point size
.nr PS $if(pointsize)$$pointsize$$else$10p$endif$
.\" line height
.nr VS $if(lineheight)$$lineheight$$else$12p$endif$
.\" font family: A, BM, H, HN, N, P, T, ZCM
.fam $if(fontfamily)$$fontfamily$$else$P$endif$
.\" paragraph indent
.nr PI $if(indent)$$indent$$else$0m$endif$
.\" interparagraph space
.nr PD 0.4v
.\" footnote width
.nr FL \n[LL]
.\" footnote point size
.nr FPS (\n[PS] - 2000)
$if(papersize)$
.\" paper size
.ds paper $papersize$
$endif$
.\" color used for strikeout
.defcolor strikecolor rgb 0.7 0.7 0.7
.\" color for links (rgb)
.ds PDFHREF.COLOUR   0.35 0.00 0.60
.\" border for links (default none)
.ds PDFHREF.BORDER   0 0 0
.\" point size difference between heading levels
.nr PSINCR 1p
.\" heading level above which point size no longer changes
.nr GROWPS 2
.\" comment these out if you want a dot after section numbers:
.als SN SN-NO-DOT
.als SN-STYLE SN-NO-DOT
.\" page numbers in footer, centered
.ds CH
.ds CF %
.\" pdf outline fold level
.nr PDFOUTLINE.FOLDLEVEL 3
.\" start out in outline view
.pdfview /PageMode /UseOutlines
.\" ***************************************************************
.\" PDF metadata
.pdfinfo /Title "$title-meta$"
.pdfinfo /Author "$author-meta$"
$if(adjusting)$
.ad $adjusting$
$endif$
$if(hyphenate)$
.hy
$else$
.nh
$endif$
$if(has-inline-math)$
.EQ
delim @@
.EN
$endif$
$for(header-includes)$
$header-includes$
$endfor$
$if(title)$
.TL
$title$
$endif$
$for(author)$
.AU
$author$
$endfor$
$if(date)$
.AU
.sp 0.5
.ft R
$date$
$endif$
$if(abstract)$
.AB
$abstract$
.AE
$endif$
.\" 1 column (use .2C for two column)
.1C
$for(include-before)$
$include-before$
$endfor$
$body$
$if(toc)$
.TC
$endif$
$for(include-after)$
$include-after$
$endfor$
.pdfsync
$body$
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"$if(lang)$ lang="$lang$" xml:lang="$lang$"$endif$$if(dir)$ dir="$dir$"$endif$>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta http-equiv="Content-Style-Type" content="text/css" />
  <meta name="generator" content="pandoc" />
$for(author-meta)$
  <meta name="version" content="S5 1.1" />
  <meta name="author" content="$author-meta$" />
$endfor$
$if(date-meta)$
  <meta name="date" content="$date-meta$" />
$endif$
$if(keywords)$
  <meta name="keywords" content="$for(keywords)$$keywords$$sep$, $endfor$" />
$endif$
  <title>$if(title-prefix)$$title-prefix$ – $endif$$pagetitle$</title>
  <style type="text/css">
    $styles.html()$
  </style>
  <!-- configuration parameters -->
  <meta name="defaultView" content="slideshow" />
  <meta name="controlVis" content="hidden" />
$for(css)$
  <link rel="stylesheet" href="$css$" type="text/css" />
$endfor$
  <!-- style sheet links -->
  <link rel="stylesheet" href="$s5-url$/slides.css" type="text/css" media="projection" id="slideProj" />
  <link rel="stylesheet" href="$s5-url$/outline.css" type="text/css" media="screen" id="outlineStyle" />
  <link rel="stylesheet" href="$s5-url$/print.css" type="text/css" media="print" id="slidePrint" />
  <link rel="stylesheet" href="$s5-url$/opera.css" type="text/css" media="projection" id="operaFix" />
  <!-- S5 JS -->
  <script src="$s5-url$/slides.js" type="text/javascript"></script>
$if(math)$
  $math$
$endif$
$for(header-includes)$
  $header-includes$
$endfor$
</head>
<body>
$for(include-before)$
$include-before$
$endfor$
<div class="layout">
<div id="controls"></div>
<div id="currentSlide"></div>
<div id="header"></div>
<div id="footer">
  <h1>$date$</h1>
  <h2>$title$</h2>
</div>
</div>
<div class="presentation">
$if(title)$
<div class="title-slide slide">
  <h1 class="title">$title$</h1>
$if(subtitle)$
  <h2 class="subtitle">$subtitle$</h2>
$endif$
$if(author)$
  <h3 class="author">$for(author)$$author$$sep$<br/>$endfor$</h3>
$endif$
$if(institute)$
  <h3 class="institute">$for(institute)$$institute$$sep$<br/>$endfor$</h3>
$endif$
$if(date)$
  <h4 class="date">$date$</h4>
$endif$
</div>
$endif$
$if(toc)$
<div class="slide" id="$idprefix$TOC">
$table-of-contents$
</div>
$endif$
$body$
$for(include-after)$
$include-after$
$endfor$
</div>
</body>
</html>
$if(author)$
#author $for(author)$$author$$sep$; $endfor$
$endif$
$if(title)$
#title $title$
$endif$
$if(lang)$
#lang $lang$
$endif$
$if(LISTtitle)$
#LISTtitle $LISTtitle$
$endif$
$if(subtitle)$
#subtitle $subtitle$
$endif$
$if(SORTauthors)$
#SORTauthors $SORTauthors$
$endif$
$if(SORTtopics)$
#SORTtopics $SORTtopics$
$endif$
$if(date)$
#date $date$
$endif$
$if(notes)$
#notes $notes$
$endif$
$if(source)$
#source $source$
$endif$

$for(header-includes)$
$header-includes$

$endfor$
$for(include-before)$
$include-before$

$endfor$
$body$
$for(include-after)$

$include-after$
$endfor$
<?xml version="1.0" encoding="UTF-8"?>
<opml version="2.0">
  <head>
    <title>$title$</title>
    <dateModified>$date$</dateModified>
    <ownerName>$for(author)$$author$$sep$; $endfor$</ownerName>
  </head>
  <body>
$body$
  </body>
</opml>
$--
$-- Affiliations
$--
<aff id="aff-$it.id$">
$-- wrap affiliation if it has a known institution identifier
$if(it.group)$
<institution content-type="group">${it.group}</institution>
$endif$
$if(it.department)$
<institution content-type="dept">${it.department}</institution>
$endif$
<institution-wrap>
$if(it.organization)$
<institution>${it.organization}</institution>
$else$
<institution>${it.name}</institution>
$endif$
$if(it.isni)$
<institution-id institution-id-type="ISNI">${it.isni}</institution-id>
$endif$
$if(it.ringgold)$
<institution-id institution-id-type="Ringgold">${it.ringgold}</institution-id>
$endif$
$if(it.ror)$
<institution-id institution-id-type="ROR">${it.ror}</institution-id>
$endif$
$for(it.pid)$
<institution-id institution-id-type="${it.type}">${it.id}</institution-id>
$endfor$
</institution-wrap>$if(it.street-address)$,
$for(it.street-address)$
<addr-line>${it}</addr-line>$sep$,
$endfor$
$else$$if(it.city)$, <city>$it.city$</city>$endif$$endif$$if(it.country)$,
<country$if(it.country-code)$ country="$it.country-code$"$endif$>$it.country$</country>$endif$
</aff>
<?xml version="1.0" encoding="utf-8"?>
<TEI xmlns="http://www.tei-c.org/ns/1.0"$if(lang)$ xml:lang="$lang$"$endif$>
<teiHeader>
  <fileDesc>
    <titleStmt>
      <title>$title$</title>
$for(author)$
      <author>$author$</author>
$endfor$
    </titleStmt>
    <publicationStmt>
$if(publicationStmt)$
      <p>$if(publicationStmt)$$publicationStmt$$endif$</p>
$endif$
$if(license)$
      <availability><licence>$license$</licence></availability>
$endif$
$if(publisher)$
      <publisher>$publisher$</publisher>
$endif$
$if(pubPlace)$
      <pubPlace>$pubPlace$</pubPlace>
$endif$
$if(address)$
      <address>$address$</address>
$endif$
$if(date)$
      <date>$date$</date>
$endif$
    </publicationStmt>
    <sourceDesc>
$if(sourceDesc)$
      $sourceDesc$
$else$
      <p>Produced by pandoc.</p>
$endif$
    </sourceDesc>
  </fileDesc>
</teiHeader>
<text>
$for(include-before)$
$include-before$
$endfor$
<body>
$body$
</body>
$for(include-after)$
$include-after$
$endfor$
</text>
</TEI>
$if(titleblock)$
= $title$
$if(author)$
$for(author)$$author$$sep$; $endfor$
$if(date)$
$date$
$endif$
$elseif(date)$
:revdate: $date$
$endif$
$if(keywords)$
:keywords: $for(keywords)$$keywords$$sep$, $endfor$
$endif$
$if(lang)$
:lang: $lang$
$endif$
$if(toc)$
:toc:
$endif$
$if(math)$
:stem: latexmath
$endif$

$endif$
$if(abstract)$
[abstract]
== Abstract
$abstract$

$endif$
$for(header-includes)$
$header-includes$

$endfor$
$for(include-before)$
$include-before$

$endfor$
$body$
$for(include-after)$

$include-after$
$endfor$
<?xml version="1.0" encoding="utf-8" ?>
<office:document-content xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:style="urn:oasis:names:tc:opendocument:xmlns:style:1.0" xmlns:text="urn:oasis:names:tc:opendocument:xmlns:text:1.0" xmlns:table="urn:oasis:names:tc:opendocument:xmlns:table:1.0" xmlns:draw="urn:oasis:names:tc:opendocument:xmlns:drawing:1.0" xmlns:fo="urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:number="urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0" xmlns:svg="urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0" xmlns:chart="urn:oasis:names:tc:opendocument:xmlns:chart:1.0" xmlns:dr3d="urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0" xmlns:math="http://www.w3.org/1998/Math/MathML" xmlns:form="urn:oasis:names:tc:opendocument:xmlns:form:1.0" xmlns:script="urn:oasis:names:tc:opendocument:xmlns:script:1.0" xmlns:ooo="http://openoffice.org/2004/office" xmlns:ooow="http://openoffice.org/2004/writer" xmlns:oooc="http://openoffice.org/2004/calc" xmlns:dom="http://www.w3.org/2001/xml-events" xmlns:xforms="http://www.w3.org/2002/xforms" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" office:version="1.3">
  <office:font-face-decls>
    <style:font-face style:name="Courier New" style:font-family-generic="modern" style:font-pitch="fixed" svg:font-family="'Courier New'" />
  </office:font-face-decls>
  <office:automatic-styles>
    $automatic-styles$
  </office:automatic-styles>
$for(header-includes)$
  $header-includes$
$endfor$
<office:body>
<office:text>
$if(title)$
<text:p text:style-name="Title">$title$</text:p>
$endif$
$if(subtitle)$
<text:p text:style-name="Subtitle">$subtitle$</text:p>
$endif$
$for(author)$
<text:p text:style-name="Author">$author$</text:p>
$endfor$
$if(date)$
<text:p text:style-name="Date">$date$</text:p>
$endif$
$if(abstract)$
$abstract$
$endif$
$for(include-before)$
$include-before$
$endfor$
$if(toc)$
<text:table-of-content>
  <text:table-of-content-source text:outline-level="$toc-depth$">
    <text:index-title-template text:style-name="Contents_20_Heading">$toc-title$</text:index-title-template>
    <text:table-of-content-entry-template text:outline-level="1"
    text:style-name="Contents_20_1">
      <text:index-entry-link-start text:style-name="Internet_20_link" />
      <text:index-entry-chapter />
      <text:index-entry-text />
      <text:index-entry-link-end />
      <text:index-entry-tab-stop style:type="right"
      style:leader-char="." />
      <text:index-entry-link-start text:style-name="Internet_20_link" />
      <text:index-entry-page-number />
      <text:index-entry-link-end />
    </text:table-of-content-entry-template>
    <text:table-of-content-entry-template text:outline-level="2"
    text:style-name="Contents_20_2">
      <text:index-entry-link-start text:style-name="Internet_20_link" />
      <text:index-entry-chapter />
      <text:index-entry-text />
      <text:index-entry-link-end />
      <text:index-entry-tab-stop style:type="right"
      style:leader-char="." />
      <text:index-entry-link-start text:style-name="Internet_20_link" />
      <text:index-entry-page-number />
      <text:index-entry-link-end />
    </text:table-of-content-entry-template>
    <text:table-of-content-entry-template text:outline-level="3"
    text:style-name="Contents_20_3">
      <text:index-entry-link-start text:style-name="Internet_20_link" />
      <text:index-entry-chapter />
      <text:index-entry-text />
      <text:index-entry-link-end />
      <text:index-entry-tab-stop style:type="right"
      style:leader-char="." />
      <text:index-entry-link-start text:style-name="Internet_20_link" />
      <text:index-entry-page-number />
      <text:index-entry-link-end />
    </text:table-of-content-entry-template>
    <text:table-of-content-entry-template text:outline-level="4"
    text:style-name="Contents_20_4">
      <text:index-entry-link-start text:style-name="Internet_20_link" />
      <text:index-entry-chapter />
      <text:index-entry-text />
      <text:index-entry-link-end />
      <text:index-entry-tab-stop style:type="right"
      style:leader-char="." />
      <text:index-entry-link-start text:style-name="Internet_20_link" />
      <text:index-entry-page-number />
      <text:index-entry-link-end />
    </text:table-of-content-entry-template>
    <text:table-of-content-entry-template text:outline-level="5"
    text:style-name="Contents_20_5">
      <text:index-entry-link-start text:style-name="Internet_20_link" />
      <text:index-entry-chapter />
      <text:index-entry-text />
      <text:index-entry-link-end />
      <text:index-entry-tab-stop style:type="right"
      style:leader-char="." />
      <text:index-entry-link-start text:style-name="Internet_20_link" />
      <text:index-entry-page-number />
      <text:index-entry-link-end />
    </text:table-of-content-entry-template>
    <text:table-of-content-entry-template text:outline-level="6"
    text:style-name="Contents_20_6">
      <text:index-entry-link-start text:style-name="Internet_20_link" />
      <text:index-entry-chapter />
      <text:index-entry-text />
      <text:index-entry-link-end />
      <text:index-entry-tab-stop style:type="right"
      style:leader-char="." />
      <text:index-entry-link-start text:style-name="Internet_20_link" />
      <text:index-entry-page-number />
      <text:index-entry-link-end />
    </text:table-of-content-entry-template>
    <text:table-of-content-entry-template text:outline-level="7"
    text:style-name="Contents_20_7">
      <text:index-entry-link-start text:style-name="Internet_20_link" />
      <text:index-entry-chapter />
      <text:index-entry-text />
      <text:index-entry-link-end />
      <text:index-entry-tab-stop style:type="right"
      style:leader-char="." />
      <text:index-entry-link-start text:style-name="Internet_20_link" />
      <text:index-entry-page-number />
      <text:index-entry-link-end />
    </text:table-of-content-entry-template>
    <text:table-of-content-entry-template text:outline-level="8"
    text:style-name="Contents_20_8">
      <text:index-entry-link-start text:style-name="Internet_20_link" />
      <text:index-entry-chapter />
      <text:index-entry-text />
      <text:index-entry-link-end />
      <text:index-entry-tab-stop style:type="right"
      style:leader-char="." />
      <text:index-entry-link-start text:style-name="Internet_20_link" />
      <text:index-entry-page-number />
      <text:index-entry-link-end />
    </text:table-of-content-entry-template>
    <text:table-of-content-entry-template text:outline-level="9"
    text:style-name="Contents_20_9">
      <text:index-entry-link-start text:style-name="Internet_20_link" />
      <text:index-entry-chapter />
      <text:index-entry-text />
      <text:index-entry-link-end />
      <text:index-entry-tab-stop style:type="right"
      style:leader-char="." />
      <text:index-entry-link-start text:style-name="Internet_20_link" />
      <text:index-entry-page-number />
      <text:index-entry-link-end />
    </text:table-of-content-entry-template>
    <text:table-of-content-entry-template text:outline-level="10"
    text:style-name="Contents_20_10">
      <text:index-entry-link-start text:style-name="Internet_20_link" />
      <text:index-entry-chapter />
      <text:index-entry-text />
      <text:index-entry-link-end />
      <text:index-entry-tab-stop style:type="right"
      style:leader-char="." />
      <text:index-entry-link-start text:style-name="Internet_20_link" />
      <text:index-entry-page-number />
      <text:index-entry-link-end />
    </text:table-of-content-entry-template>
  </text:table-of-content-source>
</text:table-of-content>
$endif$
$body$
$for(include-after)$
$include-after$
$endfor$
</office:text>
</office:body>
</office:document-content>
<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types"><Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/><Default Extension="xml" ContentType="application/xml"/><Override PartName="/ppt/presentation.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml"/><Override PartName="/ppt/slideMasters/slideMaster1.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slideMaster+xml"/><Override PartName="/ppt/slides/slide1.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slide+xml"/><Override PartName="/ppt/slides/slide2.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slide+xml"/><Override PartName="/ppt/slides/slide3.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slide+xml"/><Override PartName="/ppt/slides/slide4.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slide+xml"/><Override PartName="/ppt/notesMasters/notesMaster1.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.notesMaster+xml"/><Override PartName="/ppt/presProps.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.presProps+xml"/><Override PartName="/ppt/viewProps.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.viewProps+xml"/><Override PartName="/ppt/theme/theme1.xml" ContentType="application/vnd.openxmlformats-officedocument.theme+xml"/><Override PartName="/ppt/tableStyles.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.tableStyles+xml"/><Override PartName="/ppt/slideLayouts/slideLayout1.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml"/><Override PartName="/ppt/slideLayouts/slideLayout2.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml"/><Override PartName="/ppt/slideLayouts/slideLayout3.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml"/><Override PartName="/ppt/slideLayouts/slideLayout4.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml"/><Override PartName="/ppt/slideLayouts/slideLayout5.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml"/><Override PartName="/ppt/slideLayouts/slideLayout6.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml"/><Override PartName="/ppt/slideLayouts/slideLayout7.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml"/><Override PartName="/ppt/slideLayouts/slideLayout8.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml"/><Override PartName="/ppt/slideLayouts/slideLayout9.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml"/><Override PartName="/ppt/slideLayouts/slideLayout10.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml"/><Override PartName="/ppt/slideLayouts/slideLayout11.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml"/><Override PartName="/ppt/theme/theme2.xml" ContentType="application/vnd.openxmlformats-officedocument.theme+xml"/><Override PartName="/ppt/notesSlides/notesSlide1.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.notesSlide+xml"/><Override PartName="/ppt/notesSlides/notesSlide2.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.notesSlide+xml"/><Override PartName="/docProps/core.xml" ContentType="application/vnd.openxmlformats-package.core-properties+xml"/><Override PartName="/docProps/app.xml" ContentType="application/vnd.openxmlformats-officedocument.extended-properties+xml"/></Types><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<cp:coreProperties xmlns:cp="http://schemas.openxmlformats.org/package/2006/metadata/core-properties" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:dcmitype="http://purl.org/dc/dcmitype/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><dc:title>Title</dc:title><dc:creator>Jesse Rosenthal</dc:creator><cp:lastModifiedBy>Jesse Rosenthal</cp:lastModifiedBy><cp:revision>5</cp:revision><dcterms:created xsi:type="dcterms:W3CDTF">2017-06-05T14:10:58Z</dcterms:created><dcterms:modified xsi:type="dcterms:W3CDTF">2022-01-02T22:49:03Z</dcterms:modified></cp:coreProperties>
<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Properties xmlns="http://schemas.openxmlformats.org/officeDocument/2006/extended-properties" xmlns:vt="http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes"><TotalTime>2</TotalTime><Words>49</Words><Application>Microsoft Macintosh PowerPoint</Application><PresentationFormat>On-screen Show (16:9)</PresentationFormat><Paragraphs>15</Paragraphs><Slides>4</Slides><Notes>2</Notes><HiddenSlides>0</HiddenSlides><MMClips>0</MMClips><ScaleCrop>false</ScaleCrop><HeadingPairs><vt:vector size="6" baseType="variant"><vt:variant><vt:lpstr>Fonts Used</vt:lpstr></vt:variant><vt:variant><vt:i4>2</vt:i4></vt:variant><vt:variant><vt:lpstr>Theme</vt:lpstr></vt:variant><vt:variant><vt:i4>1</vt:i4></vt:variant><vt:variant><vt:lpstr>Slide Titles</vt:lpstr></vt:variant><vt:variant><vt:i4>4</vt:i4></vt:variant></vt:vector></HeadingPairs><TitlesOfParts><vt:vector size="7" baseType="lpstr"><vt:lpstr>Arial</vt:lpstr><vt:lpstr>Calibri</vt:lpstr><vt:lpstr>Office Theme</vt:lpstr><vt:lpstr>Presentation Title</vt:lpstr><vt:lpstr>Slide Title</vt:lpstr><vt:lpstr>Section header</vt:lpstr><vt:lpstr>Slide Title for Two-Content</vt:lpstr></vt:vector></TitlesOfParts><Company></Company><LinksUpToDate>false</LinksUpToDate><SharedDoc>false</SharedDoc><HyperlinksChanged>false</HyperlinksChanged><AppVersion>16.0000</AppVersion></Properties><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId3" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties" Target="docProps/app.xml"/><Relationship Id="rId2" Type="http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties" Target="docProps/core.xml"/><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="ppt/presentation.xml"/></Relationships><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:sldLayout xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main" type="title" preserve="1"><p:cSld name="Title Slide"><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="2" name="Title 1"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="ctrTitle"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="685800" y="1597819"/><a:ext cx="7772400" cy="1102519"/></a:xfrm></p:spPr><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master title style</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="3" name="Subtitle 2"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="subTitle" idx="1"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="1371600" y="2914650"/><a:ext cx="6400800" cy="1314450"/></a:xfrm></p:spPr><p:txBody><a:bodyPr/><a:lstStyle><a:lvl1pPr marL="0" indent="0" algn="ctr"><a:buNone/><a:defRPr><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl1pPr><a:lvl2pPr marL="342900" indent="0" algn="ctr"><a:buNone/><a:defRPr><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl2pPr><a:lvl3pPr marL="685800" indent="0" algn="ctr"><a:buNone/><a:defRPr><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl3pPr><a:lvl4pPr marL="1028700" indent="0" algn="ctr"><a:buNone/><a:defRPr><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl4pPr><a:lvl5pPr marL="1371600" indent="0" algn="ctr"><a:buNone/><a:defRPr><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl5pPr><a:lvl6pPr marL="1714500" indent="0" algn="ctr"><a:buNone/><a:defRPr><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl6pPr><a:lvl7pPr marL="2057400" indent="0" algn="ctr"><a:buNone/><a:defRPr><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl7pPr><a:lvl8pPr marL="2400300" indent="0" algn="ctr"><a:buNone/><a:defRPr><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl8pPr><a:lvl9pPr marL="2743200" indent="0" algn="ctr"><a:buNone/><a:defRPr><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl9pPr></a:lstStyle><a:p><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master subtitle style</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="4" name="Date Placeholder 3"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="dt" sz="half" idx="10"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{241EB5C9-1307-BA42-ABA2-0BC069CD8E7F}" type="datetimeFigureOut"><a:rPr lang="en-US" smtClean="0"/><a:t>1/2/22</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="5" name="Footer Placeholder 4"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="ftr" sz="quarter" idx="11"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="6" name="Slide Number Placeholder 5"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldNum" sz="quarter" idx="12"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{C5EF2332-01BF-834F-8236-50238282D533}" type="slidenum"><a:rPr lang="en-US" smtClean="0"/><a:t>‹#›</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="1444357513"/></p:ext></p:extLst></p:cSld><p:clrMapOvr><a:masterClrMapping/></p:clrMapOvr></p:sldLayout><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:sldLayout xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main" type="secHead" preserve="1"><p:cSld name="Section Header"><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="2" name="Title 1"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="title"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="722313" y="3305176"/><a:ext cx="7772400" cy="1021556"/></a:xfrm></p:spPr><p:txBody><a:bodyPr anchor="t"/><a:lstStyle><a:lvl1pPr algn="l"><a:defRPr sz="3000" b="1" cap="all"/></a:lvl1pPr></a:lstStyle><a:p><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master title style</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="3" name="Text Placeholder 2"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="body" idx="1"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="722313" y="2180035"/><a:ext cx="7772400" cy="1125140"/></a:xfrm></p:spPr><p:txBody><a:bodyPr anchor="b"/><a:lstStyle><a:lvl1pPr marL="0" indent="0"><a:buNone/><a:defRPr sz="1500"><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl1pPr><a:lvl2pPr marL="342900" indent="0"><a:buNone/><a:defRPr sz="1350"><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl2pPr><a:lvl3pPr marL="685800" indent="0"><a:buNone/><a:defRPr sz="1200"><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl3pPr><a:lvl4pPr marL="1028700" indent="0"><a:buNone/><a:defRPr sz="1050"><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl4pPr><a:lvl5pPr marL="1371600" indent="0"><a:buNone/><a:defRPr sz="1050"><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl5pPr><a:lvl6pPr marL="1714500" indent="0"><a:buNone/><a:defRPr sz="1050"><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl6pPr><a:lvl7pPr marL="2057400" indent="0"><a:buNone/><a:defRPr sz="1050"><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl7pPr><a:lvl8pPr marL="2400300" indent="0"><a:buNone/><a:defRPr sz="1050"><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl8pPr><a:lvl9pPr marL="2743200" indent="0"><a:buNone/><a:defRPr sz="1050"><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl9pPr></a:lstStyle><a:p><a:pPr lvl="0"/><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master text styles</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="4" name="Date Placeholder 3"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="dt" sz="half" idx="10"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{241EB5C9-1307-BA42-ABA2-0BC069CD8E7F}" type="datetimeFigureOut"><a:rPr lang="en-US" smtClean="0"/><a:t>1/2/22</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="5" name="Footer Placeholder 4"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="ftr" sz="quarter" idx="11"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="6" name="Slide Number Placeholder 5"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldNum" sz="quarter" idx="12"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{C5EF2332-01BF-834F-8236-50238282D533}" type="slidenum"><a:rPr lang="en-US" smtClean="0"/><a:t>‹#›</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="1073069076"/></p:ext></p:extLst></p:cSld><p:clrMapOvr><a:masterClrMapping/></p:clrMapOvr></p:sldLayout><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:sldLayout xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main" type="obj" preserve="1"><p:cSld name="Title and Content"><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="2" name="Title 1"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="title"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master title style</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="3" name="Content Placeholder 2"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph idx="1"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:pPr lvl="0"/><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master text styles</a:t></a:r></a:p><a:p><a:pPr lvl="1"/><a:r><a:rPr lang="en-US"/><a:t>Second level</a:t></a:r></a:p><a:p><a:pPr lvl="2"/><a:r><a:rPr lang="en-US"/><a:t>Third level</a:t></a:r></a:p><a:p><a:pPr lvl="3"/><a:r><a:rPr lang="en-US"/><a:t>Fourth level</a:t></a:r></a:p><a:p><a:pPr lvl="4"/><a:r><a:rPr lang="en-US"/><a:t>Fifth level</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="4" name="Date Placeholder 3"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="dt" sz="half" idx="10"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{241EB5C9-1307-BA42-ABA2-0BC069CD8E7F}" type="datetimeFigureOut"><a:rPr lang="en-US" smtClean="0"/><a:t>1/2/22</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="5" name="Footer Placeholder 4"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="ftr" sz="quarter" idx="11"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="6" name="Slide Number Placeholder 5"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldNum" sz="quarter" idx="12"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{C5EF2332-01BF-834F-8236-50238282D533}" type="slidenum"><a:rPr lang="en-US" smtClean="0"/><a:t>‹#›</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="338346009"/></p:ext></p:extLst></p:cSld><p:clrMapOvr><a:masterClrMapping/></p:clrMapOvr></p:sldLayout><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:sldLayout xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main" type="vertTx" preserve="1"><p:cSld name="Title and Vertical Text"><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="2" name="Title 1"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="title"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master title style</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="3" name="Vertical Text Placeholder 2"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="body" orient="vert" idx="1"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr vert="eaVert"/><a:lstStyle/><a:p><a:pPr lvl="0"/><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master text styles</a:t></a:r></a:p><a:p><a:pPr lvl="1"/><a:r><a:rPr lang="en-US"/><a:t>Second level</a:t></a:r></a:p><a:p><a:pPr lvl="2"/><a:r><a:rPr lang="en-US"/><a:t>Third level</a:t></a:r></a:p><a:p><a:pPr lvl="3"/><a:r><a:rPr lang="en-US"/><a:t>Fourth level</a:t></a:r></a:p><a:p><a:pPr lvl="4"/><a:r><a:rPr lang="en-US"/><a:t>Fifth level</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="4" name="Date Placeholder 3"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="dt" sz="half" idx="10"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{241EB5C9-1307-BA42-ABA2-0BC069CD8E7F}" type="datetimeFigureOut"><a:rPr lang="en-US" smtClean="0"/><a:t>1/2/22</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="5" name="Footer Placeholder 4"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="ftr" sz="quarter" idx="11"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="6" name="Slide Number Placeholder 5"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldNum" sz="quarter" idx="12"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{C5EF2332-01BF-834F-8236-50238282D533}" type="slidenum"><a:rPr lang="en-US" smtClean="0"/><a:t>‹#›</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="313914798"/></p:ext></p:extLst></p:cSld><p:clrMapOvr><a:masterClrMapping/></p:clrMapOvr></p:sldLayout><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:sldLayout xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main" type="twoObj" preserve="1"><p:cSld name="Two Content"><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="2" name="Title 1"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="title"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master title style</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="3" name="Content Placeholder 2"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph sz="half" idx="1"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="457200" y="1200151"/><a:ext cx="4038600" cy="3394472"/></a:xfrm></p:spPr><p:txBody><a:bodyPr/><a:lstStyle><a:lvl1pPr><a:defRPr sz="2100"/></a:lvl1pPr><a:lvl2pPr><a:defRPr sz="1800"/></a:lvl2pPr><a:lvl3pPr><a:defRPr sz="1500"/></a:lvl3pPr><a:lvl4pPr><a:defRPr sz="1350"/></a:lvl4pPr><a:lvl5pPr><a:defRPr sz="1350"/></a:lvl5pPr><a:lvl6pPr><a:defRPr sz="1350"/></a:lvl6pPr><a:lvl7pPr><a:defRPr sz="1350"/></a:lvl7pPr><a:lvl8pPr><a:defRPr sz="1350"/></a:lvl8pPr><a:lvl9pPr><a:defRPr sz="1350"/></a:lvl9pPr></a:lstStyle><a:p><a:pPr lvl="0"/><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master text styles</a:t></a:r></a:p><a:p><a:pPr lvl="1"/><a:r><a:rPr lang="en-US"/><a:t>Second level</a:t></a:r></a:p><a:p><a:pPr lvl="2"/><a:r><a:rPr lang="en-US"/><a:t>Third level</a:t></a:r></a:p><a:p><a:pPr lvl="3"/><a:r><a:rPr lang="en-US"/><a:t>Fourth level</a:t></a:r></a:p><a:p><a:pPr lvl="4"/><a:r><a:rPr lang="en-US"/><a:t>Fifth level</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="4" name="Content Placeholder 3"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph sz="half" idx="2"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="4648200" y="1200151"/><a:ext cx="4038600" cy="3394472"/></a:xfrm></p:spPr><p:txBody><a:bodyPr/><a:lstStyle><a:lvl1pPr><a:defRPr sz="2100"/></a:lvl1pPr><a:lvl2pPr><a:defRPr sz="1800"/></a:lvl2pPr><a:lvl3pPr><a:defRPr sz="1500"/></a:lvl3pPr><a:lvl4pPr><a:defRPr sz="1350"/></a:lvl4pPr><a:lvl5pPr><a:defRPr sz="1350"/></a:lvl5pPr><a:lvl6pPr><a:defRPr sz="1350"/></a:lvl6pPr><a:lvl7pPr><a:defRPr sz="1350"/></a:lvl7pPr><a:lvl8pPr><a:defRPr sz="1350"/></a:lvl8pPr><a:lvl9pPr><a:defRPr sz="1350"/></a:lvl9pPr></a:lstStyle><a:p><a:pPr lvl="0"/><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master text styles</a:t></a:r></a:p><a:p><a:pPr lvl="1"/><a:r><a:rPr lang="en-US"/><a:t>Second level</a:t></a:r></a:p><a:p><a:pPr lvl="2"/><a:r><a:rPr lang="en-US"/><a:t>Third level</a:t></a:r></a:p><a:p><a:pPr lvl="3"/><a:r><a:rPr lang="en-US"/><a:t>Fourth level</a:t></a:r></a:p><a:p><a:pPr lvl="4"/><a:r><a:rPr lang="en-US"/><a:t>Fifth level</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="5" name="Date Placeholder 4"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="dt" sz="half" idx="10"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{241EB5C9-1307-BA42-ABA2-0BC069CD8E7F}" type="datetimeFigureOut"><a:rPr lang="en-US" smtClean="0"/><a:t>1/2/22</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="6" name="Footer Placeholder 5"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="ftr" sz="quarter" idx="11"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="7" name="Slide Number Placeholder 6"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldNum" sz="quarter" idx="12"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{C5EF2332-01BF-834F-8236-50238282D533}" type="slidenum"><a:rPr lang="en-US" smtClean="0"/><a:t>‹#›</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="2619886245"/></p:ext></p:extLst></p:cSld><p:clrMapOvr><a:masterClrMapping/></p:clrMapOvr></p:sldLayout><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideMaster" Target="../slideMasters/slideMaster1.xml"/></Relationships><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideMaster" Target="../slideMasters/slideMaster1.xml"/></Relationships><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideMaster" Target="../slideMasters/slideMaster1.xml"/></Relationships><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideMaster" Target="../slideMasters/slideMaster1.xml"/></Relationships><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideMaster" Target="../slideMasters/slideMaster1.xml"/></Relationships><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideMaster" Target="../slideMasters/slideMaster1.xml"/></Relationships><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideMaster" Target="../slideMasters/slideMaster1.xml"/></Relationships><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideMaster" Target="../slideMasters/slideMaster1.xml"/></Relationships><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideMaster" Target="../slideMasters/slideMaster1.xml"/></Relationships><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideMaster" Target="../slideMasters/slideMaster1.xml"/></Relationships><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideMaster" Target="../slideMasters/slideMaster1.xml"/></Relationships><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:sldLayout xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main" type="titleOnly" preserve="1"><p:cSld name="Title Only"><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="2" name="Title 1"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="title"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master title style</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="3" name="Date Placeholder 2"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="dt" sz="half" idx="10"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{241EB5C9-1307-BA42-ABA2-0BC069CD8E7F}" type="datetimeFigureOut"><a:rPr lang="en-US" smtClean="0"/><a:t>1/2/22</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="4" name="Footer Placeholder 3"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="ftr" sz="quarter" idx="11"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="5" name="Slide Number Placeholder 4"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldNum" sz="quarter" idx="12"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{C5EF2332-01BF-834F-8236-50238282D533}" type="slidenum"><a:rPr lang="en-US" smtClean="0"/><a:t>‹#›</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="3472721253"/></p:ext></p:extLst></p:cSld><p:clrMapOvr><a:masterClrMapping/></p:clrMapOvr></p:sldLayout><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:sldLayout xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main" type="objTx" preserve="1"><p:cSld name="Content with Caption"><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="2" name="Title 1"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="title"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="457201" y="204787"/><a:ext cx="3008313" cy="871538"/></a:xfrm></p:spPr><p:txBody><a:bodyPr anchor="b"/><a:lstStyle><a:lvl1pPr algn="l"><a:defRPr sz="1500" b="1"/></a:lvl1pPr></a:lstStyle><a:p><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master title style</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="3" name="Content Placeholder 2"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph idx="1"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="3575050" y="204788"/><a:ext cx="5111750" cy="4389835"/></a:xfrm></p:spPr><p:txBody><a:bodyPr/><a:lstStyle><a:lvl1pPr><a:defRPr sz="2400"/></a:lvl1pPr><a:lvl2pPr><a:defRPr sz="2100"/></a:lvl2pPr><a:lvl3pPr><a:defRPr sz="1800"/></a:lvl3pPr><a:lvl4pPr><a:defRPr sz="1500"/></a:lvl4pPr><a:lvl5pPr><a:defRPr sz="1500"/></a:lvl5pPr><a:lvl6pPr><a:defRPr sz="1500"/></a:lvl6pPr><a:lvl7pPr><a:defRPr sz="1500"/></a:lvl7pPr><a:lvl8pPr><a:defRPr sz="1500"/></a:lvl8pPr><a:lvl9pPr><a:defRPr sz="1500"/></a:lvl9pPr></a:lstStyle><a:p><a:pPr lvl="0"/><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master text styles</a:t></a:r></a:p><a:p><a:pPr lvl="1"/><a:r><a:rPr lang="en-US"/><a:t>Second level</a:t></a:r></a:p><a:p><a:pPr lvl="2"/><a:r><a:rPr lang="en-US"/><a:t>Third level</a:t></a:r></a:p><a:p><a:pPr lvl="3"/><a:r><a:rPr lang="en-US"/><a:t>Fourth level</a:t></a:r></a:p><a:p><a:pPr lvl="4"/><a:r><a:rPr lang="en-US"/><a:t>Fifth level</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="4" name="Text Placeholder 3"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="body" sz="half" idx="2"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="457201" y="1076326"/><a:ext cx="3008313" cy="3518297"/></a:xfrm></p:spPr><p:txBody><a:bodyPr/><a:lstStyle><a:lvl1pPr marL="0" indent="0"><a:buNone/><a:defRPr sz="1050"/></a:lvl1pPr><a:lvl2pPr marL="342900" indent="0"><a:buNone/><a:defRPr sz="900"/></a:lvl2pPr><a:lvl3pPr marL="685800" indent="0"><a:buNone/><a:defRPr sz="750"/></a:lvl3pPr><a:lvl4pPr marL="1028700" indent="0"><a:buNone/><a:defRPr sz="675"/></a:lvl4pPr><a:lvl5pPr marL="1371600" indent="0"><a:buNone/><a:defRPr sz="675"/></a:lvl5pPr><a:lvl6pPr marL="1714500" indent="0"><a:buNone/><a:defRPr sz="675"/></a:lvl6pPr><a:lvl7pPr marL="2057400" indent="0"><a:buNone/><a:defRPr sz="675"/></a:lvl7pPr><a:lvl8pPr marL="2400300" indent="0"><a:buNone/><a:defRPr sz="675"/></a:lvl8pPr><a:lvl9pPr marL="2743200" indent="0"><a:buNone/><a:defRPr sz="675"/></a:lvl9pPr></a:lstStyle><a:p><a:pPr lvl="0"/><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master text styles</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="5" name="Date Placeholder 4"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="dt" sz="half" idx="10"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{241EB5C9-1307-BA42-ABA2-0BC069CD8E7F}" type="datetimeFigureOut"><a:rPr lang="en-US" smtClean="0"/><a:t>1/2/22</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="6" name="Footer Placeholder 5"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="ftr" sz="quarter" idx="11"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="7" name="Slide Number Placeholder 6"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldNum" sz="quarter" idx="12"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{C5EF2332-01BF-834F-8236-50238282D533}" type="slidenum"><a:rPr lang="en-US" smtClean="0"/><a:t>‹#›</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="3540895647"/></p:ext></p:extLst></p:cSld><p:clrMapOvr><a:masterClrMapping/></p:clrMapOvr></p:sldLayout><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:sldLayout xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main" type="picTx" preserve="1"><p:cSld name="Picture with Caption"><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="2" name="Title 1"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="title"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="1792288" y="3600450"/><a:ext cx="5486400" cy="425054"/></a:xfrm></p:spPr><p:txBody><a:bodyPr anchor="b"/><a:lstStyle><a:lvl1pPr algn="l"><a:defRPr sz="1500" b="1"/></a:lvl1pPr></a:lstStyle><a:p><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master title style</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="3" name="Picture Placeholder 2"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="pic" idx="1"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="1792288" y="459581"/><a:ext cx="5486400" cy="3086100"/></a:xfrm></p:spPr><p:txBody><a:bodyPr/><a:lstStyle><a:lvl1pPr marL="0" indent="0"><a:buNone/><a:defRPr sz="2400"/></a:lvl1pPr><a:lvl2pPr marL="342900" indent="0"><a:buNone/><a:defRPr sz="2100"/></a:lvl2pPr><a:lvl3pPr marL="685800" indent="0"><a:buNone/><a:defRPr sz="1800"/></a:lvl3pPr><a:lvl4pPr marL="1028700" indent="0"><a:buNone/><a:defRPr sz="1500"/></a:lvl4pPr><a:lvl5pPr marL="1371600" indent="0"><a:buNone/><a:defRPr sz="1500"/></a:lvl5pPr><a:lvl6pPr marL="1714500" indent="0"><a:buNone/><a:defRPr sz="1500"/></a:lvl6pPr><a:lvl7pPr marL="2057400" indent="0"><a:buNone/><a:defRPr sz="1500"/></a:lvl7pPr><a:lvl8pPr marL="2400300" indent="0"><a:buNone/><a:defRPr sz="1500"/></a:lvl8pPr><a:lvl9pPr marL="2743200" indent="0"><a:buNone/><a:defRPr sz="1500"/></a:lvl9pPr></a:lstStyle><a:p><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="4" name="Text Placeholder 3"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="body" sz="half" idx="2"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="1792288" y="4025503"/><a:ext cx="5486400" cy="603647"/></a:xfrm></p:spPr><p:txBody><a:bodyPr/><a:lstStyle><a:lvl1pPr marL="0" indent="0"><a:buNone/><a:defRPr sz="1050"/></a:lvl1pPr><a:lvl2pPr marL="342900" indent="0"><a:buNone/><a:defRPr sz="900"/></a:lvl2pPr><a:lvl3pPr marL="685800" indent="0"><a:buNone/><a:defRPr sz="750"/></a:lvl3pPr><a:lvl4pPr marL="1028700" indent="0"><a:buNone/><a:defRPr sz="675"/></a:lvl4pPr><a:lvl5pPr marL="1371600" indent="0"><a:buNone/><a:defRPr sz="675"/></a:lvl5pPr><a:lvl6pPr marL="1714500" indent="0"><a:buNone/><a:defRPr sz="675"/></a:lvl6pPr><a:lvl7pPr marL="2057400" indent="0"><a:buNone/><a:defRPr sz="675"/></a:lvl7pPr><a:lvl8pPr marL="2400300" indent="0"><a:buNone/><a:defRPr sz="675"/></a:lvl8pPr><a:lvl9pPr marL="2743200" indent="0"><a:buNone/><a:defRPr sz="675"/></a:lvl9pPr></a:lstStyle><a:p><a:pPr lvl="0"/><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master text styles</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="5" name="Date Placeholder 4"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="dt" sz="half" idx="10"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{241EB5C9-1307-BA42-ABA2-0BC069CD8E7F}" type="datetimeFigureOut"><a:rPr lang="en-US" smtClean="0"/><a:t>1/2/22</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="6" name="Footer Placeholder 5"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="ftr" sz="quarter" idx="11"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="7" name="Slide Number Placeholder 6"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldNum" sz="quarter" idx="12"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{C5EF2332-01BF-834F-8236-50238282D533}" type="slidenum"><a:rPr lang="en-US" smtClean="0"/><a:t>‹#›</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="3566899855"/></p:ext></p:extLst></p:cSld><p:clrMapOvr><a:masterClrMapping/></p:clrMapOvr></p:sldLayout><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:sldLayout xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main" type="twoTxTwoObj" preserve="1"><p:cSld name="Comparison"><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="2" name="Title 1"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="title"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle><a:lvl1pPr><a:defRPr/></a:lvl1pPr></a:lstStyle><a:p><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master title style</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="3" name="Text Placeholder 2"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="body" idx="1"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="457200" y="1151335"/><a:ext cx="4040188" cy="479822"/></a:xfrm></p:spPr><p:txBody><a:bodyPr anchor="b"/><a:lstStyle><a:lvl1pPr marL="0" indent="0"><a:buNone/><a:defRPr sz="1800" b="1"/></a:lvl1pPr><a:lvl2pPr marL="342900" indent="0"><a:buNone/><a:defRPr sz="1500" b="1"/></a:lvl2pPr><a:lvl3pPr marL="685800" indent="0"><a:buNone/><a:defRPr sz="1350" b="1"/></a:lvl3pPr><a:lvl4pPr marL="1028700" indent="0"><a:buNone/><a:defRPr sz="1200" b="1"/></a:lvl4pPr><a:lvl5pPr marL="1371600" indent="0"><a:buNone/><a:defRPr sz="1200" b="1"/></a:lvl5pPr><a:lvl6pPr marL="1714500" indent="0"><a:buNone/><a:defRPr sz="1200" b="1"/></a:lvl6pPr><a:lvl7pPr marL="2057400" indent="0"><a:buNone/><a:defRPr sz="1200" b="1"/></a:lvl7pPr><a:lvl8pPr marL="2400300" indent="0"><a:buNone/><a:defRPr sz="1200" b="1"/></a:lvl8pPr><a:lvl9pPr marL="2743200" indent="0"><a:buNone/><a:defRPr sz="1200" b="1"/></a:lvl9pPr></a:lstStyle><a:p><a:pPr lvl="0"/><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master text styles</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="4" name="Content Placeholder 3"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph sz="half" idx="2"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="457200" y="1631156"/><a:ext cx="4040188" cy="2963466"/></a:xfrm></p:spPr><p:txBody><a:bodyPr/><a:lstStyle><a:lvl1pPr><a:defRPr sz="1800"/></a:lvl1pPr><a:lvl2pPr><a:defRPr sz="1500"/></a:lvl2pPr><a:lvl3pPr><a:defRPr sz="1350"/></a:lvl3pPr><a:lvl4pPr><a:defRPr sz="1200"/></a:lvl4pPr><a:lvl5pPr><a:defRPr sz="1200"/></a:lvl5pPr><a:lvl6pPr><a:defRPr sz="1200"/></a:lvl6pPr><a:lvl7pPr><a:defRPr sz="1200"/></a:lvl7pPr><a:lvl8pPr><a:defRPr sz="1200"/></a:lvl8pPr><a:lvl9pPr><a:defRPr sz="1200"/></a:lvl9pPr></a:lstStyle><a:p><a:pPr lvl="0"/><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master text styles</a:t></a:r></a:p><a:p><a:pPr lvl="1"/><a:r><a:rPr lang="en-US"/><a:t>Second level</a:t></a:r></a:p><a:p><a:pPr lvl="2"/><a:r><a:rPr lang="en-US"/><a:t>Third level</a:t></a:r></a:p><a:p><a:pPr lvl="3"/><a:r><a:rPr lang="en-US"/><a:t>Fourth level</a:t></a:r></a:p><a:p><a:pPr lvl="4"/><a:r><a:rPr lang="en-US"/><a:t>Fifth level</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="5" name="Text Placeholder 4"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="body" sz="quarter" idx="3"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="4645026" y="1151335"/><a:ext cx="4041775" cy="479822"/></a:xfrm></p:spPr><p:txBody><a:bodyPr anchor="b"/><a:lstStyle><a:lvl1pPr marL="0" indent="0"><a:buNone/><a:defRPr sz="1800" b="1"/></a:lvl1pPr><a:lvl2pPr marL="342900" indent="0"><a:buNone/><a:defRPr sz="1500" b="1"/></a:lvl2pPr><a:lvl3pPr marL="685800" indent="0"><a:buNone/><a:defRPr sz="1350" b="1"/></a:lvl3pPr><a:lvl4pPr marL="1028700" indent="0"><a:buNone/><a:defRPr sz="1200" b="1"/></a:lvl4pPr><a:lvl5pPr marL="1371600" indent="0"><a:buNone/><a:defRPr sz="1200" b="1"/></a:lvl5pPr><a:lvl6pPr marL="1714500" indent="0"><a:buNone/><a:defRPr sz="1200" b="1"/></a:lvl6pPr><a:lvl7pPr marL="2057400" indent="0"><a:buNone/><a:defRPr sz="1200" b="1"/></a:lvl7pPr><a:lvl8pPr marL="2400300" indent="0"><a:buNone/><a:defRPr sz="1200" b="1"/></a:lvl8pPr><a:lvl9pPr marL="2743200" indent="0"><a:buNone/><a:defRPr sz="1200" b="1"/></a:lvl9pPr></a:lstStyle><a:p><a:pPr lvl="0"/><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master text styles</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="6" name="Content Placeholder 5"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph sz="quarter" idx="4"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="4645026" y="1631156"/><a:ext cx="4041775" cy="2963466"/></a:xfrm></p:spPr><p:txBody><a:bodyPr/><a:lstStyle><a:lvl1pPr><a:defRPr sz="1800"/></a:lvl1pPr><a:lvl2pPr><a:defRPr sz="1500"/></a:lvl2pPr><a:lvl3pPr><a:defRPr sz="1350"/></a:lvl3pPr><a:lvl4pPr><a:defRPr sz="1200"/></a:lvl4pPr><a:lvl5pPr><a:defRPr sz="1200"/></a:lvl5pPr><a:lvl6pPr><a:defRPr sz="1200"/></a:lvl6pPr><a:lvl7pPr><a:defRPr sz="1200"/></a:lvl7pPr><a:lvl8pPr><a:defRPr sz="1200"/></a:lvl8pPr><a:lvl9pPr><a:defRPr sz="1200"/></a:lvl9pPr></a:lstStyle><a:p><a:pPr lvl="0"/><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master text styles</a:t></a:r></a:p><a:p><a:pPr lvl="1"/><a:r><a:rPr lang="en-US"/><a:t>Second level</a:t></a:r></a:p><a:p><a:pPr lvl="2"/><a:r><a:rPr lang="en-US"/><a:t>Third level</a:t></a:r></a:p><a:p><a:pPr lvl="3"/><a:r><a:rPr lang="en-US"/><a:t>Fourth level</a:t></a:r></a:p><a:p><a:pPr lvl="4"/><a:r><a:rPr lang="en-US"/><a:t>Fifth level</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="7" name="Date Placeholder 6"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="dt" sz="half" idx="10"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{241EB5C9-1307-BA42-ABA2-0BC069CD8E7F}" type="datetimeFigureOut"><a:rPr lang="en-US" smtClean="0"/><a:t>1/2/22</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="8" name="Footer Placeholder 7"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="ftr" sz="quarter" idx="11"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="9" name="Slide Number Placeholder 8"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldNum" sz="quarter" idx="12"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{C5EF2332-01BF-834F-8236-50238282D533}" type="slidenum"><a:rPr lang="en-US" smtClean="0"/><a:t>‹#›</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="2535793967"/></p:ext></p:extLst></p:cSld><p:clrMapOvr><a:masterClrMapping/></p:clrMapOvr></p:sldLayout><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:sldLayout xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main" type="blank" preserve="1"><p:cSld name="Blank"><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="2" name="Date Placeholder 1"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="dt" sz="half" idx="10"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{241EB5C9-1307-BA42-ABA2-0BC069CD8E7F}" type="datetimeFigureOut"><a:rPr lang="en-US" smtClean="0"/><a:t>1/2/22</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="3" name="Footer Placeholder 2"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="ftr" sz="quarter" idx="11"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="4" name="Slide Number Placeholder 3"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldNum" sz="quarter" idx="12"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{C5EF2332-01BF-834F-8236-50238282D533}" type="slidenum"><a:rPr lang="en-US" smtClean="0"/><a:t>‹#›</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="2130901097"/></p:ext></p:extLst></p:cSld><p:clrMapOvr><a:masterClrMapping/></p:clrMapOvr></p:sldLayout><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:sldLayout xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main" type="vertTitleAndTx" preserve="1"><p:cSld name="Vertical Title and Text"><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="2" name="Vertical Title 1"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="title" orient="vert"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="6629400" y="205979"/><a:ext cx="2057400" cy="4388644"/></a:xfrm></p:spPr><p:txBody><a:bodyPr vert="eaVert"/><a:lstStyle/><a:p><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master title style</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="3" name="Vertical Text Placeholder 2"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="body" orient="vert" idx="1"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="457200" y="205979"/><a:ext cx="6019800" cy="4388644"/></a:xfrm></p:spPr><p:txBody><a:bodyPr vert="eaVert"/><a:lstStyle/><a:p><a:pPr lvl="0"/><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master text styles</a:t></a:r></a:p><a:p><a:pPr lvl="1"/><a:r><a:rPr lang="en-US"/><a:t>Second level</a:t></a:r></a:p><a:p><a:pPr lvl="2"/><a:r><a:rPr lang="en-US"/><a:t>Third level</a:t></a:r></a:p><a:p><a:pPr lvl="3"/><a:r><a:rPr lang="en-US"/><a:t>Fourth level</a:t></a:r></a:p><a:p><a:pPr lvl="4"/><a:r><a:rPr lang="en-US"/><a:t>Fifth level</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="4" name="Date Placeholder 3"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="dt" sz="half" idx="10"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{241EB5C9-1307-BA42-ABA2-0BC069CD8E7F}" type="datetimeFigureOut"><a:rPr lang="en-US" smtClean="0"/><a:t>1/2/22</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="5" name="Footer Placeholder 4"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="ftr" sz="quarter" idx="11"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="6" name="Slide Number Placeholder 5"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldNum" sz="quarter" idx="12"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{C5EF2332-01BF-834F-8236-50238282D533}" type="slidenum"><a:rPr lang="en-US" smtClean="0"/><a:t>‹#›</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="2581529045"/></p:ext></p:extLst></p:cSld><p:clrMapOvr><a:masterClrMapping/></p:clrMapOvr></p:sldLayout><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:presentation xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main" saveSubsetFonts="1" autoCompressPictures="0"><p:sldMasterIdLst><p:sldMasterId id="2147483648" r:id="rId1"/></p:sldMasterIdLst><p:notesMasterIdLst><p:notesMasterId r:id="rId6"/></p:notesMasterIdLst><p:sldIdLst><p:sldId id="256" r:id="rId2"/><p:sldId id="257" r:id="rId3"/><p:sldId id="258" r:id="rId4"/><p:sldId id="259" r:id="rId5"/></p:sldIdLst><p:sldSz cx="9144000" cy="5143500" type="screen16x9"/><p:notesSz cx="6858000" cy="9144000"/><p:defaultTextStyle><a:defPPr><a:defRPr lang="en-US"/></a:defPPr><a:lvl1pPr marL="0" algn="l" defTabSz="457200" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl1pPr><a:lvl2pPr marL="457200" algn="l" defTabSz="457200" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl2pPr><a:lvl3pPr marL="914400" algn="l" defTabSz="457200" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl3pPr><a:lvl4pPr marL="1371600" algn="l" defTabSz="457200" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl4pPr><a:lvl5pPr marL="1828800" algn="l" defTabSz="457200" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl5pPr><a:lvl6pPr marL="2286000" algn="l" defTabSz="457200" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl6pPr><a:lvl7pPr marL="2743200" algn="l" defTabSz="457200" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl7pPr><a:lvl8pPr marL="3200400" algn="l" defTabSz="457200" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl8pPr><a:lvl9pPr marL="3657600" algn="l" defTabSz="457200" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl9pPr></p:defaultTextStyle><p:extLst><p:ext uri="{EFAFB233-063F-42B5-8137-9DF3F51BA10A}"><p15:sldGuideLst xmlns:p15="http://schemas.microsoft.com/office/powerpoint/2012/main"><p15:guide id="1" orient="horz" pos="1620" userDrawn="1"><p15:clr><a:srgbClr val="A4A3A4"/></p15:clr></p15:guide><p15:guide id="2" pos="2880" userDrawn="1"><p15:clr><a:srgbClr val="A4A3A4"/></p15:clr></p15:guide></p15:sldGuideLst></p:ext></p:extLst></p:presentation><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:sld xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main"><p:cSld><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="2" name="Title 1"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="title"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:r><a:rPr lang="en-US" dirty="0"/><a:t>Slide Title</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="3" name="Content Placeholder 2"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph idx="1"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:pPr marL="0" indent="0"><a:buNone/></a:pPr><a:r><a:rPr lang="en-US" dirty="0"/><a:t>Hello</a:t></a:r><a:r><a:rPr lang="en-US"/><a:t>, world.</a:t></a:r></a:p></p:txBody></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="572707455"/></p:ext></p:extLst></p:cSld><p:clrMapOvr><a:masterClrMapping/></p:clrMapOvr></p:sld><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout" Target="../slideLayouts/slideLayout3.xml"/></Relationships><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/notesSlide" Target="../notesSlides/notesSlide1.xml"/><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout" Target="../slideLayouts/slideLayout1.xml"/></Relationships><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/notesSlide" Target="../notesSlides/notesSlide2.xml"/><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout" Target="../slideLayouts/slideLayout2.xml"/></Relationships><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout" Target="../slideLayouts/slideLayout4.xml"/></Relationships><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:sld xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main"><p:cSld><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="4" name="Title 3"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="title"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:r><a:rPr lang="en-US" dirty="0"/><a:t>Slide Title for Two-Content</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="5" name="Content Placeholder 4"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph sz="half" idx="1"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:r><a:rPr lang="en-US" dirty="0"/><a:t>Some content on </a:t></a:r><a:r><a:rPr lang="en-US"/><a:t>the left.</a:t></a:r><a:endParaRPr lang="en-US" dirty="0"/></a:p><a:p><a:pPr marL="0" indent="0"><a:buNone/></a:pPr><a:endParaRPr lang="en-US" dirty="0"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="6" name="Content Placeholder 5"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph sz="half" idx="2"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:r><a:rPr lang="en-US" dirty="0"/><a:t>Some content on the right.</a:t></a:r></a:p></p:txBody></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="1324621109"/></p:ext></p:extLst></p:cSld><p:clrMapOvr><a:masterClrMapping/></p:clrMapOvr></p:sld><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:sld xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main"><p:cSld><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="2" name="Title 1"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="ctrTitle"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:r><a:rPr lang="en-US" dirty="0"/><a:t>Presentation Title</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="3" name="Subtitle 2"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="subTitle" idx="1"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:r><a:rPr lang="en-US" dirty="0"/><a:t>Presentation Subtitle</a:t></a:r></a:p></p:txBody></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="392669009"/></p:ext></p:extLst></p:cSld><p:clrMapOvr><a:masterClrMapping/></p:clrMapOvr></p:sld><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:sld xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main"><p:cSld><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="2" name="Title 1"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="title"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:r><a:rPr lang="en-US" dirty="0"/><a:t>Section header</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="3" name="Text Placeholder 2"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="body" idx="1"/></p:nvPr></p:nvSpPr><p:spPr/></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="3996781534"/></p:ext></p:extLst></p:cSld><p:clrMapOvr><a:masterClrMapping/></p:clrMapOvr></p:sld><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId8" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/viewProps" Target="viewProps.xml"/><Relationship Id="rId3" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slide" Target="slides/slide2.xml"/><Relationship Id="rId7" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/presProps" Target="presProps.xml"/><Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slide" Target="slides/slide1.xml"/><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideMaster" Target="slideMasters/slideMaster1.xml"/><Relationship Id="rId6" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/notesMaster" Target="notesMasters/notesMaster1.xml"/><Relationship Id="rId5" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slide" Target="slides/slide4.xml"/><Relationship Id="rId10" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/tableStyles" Target="tableStyles.xml"/><Relationship Id="rId4" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slide" Target="slides/slide3.xml"/><Relationship Id="rId9" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme" Target="theme/theme1.xml"/></Relationships><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:presentationPr xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main"><p:extLst><p:ext uri="{E76CE94A-603C-4142-B9EB-6D1370010A27}"><p14:discardImageEditData xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="0"/></p:ext><p:ext uri="{D31A062A-798A-4329-ABDD-BBA856620510}"><p14:defaultImageDpi xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="0"/></p:ext><p:ext uri="{FD5EFAAD-0ECE-453E-9831-46B23BE46B34}"><p15:chartTrackingRefBased xmlns:p15="http://schemas.microsoft.com/office/powerpoint/2012/main" val="0"/></p:ext></p:extLst></p:presentationPr><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<a:tblStyleLst xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" def="{5C22544A-7EE6-4342-B048-85BDC9FD1C3A}"/><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:notesMaster xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main"><p:cSld><p:bg><p:bgRef idx="1001"><a:schemeClr val="bg1"/></p:bgRef></p:bg><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="2" name="Header Placeholder 1"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="hdr" sz="quarter"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="2971800" cy="458788"/></a:xfrm><a:prstGeom prst="rect"><a:avLst/></a:prstGeom></p:spPr><p:txBody><a:bodyPr vert="horz" lIns="91440" tIns="45720" rIns="91440" bIns="45720" rtlCol="0"/><a:lstStyle><a:lvl1pPr algn="l"><a:defRPr sz="1200"/></a:lvl1pPr></a:lstStyle><a:p><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="3" name="Date Placeholder 2"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="dt" idx="1"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="3884613" y="0"/><a:ext cx="2971800" cy="458788"/></a:xfrm><a:prstGeom prst="rect"><a:avLst/></a:prstGeom></p:spPr><p:txBody><a:bodyPr vert="horz" lIns="91440" tIns="45720" rIns="91440" bIns="45720" rtlCol="0"/><a:lstStyle><a:lvl1pPr algn="r"><a:defRPr sz="1200"/></a:lvl1pPr></a:lstStyle><a:p><a:fld id="{0F9C1CCF-B725-44A7-AA57-5E433BD85C9F}" type="datetimeFigureOut"><a:rPr lang="en-US" smtClean="0"/><a:t>1/2/22</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="4" name="Slide Image Placeholder 3"/><p:cNvSpPr><a:spLocks noGrp="1" noRot="1" noChangeAspect="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldImg" idx="2"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="685800" y="1143000"/><a:ext cx="5486400" cy="3086100"/></a:xfrm><a:prstGeom prst="rect"><a:avLst/></a:prstGeom><a:noFill/><a:ln w="12700"><a:solidFill><a:prstClr val="black"/></a:solidFill></a:ln></p:spPr><p:txBody><a:bodyPr vert="horz" lIns="91440" tIns="45720" rIns="91440" bIns="45720" rtlCol="0" anchor="ctr"/><a:lstStyle/><a:p><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="5" name="Notes Placeholder 4"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="body" sz="quarter" idx="3"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="685800" y="4400550"/><a:ext cx="5486400" cy="3600450"/></a:xfrm><a:prstGeom prst="rect"><a:avLst/></a:prstGeom></p:spPr><p:txBody><a:bodyPr vert="horz" lIns="91440" tIns="45720" rIns="91440" bIns="45720" rtlCol="0"/><a:lstStyle/><a:p><a:pPr lvl="0"/><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master text styles</a:t></a:r></a:p><a:p><a:pPr lvl="1"/><a:r><a:rPr lang="en-US"/><a:t>Second level</a:t></a:r></a:p><a:p><a:pPr lvl="2"/><a:r><a:rPr lang="en-US"/><a:t>Third level</a:t></a:r></a:p><a:p><a:pPr lvl="3"/><a:r><a:rPr lang="en-US"/><a:t>Fourth level</a:t></a:r></a:p><a:p><a:pPr lvl="4"/><a:r><a:rPr lang="en-US"/><a:t>Fifth level</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="6" name="Footer Placeholder 5"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="ftr" sz="quarter" idx="4"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="0" y="8685213"/><a:ext cx="2971800" cy="458787"/></a:xfrm><a:prstGeom prst="rect"><a:avLst/></a:prstGeom></p:spPr><p:txBody><a:bodyPr vert="horz" lIns="91440" tIns="45720" rIns="91440" bIns="45720" rtlCol="0" anchor="b"/><a:lstStyle><a:lvl1pPr algn="l"><a:defRPr sz="1200"/></a:lvl1pPr></a:lstStyle><a:p><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="7" name="Slide Number Placeholder 6"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldNum" sz="quarter" idx="5"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="3884613" y="8685213"/><a:ext cx="2971800" cy="458787"/></a:xfrm><a:prstGeom prst="rect"><a:avLst/></a:prstGeom></p:spPr><p:txBody><a:bodyPr vert="horz" lIns="91440" tIns="45720" rIns="91440" bIns="45720" rtlCol="0" anchor="b"/><a:lstStyle><a:lvl1pPr algn="r"><a:defRPr sz="1200"/></a:lvl1pPr></a:lstStyle><a:p><a:fld id="{18BDFEC3-8487-43E8-A154-7C12CBC1FFF2}" type="slidenum"><a:rPr lang="en-US" smtClean="0"/><a:t>‹#›</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="3782709779"/></p:ext></p:extLst></p:cSld><p:clrMap bg1="lt1" tx1="dk1" bg2="lt2" tx2="dk2" accent1="accent1" accent2="accent2" accent3="accent3" accent4="accent4" accent5="accent5" accent6="accent6" hlink="hlink" folHlink="folHlink"/><p:notesStyle><a:lvl1pPr marL="0" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1200" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl1pPr><a:lvl2pPr marL="457200" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1200" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl2pPr><a:lvl3pPr marL="914400" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1200" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl3pPr><a:lvl4pPr marL="1371600" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1200" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl4pPr><a:lvl5pPr marL="1828800" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1200" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl5pPr><a:lvl6pPr marL="2286000" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1200" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl6pPr><a:lvl7pPr marL="2743200" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1200" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl7pPr><a:lvl8pPr marL="3200400" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1200" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl8pPr><a:lvl9pPr marL="3657600" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1200" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl9pPr></p:notesStyle></p:notesMaster><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme" Target="../theme/theme2.xml"/></Relationships><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:viewPr xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main" lastView="sldThumbnailView"><p:normalViewPr><p:restoredLeft sz="15643" autoAdjust="0"/><p:restoredTop sz="94694" autoAdjust="0"/></p:normalViewPr><p:slideViewPr><p:cSldViewPr snapToGrid="0" snapToObjects="1"><p:cViewPr varScale="1"><p:scale><a:sx n="161" d="100"/><a:sy n="161" d="100"/></p:scale><p:origin x="560" y="200"/></p:cViewPr><p:guideLst><p:guide orient="horz" pos="1620"/><p:guide pos="2880"/></p:guideLst></p:cSldViewPr></p:slideViewPr><p:outlineViewPr><p:cViewPr><p:scale><a:sx n="33" d="100"/><a:sy n="33" d="100"/></p:scale><p:origin x="0" y="0"/></p:cViewPr></p:outlineViewPr><p:notesTextViewPr><p:cViewPr><p:scale><a:sx n="100" d="100"/><a:sy n="100" d="100"/></p:scale><p:origin x="0" y="0"/></p:cViewPr></p:notesTextViewPr><p:gridSpacing cx="76200" cy="76200"/></p:viewPr><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:sldMaster xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main"><p:cSld><p:bg><p:bgRef idx="1001"><a:schemeClr val="bg1"/></p:bgRef></p:bg><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="2" name="Title Placeholder 1"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="title"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="457200" y="205979"/><a:ext cx="8229600" cy="857250"/></a:xfrm><a:prstGeom prst="rect"><a:avLst/></a:prstGeom></p:spPr><p:txBody><a:bodyPr vert="horz" lIns="91440" tIns="45720" rIns="91440" bIns="45720" rtlCol="0" anchor="ctr"><a:normAutofit/></a:bodyPr><a:lstStyle/><a:p><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master title style</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="3" name="Text Placeholder 2"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="body" idx="1"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="457200" y="1200151"/><a:ext cx="8229600" cy="3394472"/></a:xfrm><a:prstGeom prst="rect"><a:avLst/></a:prstGeom></p:spPr><p:txBody><a:bodyPr vert="horz" lIns="91440" tIns="45720" rIns="91440" bIns="45720" rtlCol="0"><a:normAutofit/></a:bodyPr><a:lstStyle/><a:p><a:pPr lvl="0"/><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master text styles</a:t></a:r></a:p><a:p><a:pPr lvl="1"/><a:r><a:rPr lang="en-US"/><a:t>Second level</a:t></a:r></a:p><a:p><a:pPr lvl="2"/><a:r><a:rPr lang="en-US"/><a:t>Third level</a:t></a:r></a:p><a:p><a:pPr lvl="3"/><a:r><a:rPr lang="en-US"/><a:t>Fourth level</a:t></a:r></a:p><a:p><a:pPr lvl="4"/><a:r><a:rPr lang="en-US"/><a:t>Fifth level</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="4" name="Date Placeholder 3"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="dt" sz="half" idx="2"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="457200" y="4767263"/><a:ext cx="2133600" cy="273844"/></a:xfrm><a:prstGeom prst="rect"><a:avLst/></a:prstGeom></p:spPr><p:txBody><a:bodyPr vert="horz" lIns="91440" tIns="45720" rIns="91440" bIns="45720" rtlCol="0" anchor="ctr"/><a:lstStyle><a:lvl1pPr algn="l"><a:defRPr sz="900"><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl1pPr></a:lstStyle><a:p><a:fld id="{241EB5C9-1307-BA42-ABA2-0BC069CD8E7F}" type="datetimeFigureOut"><a:rPr lang="en-US" smtClean="0"/><a:t>1/2/22</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="5" name="Footer Placeholder 4"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="ftr" sz="quarter" idx="3"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="3124200" y="4767263"/><a:ext cx="2895600" cy="273844"/></a:xfrm><a:prstGeom prst="rect"><a:avLst/></a:prstGeom></p:spPr><p:txBody><a:bodyPr vert="horz" lIns="91440" tIns="45720" rIns="91440" bIns="45720" rtlCol="0" anchor="ctr"/><a:lstStyle><a:lvl1pPr algn="ctr"><a:defRPr sz="900"><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl1pPr></a:lstStyle><a:p><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="6" name="Slide Number Placeholder 5"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldNum" sz="quarter" idx="4"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="6553200" y="4767263"/><a:ext cx="2133600" cy="273844"/></a:xfrm><a:prstGeom prst="rect"><a:avLst/></a:prstGeom></p:spPr><p:txBody><a:bodyPr vert="horz" lIns="91440" tIns="45720" rIns="91440" bIns="45720" rtlCol="0" anchor="ctr"/><a:lstStyle><a:lvl1pPr algn="r"><a:defRPr sz="900"><a:solidFill><a:schemeClr val="tx1"><a:tint val="75000"/></a:schemeClr></a:solidFill></a:defRPr></a:lvl1pPr></a:lstStyle><a:p><a:fld id="{C5EF2332-01BF-834F-8236-50238282D533}" type="slidenum"><a:rPr lang="en-US" smtClean="0"/><a:t>‹#›</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="3676200875"/></p:ext></p:extLst></p:cSld><p:clrMap bg1="lt1" tx1="dk1" bg2="lt2" tx2="dk2" accent1="accent1" accent2="accent2" accent3="accent3" accent4="accent4" accent5="accent5" accent6="accent6" hlink="hlink" folHlink="folHlink"/><p:sldLayoutIdLst><p:sldLayoutId id="2147483649" r:id="rId1"/><p:sldLayoutId id="2147483650" r:id="rId2"/><p:sldLayoutId id="2147483651" r:id="rId3"/><p:sldLayoutId id="2147483652" r:id="rId4"/><p:sldLayoutId id="2147483653" r:id="rId5"/><p:sldLayoutId id="2147483654" r:id="rId6"/><p:sldLayoutId id="2147483655" r:id="rId7"/><p:sldLayoutId id="2147483656" r:id="rId8"/><p:sldLayoutId id="2147483657" r:id="rId9"/><p:sldLayoutId id="2147483658" r:id="rId10"/><p:sldLayoutId id="2147483659" r:id="rId11"/></p:sldLayoutIdLst><p:txStyles><p:titleStyle><a:lvl1pPr algn="ctr" defTabSz="342900" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:spcBef><a:spcPct val="0"/></a:spcBef><a:buNone/><a:defRPr sz="3300" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mj-lt"/><a:ea typeface="+mj-ea"/><a:cs typeface="+mj-cs"/></a:defRPr></a:lvl1pPr></p:titleStyle><p:bodyStyle><a:lvl1pPr marL="342900" indent="-342900" algn="l" defTabSz="342900" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:spcBef><a:spcPct val="20000"/></a:spcBef><a:buFont typeface="Arial"/><a:buChar char="•"/><a:defRPr sz="2400" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl1pPr><a:lvl2pPr marL="685800" indent="-342900" algn="l" defTabSz="342900" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:spcBef><a:spcPct val="20000"/></a:spcBef><a:buFont typeface="Arial"/><a:buChar char="–"/><a:defRPr sz="2100" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl2pPr><a:lvl3pPr marL="1028700" indent="-342900" algn="l" defTabSz="342900" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:spcBef><a:spcPct val="20000"/></a:spcBef><a:buFont typeface="Arial"/><a:buChar char="•"/><a:defRPr sz="1800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl3pPr><a:lvl4pPr marL="1371600" indent="-342900" algn="l" defTabSz="342900" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:spcBef><a:spcPct val="20000"/></a:spcBef><a:buFont typeface="Arial"/><a:buChar char="–"/><a:defRPr sz="1500" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl4pPr><a:lvl5pPr marL="1714500" indent="-342900" algn="l" defTabSz="342900" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:spcBef><a:spcPct val="20000"/></a:spcBef><a:buFont typeface="Arial"/><a:buChar char="»"/><a:defRPr sz="1500" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl5pPr><a:lvl6pPr marL="2057400" indent="-342900" algn="l" defTabSz="342900" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:spcBef><a:spcPct val="20000"/></a:spcBef><a:buFont typeface="Arial"/><a:buChar char="•"/><a:defRPr sz="1500" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl6pPr><a:lvl7pPr marL="2400300" indent="-342900" algn="l" defTabSz="342900" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:spcBef><a:spcPct val="20000"/></a:spcBef><a:buFont typeface="Arial"/><a:buChar char="•"/><a:defRPr sz="1500" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl7pPr><a:lvl8pPr marL="2743200" indent="-342900" algn="l" defTabSz="342900" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:spcBef><a:spcPct val="20000"/></a:spcBef><a:buFont typeface="Arial"/><a:buChar char="•"/><a:defRPr sz="1500" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl8pPr><a:lvl9pPr marL="3086100" indent="-342900" algn="l" defTabSz="342900" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:spcBef><a:spcPct val="20000"/></a:spcBef><a:buFont typeface="Arial"/><a:buChar char="•"/><a:defRPr sz="1500" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl9pPr></p:bodyStyle><p:otherStyle><a:defPPr><a:defRPr lang="en-US"/></a:defPPr><a:lvl1pPr marL="0" algn="l" defTabSz="342900" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1350" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl1pPr><a:lvl2pPr marL="342900" algn="l" defTabSz="342900" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1350" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl2pPr><a:lvl3pPr marL="685800" algn="l" defTabSz="342900" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1350" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl3pPr><a:lvl4pPr marL="1028700" algn="l" defTabSz="342900" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1350" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl4pPr><a:lvl5pPr marL="1371600" algn="l" defTabSz="342900" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1350" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl5pPr><a:lvl6pPr marL="1714500" algn="l" defTabSz="342900" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1350" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl6pPr><a:lvl7pPr marL="2057400" algn="l" defTabSz="342900" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1350" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl7pPr><a:lvl8pPr marL="2400300" algn="l" defTabSz="342900" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1350" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl8pPr><a:lvl9pPr marL="2743200" algn="l" defTabSz="342900" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1350" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl9pPr></p:otherStyle></p:txStyles></p:sldMaster><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId8" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout" Target="../slideLayouts/slideLayout8.xml"/><Relationship Id="rId3" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout" Target="../slideLayouts/slideLayout3.xml"/><Relationship Id="rId7" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout" Target="../slideLayouts/slideLayout7.xml"/><Relationship Id="rId12" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme" Target="../theme/theme1.xml"/><Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout" Target="../slideLayouts/slideLayout2.xml"/><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout" Target="../slideLayouts/slideLayout1.xml"/><Relationship Id="rId6" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout" Target="../slideLayouts/slideLayout6.xml"/><Relationship Id="rId11" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout" Target="../slideLayouts/slideLayout11.xml"/><Relationship Id="rId5" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout" Target="../slideLayouts/slideLayout5.xml"/><Relationship Id="rId10" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout" Target="../slideLayouts/slideLayout10.xml"/><Relationship Id="rId4" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout" Target="../slideLayouts/slideLayout4.xml"/><Relationship Id="rId9" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout" Target="../slideLayouts/slideLayout9.xml"/></Relationships><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme"><a:themeElements><a:clrScheme name="Office"><a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1><a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1><a:dk2><a:srgbClr val="44546A"/></a:dk2><a:lt2><a:srgbClr val="E7E6E6"/></a:lt2><a:accent1><a:srgbClr val="5B9BD5"/></a:accent1><a:accent2><a:srgbClr val="ED7D31"/></a:accent2><a:accent3><a:srgbClr val="A5A5A5"/></a:accent3><a:accent4><a:srgbClr val="FFC000"/></a:accent4><a:accent5><a:srgbClr val="4472C4"/></a:accent5><a:accent6><a:srgbClr val="70AD47"/></a:accent6><a:hlink><a:srgbClr val="0563C1"/></a:hlink><a:folHlink><a:srgbClr val="954F72"/></a:folHlink></a:clrScheme><a:fontScheme name="Office"><a:majorFont><a:latin typeface="Calibri Light" panose="020F0302020204030204"/><a:ea typeface=""/><a:cs typeface=""/><a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/><a:font script="Hang" typeface="맑은 고딕"/><a:font script="Hans" typeface="宋体"/><a:font script="Hant" typeface="新細明體"/><a:font script="Arab" typeface="Times New Roman"/><a:font script="Hebr" typeface="Times New Roman"/><a:font script="Thai" typeface="Angsana New"/><a:font script="Ethi" typeface="Nyala"/><a:font script="Beng" typeface="Vrinda"/><a:font script="Gujr" typeface="Shruti"/><a:font script="Khmr" typeface="MoolBoran"/><a:font script="Knda" typeface="Tunga"/><a:font script="Guru" typeface="Raavi"/><a:font script="Cans" typeface="Euphemia"/><a:font script="Cher" typeface="Plantagenet Cherokee"/><a:font script="Yiii" typeface="Microsoft Yi Baiti"/><a:font script="Tibt" typeface="Microsoft Himalaya"/><a:font script="Thaa" typeface="MV Boli"/><a:font script="Deva" typeface="Mangal"/><a:font script="Telu" typeface="Gautami"/><a:font script="Taml" typeface="Latha"/><a:font script="Syrc" typeface="Estrangelo Edessa"/><a:font script="Orya" typeface="Kalinga"/><a:font script="Mlym" typeface="Kartika"/><a:font script="Laoo" typeface="DokChampa"/><a:font script="Sinh" typeface="Iskoola Pota"/><a:font script="Mong" typeface="Mongolian Baiti"/><a:font script="Viet" typeface="Times New Roman"/><a:font script="Uigh" typeface="Microsoft Uighur"/><a:font script="Geor" typeface="Sylfaen"/></a:majorFont><a:minorFont><a:latin typeface="Calibri" panose="020F0502020204030204"/><a:ea typeface=""/><a:cs typeface=""/><a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/><a:font script="Hang" typeface="맑은 고딕"/><a:font script="Hans" typeface="宋体"/><a:font script="Hant" typeface="新細明體"/><a:font script="Arab" typeface="Arial"/><a:font script="Hebr" typeface="Arial"/><a:font script="Thai" typeface="Cordia New"/><a:font script="Ethi" typeface="Nyala"/><a:font script="Beng" typeface="Vrinda"/><a:font script="Gujr" typeface="Shruti"/><a:font script="Khmr" typeface="DaunPenh"/><a:font script="Knda" typeface="Tunga"/><a:font script="Guru" typeface="Raavi"/><a:font script="Cans" typeface="Euphemia"/><a:font script="Cher" typeface="Plantagenet Cherokee"/><a:font script="Yiii" typeface="Microsoft Yi Baiti"/><a:font script="Tibt" typeface="Microsoft Himalaya"/><a:font script="Thaa" typeface="MV Boli"/><a:font script="Deva" typeface="Mangal"/><a:font script="Telu" typeface="Gautami"/><a:font script="Taml" typeface="Latha"/><a:font script="Syrc" typeface="Estrangelo Edessa"/><a:font script="Orya" typeface="Kalinga"/><a:font script="Mlym" typeface="Kartika"/><a:font script="Laoo" typeface="DokChampa"/><a:font script="Sinh" typeface="Iskoola Pota"/><a:font script="Mong" typeface="Mongolian Baiti"/><a:font script="Viet" typeface="Arial"/><a:font script="Uigh" typeface="Microsoft Uighur"/><a:font script="Geor" typeface="Sylfaen"/></a:minorFont></a:fontScheme><a:fmtScheme name="Office"><a:fillStyleLst><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:gradFill rotWithShape="1"><a:gsLst><a:gs pos="0"><a:schemeClr val="phClr"><a:lumMod val="110000"/><a:satMod val="105000"/><a:tint val="67000"/></a:schemeClr></a:gs><a:gs pos="50000"><a:schemeClr val="phClr"><a:lumMod val="105000"/><a:satMod val="103000"/><a:tint val="73000"/></a:schemeClr></a:gs><a:gs pos="100000"><a:schemeClr val="phClr"><a:lumMod val="105000"/><a:satMod val="109000"/><a:tint val="81000"/></a:schemeClr></a:gs></a:gsLst><a:lin ang="5400000" scaled="0"/></a:gradFill><a:gradFill rotWithShape="1"><a:gsLst><a:gs pos="0"><a:schemeClr val="phClr"><a:satMod val="103000"/><a:lumMod val="102000"/><a:tint val="94000"/></a:schemeClr></a:gs><a:gs pos="50000"><a:schemeClr val="phClr"><a:satMod val="110000"/><a:lumMod val="100000"/><a:shade val="100000"/></a:schemeClr></a:gs><a:gs pos="100000"><a:schemeClr val="phClr"><a:lumMod val="99000"/><a:satMod val="120000"/><a:shade val="78000"/></a:schemeClr></a:gs></a:gsLst><a:lin ang="5400000" scaled="0"/></a:gradFill></a:fillStyleLst><a:lnStyleLst><a:ln w="6350" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/><a:miter lim="800000"/></a:ln><a:ln w="12700" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/><a:miter lim="800000"/></a:ln><a:ln w="19050" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/><a:miter lim="800000"/></a:ln></a:lnStyleLst><a:effectStyleLst><a:effectStyle><a:effectLst/></a:effectStyle><a:effectStyle><a:effectLst/></a:effectStyle><a:effectStyle><a:effectLst><a:outerShdw blurRad="57150" dist="19050" dir="5400000" algn="ctr" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="63000"/></a:srgbClr></a:outerShdw></a:effectLst></a:effectStyle></a:effectStyleLst><a:bgFillStyleLst><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:solidFill><a:schemeClr val="phClr"><a:tint val="95000"/><a:satMod val="170000"/></a:schemeClr></a:solidFill><a:gradFill rotWithShape="1"><a:gsLst><a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="93000"/><a:satMod val="150000"/><a:shade val="98000"/><a:lumMod val="102000"/></a:schemeClr></a:gs><a:gs pos="50000"><a:schemeClr val="phClr"><a:tint val="98000"/><a:satMod val="130000"/><a:shade val="90000"/><a:lumMod val="103000"/></a:schemeClr></a:gs><a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="63000"/><a:satMod val="120000"/></a:schemeClr></a:gs></a:gsLst><a:lin ang="5400000" scaled="0"/></a:gradFill></a:bgFillStyleLst></a:fmtScheme></a:themeElements><a:objectDefaults/><a:extraClrSchemeLst/><a:extLst><a:ext uri="{05A4C25C-085E-4340-85A3-A5531E510DB2}"><thm15:themeFamily xmlns:thm15="http://schemas.microsoft.com/office/thememl/2012/main" name="Office Theme" id="{62F939B6-93AF-4DB8-9C6B-D6C7DFDC589F}" vid="{4A3C46E8-61CC-4603-A589-7422A47A8E4A}"/></a:ext></a:extLst></a:theme><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme"><a:themeElements><a:clrScheme name="Office"><a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1><a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1><a:dk2><a:srgbClr val="1F497D"/></a:dk2><a:lt2><a:srgbClr val="EEECE1"/></a:lt2><a:accent1><a:srgbClr val="4F81BD"/></a:accent1><a:accent2><a:srgbClr val="C0504D"/></a:accent2><a:accent3><a:srgbClr val="9BBB59"/></a:accent3><a:accent4><a:srgbClr val="8064A2"/></a:accent4><a:accent5><a:srgbClr val="4BACC6"/></a:accent5><a:accent6><a:srgbClr val="F79646"/></a:accent6><a:hlink><a:srgbClr val="0000FF"/></a:hlink><a:folHlink><a:srgbClr val="800080"/></a:folHlink></a:clrScheme><a:fontScheme name="Office"><a:majorFont><a:latin typeface="Calibri"/><a:ea typeface=""/><a:cs typeface=""/><a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/><a:font script="Hang" typeface="맑은 고딕"/><a:font script="Hans" typeface="宋体"/><a:font script="Hant" typeface="新細明體"/><a:font script="Arab" typeface="Times New Roman"/><a:font script="Hebr" typeface="Times New Roman"/><a:font script="Thai" typeface="Angsana New"/><a:font script="Ethi" typeface="Nyala"/><a:font script="Beng" typeface="Vrinda"/><a:font script="Gujr" typeface="Shruti"/><a:font script="Khmr" typeface="MoolBoran"/><a:font script="Knda" typeface="Tunga"/><a:font script="Guru" typeface="Raavi"/><a:font script="Cans" typeface="Euphemia"/><a:font script="Cher" typeface="Plantagenet Cherokee"/><a:font script="Yiii" typeface="Microsoft Yi Baiti"/><a:font script="Tibt" typeface="Microsoft Himalaya"/><a:font script="Thaa" typeface="MV Boli"/><a:font script="Deva" typeface="Mangal"/><a:font script="Telu" typeface="Gautami"/><a:font script="Taml" typeface="Latha"/><a:font script="Syrc" typeface="Estrangelo Edessa"/><a:font script="Orya" typeface="Kalinga"/><a:font script="Mlym" typeface="Kartika"/><a:font script="Laoo" typeface="DokChampa"/><a:font script="Sinh" typeface="Iskoola Pota"/><a:font script="Mong" typeface="Mongolian Baiti"/><a:font script="Viet" typeface="Times New Roman"/><a:font script="Uigh" typeface="Microsoft Uighur"/><a:font script="Geor" typeface="Sylfaen"/></a:majorFont><a:minorFont><a:latin typeface="Calibri"/><a:ea typeface=""/><a:cs typeface=""/><a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/><a:font script="Hang" typeface="맑은 고딕"/><a:font script="Hans" typeface="宋体"/><a:font script="Hant" typeface="新細明體"/><a:font script="Arab" typeface="Arial"/><a:font script="Hebr" typeface="Arial"/><a:font script="Thai" typeface="Cordia New"/><a:font script="Ethi" typeface="Nyala"/><a:font script="Beng" typeface="Vrinda"/><a:font script="Gujr" typeface="Shruti"/><a:font script="Khmr" typeface="DaunPenh"/><a:font script="Knda" typeface="Tunga"/><a:font script="Guru" typeface="Raavi"/><a:font script="Cans" typeface="Euphemia"/><a:font script="Cher" typeface="Plantagenet Cherokee"/><a:font script="Yiii" typeface="Microsoft Yi Baiti"/><a:font script="Tibt" typeface="Microsoft Himalaya"/><a:font script="Thaa" typeface="MV Boli"/><a:font script="Deva" typeface="Mangal"/><a:font script="Telu" typeface="Gautami"/><a:font script="Taml" typeface="Latha"/><a:font script="Syrc" typeface="Estrangelo Edessa"/><a:font script="Orya" typeface="Kalinga"/><a:font script="Mlym" typeface="Kartika"/><a:font script="Laoo" typeface="DokChampa"/><a:font script="Sinh" typeface="Iskoola Pota"/><a:font script="Mong" typeface="Mongolian Baiti"/><a:font script="Viet" typeface="Arial"/><a:font script="Uigh" typeface="Microsoft Uighur"/><a:font script="Geor" typeface="Sylfaen"/></a:minorFont></a:fontScheme><a:fmtScheme name="Office"><a:fillStyleLst><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:gradFill rotWithShape="1"><a:gsLst><a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs><a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs><a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs></a:gsLst><a:lin ang="16200000" scaled="1"/></a:gradFill><a:gradFill rotWithShape="1"><a:gsLst><a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs><a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs></a:gsLst><a:lin ang="16200000" scaled="0"/></a:gradFill></a:fillStyleLst><a:lnStyleLst><a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln><a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln><a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln></a:lnStyleLst><a:effectStyleLst><a:effectStyle><a:effectLst><a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw></a:effectLst></a:effectStyle><a:effectStyle><a:effectLst><a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw></a:effectLst></a:effectStyle><a:effectStyle><a:effectLst><a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw></a:effectLst><a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d><a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d></a:effectStyle></a:effectStyleLst><a:bgFillStyleLst><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:gradFill rotWithShape="1"><a:gsLst><a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs><a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs><a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs></a:gsLst><a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path></a:gradFill><a:gradFill rotWithShape="1"><a:gsLst><a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs><a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs></a:gsLst><a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path></a:gradFill></a:bgFillStyleLst></a:fmtScheme></a:themeElements><a:objectDefaults><a:spDef><a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style></a:spDef><a:lnDef><a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style></a:lnDef></a:objectDefaults><a:extraClrSchemeLst/></a:theme><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:notes xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main"><p:cSld><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="2" name="Slide Image Placeholder 1"/><p:cNvSpPr><a:spLocks noGrp="1" noRot="1" noChangeAspect="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldImg"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="685800" y="1143000"/><a:ext cx="5486400" cy="3086100"/></a:xfrm></p:spPr></p:sp><p:sp><p:nvSpPr><p:cNvPr id="3" name="Notes Placeholder 2"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="body" idx="1"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:r><a:rPr lang="en-US" dirty="0"/><a:t>A</a:t></a:r><a:r><a:rPr lang="en-US" baseline="0" dirty="0"/><a:t> speaker note on </a:t></a:r><a:r><a:rPr lang="en-US" baseline="0"/><a:t>this slide too.</a:t></a:r><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="4" name="Slide Number Placeholder 3"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldNum" sz="quarter" idx="10"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{18BDFEC3-8487-43E8-A154-7C12CBC1FFF2}" type="slidenum"><a:rPr lang="en-US" smtClean="0"/><a:t>2</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="3016900036"/></p:ext></p:extLst></p:cSld><p:clrMapOvr><a:masterClrMapping/></p:clrMapOvr></p:notes><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:notes xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main"><p:cSld><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="2" name="Slide Image Placeholder 1"/><p:cNvSpPr><a:spLocks noGrp="1" noRot="1" noChangeAspect="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldImg"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="685800" y="1143000"/><a:ext cx="5486400" cy="3086100"/></a:xfrm></p:spPr></p:sp><p:sp><p:nvSpPr><p:cNvPr id="3" name="Notes Placeholder 2"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="body" idx="1"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:r><a:rPr lang="en-US" dirty="0"/><a:t>Here</a:t></a:r><a:r><a:rPr lang="en-US" baseline="0" dirty="0"/><a:t> is a note</a:t></a:r></a:p><a:p><a:endParaRPr lang="en-US" baseline="0" dirty="0"/></a:p><a:p><a:r><a:rPr lang="en-US" baseline="0" dirty="0"/><a:t>With another paragraph.</a:t></a:r><a:endParaRPr lang="en-US" dirty="0"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="4" name="Slide Number Placeholder 3"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldNum" sz="quarter" idx="10"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="{18BDFEC3-8487-43E8-A154-7C12CBC1FFF2}" type="slidenum"><a:rPr lang="en-US" smtClean="0"/><a:t>1</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="3171319170"/></p:ext></p:extLst></p:cSld><p:clrMapOvr><a:masterClrMapping/></p:clrMapOvr></p:notes><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slide" Target="../slides/slide2.xml"/><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/notesMaster" Target="../notesMasters/notesMaster1.xml"/></Relationships><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slide" Target="../slides/slide1.xml"/><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/notesMaster" Target="../notesMasters/notesMaster1.xml"/></Relationships>Abstract: Sammendrag
Appendix: Tillegg
Bibliography: Bibliografi
Cc: Kopi sendt
Chapter: Kapittel
Contents: Innhold
Encl: Vedlegg
Figure: Figur
Glossary: Ordliste
Index: Register
ListOfFigures: Figurer
ListOfTables: Tabeller
Page: Side
Part: Del
Preface: Forord
Proof: Bevis
References: Referanser
See: Se
SeeAlso: Se også
Table: Tabell
To: Til
Abstract: Kokkuvõte
Appendix: Lisa
Bibliography: Kirjandus
Cc: Koopia(d)
Chapter: Peatükk
Contents: Sisukord
Encl: Lisa(d)
Figure: Joonis
Glossary: Sõnastik
Index: Indeks
Listing: Kood
ListOfFigures: Joonised
ListOfTables: Tabelid
Page: Lk.
Part: Osa
Preface: Sissejuhatus
Proof: Korrektuur
References: Viited
See: vt.
SeeAlso: vt. ka
Table: Tabel
To: Saaja
Abstract: Recapitulaziun
Appendix: Appendix
Bibliography: Index bibliografic
Cc: Copia a
Chapter: Chapitel
Contents: Tavla dal cuntegn
Encl: Agiunta(s)
Figure: Figura
Glossary: Glossari
Index: Register da materias
ListOfFigures: Tavla da las figuras
ListOfTables: Tavla da las tabellas
Page: pagina
Part: Part
Preface: Prefaziun
Proof: Demonstraziun
References: Bibliografia
See: vesair
SeeAlso: vesair era
Table: Tabella
To: A
Abstract: Útdráttur
Appendix: Viðauki
Bibliography: Heimildir
Cc: Samrit
Chapter: Kafli
Contents: Efnisyfirlit
Encl: Hjálagt
Figure: Mynd
Glossary: Orðalisti
Index: Atriðisorðaskrá
ListOfFigures: Myndaskrá
ListOfTables: Töfluskrá
Page: Blaðsíða
Part: Hluti
Preface: Formáli
Proof: Sönnun
References: Heimildir
See: Sjá
SeeAlso: Sjá einnig
Table: Tafla
To: Til:
Abstract: Özet
Appendix: Ek
Bibliography: Kaynakça
Cc: Diğer Alıcılar
Chapter: Bölüm
Contents: İçindekiler
Encl: İlişik
Figure: Şekil
Glossary: Lügatçe
Index: Dizin
ListOfFigures: Şekil Listesi
ListOfTables: Tablo Listesi
Page: Sayfa
Part: Kısım
Preface: Önsöz
Proof: Kanıt
References: Kaynaklar
See: bkz.
SeeAlso: ayrıca bkz.
Table: Tablo
To: Alıcı
Abstract: 요약
Appendix: 부록
Bibliography: 참고문헌
Cc: 사본
Chapter: 장
Contents: 차례
Encl: 동봉
Figure: 그림
Index: 찾아보기
ListOfFigures: 그림 차례
ListOfTables: 표 차례
Page: 페이지
Preface: 서문
Proof: 증명
References: 참고문헌
Table: 표
To: 수신:
Abstract: Аннотация
Appendix: Приложение
Bibliography: Литература
Cc: исх.
Chapter: Глава
Contents: Оглавление
Encl: вкл.
Figure: Рис.
Index: Предметный указатель
ListOfFigures: Список иллюстраций
ListOfTables: Список таблиц
Page: с.
Part: Часть
Preface: Предисловие
Proof: Доказательство
References: Список литературы
See: см.
SeeAlso: см. также
Table: Таблица
To: вх.
Abstract: Kivonat
Appendix: Függelék
Bibliography: Irodalomjegyzék
Cc: Körlevél–címzettek
Chapter: fejezet
Contents: Tartalomjegyzék
Encl: Melléklet
Figure: ábra
Glossary: Szójegyzék
Index: Tárgymutató
ListOfFigures: Ábrák jegyzéke
ListOfTables: Táblázatok jegyzéke
Page: oldal
Part: rész
Preface: Előszó
Proof: Bizonyítás
References: Hivatkozások
See: lásd
SeeAlso: lásd még
Table: táblázat
To: Címzett
Abstract: সারসংক্ষেপ
Appendix: পরিশিষ্ট
Bibliography: তথ্যবিবরণ
Cc: অনুলিপি
Chapter: অধ্যায়
Contents: সূচীপত্র
Encl: সংযুক্তি
Figure: ছবি/নকশা
Glossary: পরিভাষার শব্দসম্ভার
Index: সূচক/নির্দেশক
ListOfFigures: ছবি/নকশা সমূহের তালিকা
ListOfTables: তালিকাসারণী
Page: পৃষ্ঠা
Part: খন্ড
Preface: পূর্বকথা
Proof: প্রমাণ
References: তথ্যসুত্রসমূহ
See: দেখুন
SeeAlso: আরও দেখুন
Table: সারনী
To: প্রতি
Abstract: תקציר
Appendix: נספח
Bibliography: ביבליוגרפיה
Cc: העתקים
Chapter: פרק
Contents: תוכן העניינים
Encl: רצ"ב
Figure: איור
Glossary: מילון מונחים}
Index: מפתח
ListOfFigures: רשימת האיורים
ListOfTables: רשימת הטבלאות
Page: עמוד
Part: חלק
Preface: מבוא
Proof: הוכחה}
Ps: נ.ב.
References: מקורות
See: ראה
SeeAlso: ראה גם}
Table: טבלה
To: אל
Abstract: ບົດຫຍໍ້ຄວາມ
Appendix: ພາກຄັດຕິດ
Bibliography: ເອກະສານອ້າງອີງ
Cc: ສໍາເນົາເຖິງ
Chapter: ບົດທີ
Contents: ສາລະບານ
Encl: ເອກະສານປະກອບ
Figure: ຮູບທີ
Glossary: ປະມວນສັບ
Index: ດັດຊະນີ
ListOfFigures: ສາລະບານຮູບ
ListOfTables: ສາລະບານຕາຕະລາງ
Page: ໜ້າ
Part: ພາກ
Preface: ຄໍານໍາ
Proof: ຂໍ້ພິສູດ
References: ໜັງສືອ້າງອີງ
See: ອ່ານ
SeeAlso: ອ່ານເພີ່ມ
Table: ຕາຕະລາງທີ
To: ຮຽນ
Abstract: Abstrakt
Appendix: Dodatok
Bibliography: Literatúra
Cc: cc.
Chapter: Kapitola
Contents: Obsah
Encl: Prílohy
Figure: Obrázok
Glossary: Slovník
Index: Index
ListOfFigures: Zoznam obrázkov
ListOfTables: Zoznam tabuliek
Page: Strana
Part: Časť
Preface: Úvod
Proof: Dôkaz
References: Referencie
See: viď
SeeAlso: viď tiež
Table: Tabuľka
To: Pre
Abstract: Samandrag
Appendix: Tillegg
Bibliography: Bibliografi
Cc: Kopi sendt
Chapter: Kapittel
Contents: Innhald
Encl: Vedlegg
Figure: Figur
Glossary: Ordliste
Index: Register
ListOfFigures: Figurar
ListOfTables: Tabellar
Page: Side
Part: Del
Preface: Forord
Proof: Bevis
References: Referansar
See: Sjå
SeeAlso: Sjå òg
Table: Tabell
To: Til
Abstract: Resumo
Appendix: Apendico
Bibliography: Bibliografio
Cc: Kopie al
Chapter: Ĉapitro
Contents: Enhavo
Encl: Aldono(j)
Figure: Figuro
Glossary: Glosaro
Index: Indekso
ListOfFigures: Listo de figuroj
ListOfTables: Listo de tabeloj
Page: Paĝo
Preface: Antaŭparolo
Proof: Pruvo
References: Citaĵoj
See: vidu
SeeAlso: Parto
Table: Tabelo
To: Al
Abstract: چکیﺪﻫ
Appendix: پیﻮﺴﺗ
Bibliography: کﺕﺎﺑc>ﻧﺎﻤﻫ
Cc: ﺭﻮﻧﻮﺸﺗ
Chapter: ﻒﺼﻟ
Contents: ﻒﻫﺮﺴﺗ ﻢﻃﺎﻠﺑ
Encl: پیﻮﺴﺗ
Figure: ﺶﻜﻟ
Glossary: ﺩﺎﻨﺷc>ﻧﺎﻤﻫ
Index: ﻦﻣﺍیﻩ
ListOfFigures: ﻝیﺲﺗ ﺖﺻﺍﻭیﺭ
ListOfTables: ﻝیﺲﺗ ﺝﺩﺍﻮﻟ
Page: ﺺﻔﺣﺓ
Part: ﺐﺨﺷ
Preface: پیﺵگﻒﺗﺍﺭ
Proof: ﺏﺮﻫﺎﻧ
References: ﻡﺭﺎﺠﻋ
See: ﺐﺑیﻥیﺩ
SeeAlso: ﻥیﺯ ﺐﺑیﻥیﺩ
Table: ﺝﺩﻮﻟ
To: ﺐﻫ
Abstract: ﻢﻠﺨّﺻ
Appendix: ﺾﻣیﻡہ
Bibliography: کﺕﺎﺑیﺎﺗ
Cc: ﻦﻘﻟ
Chapter: ﺏﺎﺑ
Contents: ﻑہﺮﺴﺗ ﻊﻧﻭﺎﻧﺎﺗ
Encl: ﻢﻨﺴﻟک
Figure: ﺶﻜﻟ
Glossary: ﻞﻐﺗ
Index: ﺎﺷﺍﺭیہ
ListOfFigures: ﻑہﺮﺴﺗ ﺎﺷکﺎﻟ
ListOfTables: ﻑہﺮﺴﺗ ﺝﺩﺍﻮﻟ
Page: ﺺﻔﺣہ
Part: ﺢﺻّہ
Preface: ﺩیﺏﺍچہ
Proof: ﺚﺑﻮﺗ
References: ﺡﻭﺎﻟہ ﺝﺎﺗ
Section: ﻒﺼﻟ
See: ﻡﻼﺤﻇہ ہﻭ
SeeAlso: ﺍیﺽﺍً
Table: ﺝﺩﻮﻟ
To: ﺐﻣﻼﺤﻇہ
Abstract: Resum
Appendix: Apèndix
Bibliography: Bibliografia
Cc: Còpies a
Chapter: Capítol
Contents: Índex
Encl: Adjunt
Figure: Figura
Glossary: Glossari
Index: Índex alfabètic
ListOfFigures: Índex de figures
ListOfTables: Índex de taules
Page: Pàgina
Part: Part
Preface: Pròleg
Proof: Demostració
References: Referències
See: Vegeu
SeeAlso: Vegeu també
Table: Taula
To: A
Abstract: Laburpena
Appendix: Eranskina
Bibliography: Bibliografia
Cc: Kopia
Chapter: Kapitulua
Contents: Gaien Aurkibidea
Encl: Erantsia
Figure: Irudia
Glossary: Glosarioa
Index: Kontzeptuen Aurkibidea
ListOfFigures: Irudien Zerrenda
ListOfTables: Taulen Zerrenda
Page: Orria
Part: Atala
Preface: Hitzaurrea
Proof: Frogapena
References: Erreferentziak
See: Ikusi
SeeAlso: Ikusi, halaber
Table: Taula
To: Nori
Abstract: Sammanfattning
Appendix: Bilaga
Bibliography: Litteraturförteckning
Cc: Kopia för kännedom
Chapter: Kapitel
Contents: Innehåll
Encl: Bil.
Figure: Figur
Glossary: Ordlista
Index: Sakregister
ListOfFigures: Figurer
ListOfTables: Tabeller
Page: Sida
Part: Del
Preface: Förord
Proof: Bevis
References: Referenser
See: se
SeeAlso: se även
Table: Tabell
To: Till
Abstract: Povzetek
Appendix: Dodatek
Bibliography: Literatura
Cc: Kopije
Chapter: Poglavje
Contents: Kazalo
Encl: Priloge
Figure: Slika
Glossary: Slovar
Index: Stvarno kazalo
ListOfFigures: Slike
ListOfTables: Tabele
Page: Stran
Part: Del
Preface: Predgovor
Proof: Dokaz
References: Literatura
See: glej
SeeAlso: glej tudi
Table: Tabela
To: Prejme
Abstract: Resumé
Appendix: Bilag
Bibliography: Litteratur
Cc: Kopi til}
Chapter: Kapitel
Contents: Indhold
Encl: Vedlagt
Figure: Figur
Glossary: Gloseliste
Index: Indeks
ListOfFigures: Figurer
ListOfTables: Tabeller
Page: Side
Part: Del
Preface: Forord
Proof: Bevis
References: Litteratur
See: Se
SeeAlso: Se også
Table: Tabel
To: Til}
Abstract: Santrauka
Appendix: Priedas
Bibliography: Literatūra
Cc: Kopijos
Chapter: Skyrius
Contents: Turinys
Encl: Įdėta
Figure: pav.
Glossary: Terminų žodynas
Index: Rodyklė
ListOfFigures: Iliustracijų sąrašas
ListOfTables: Lentelių sąrašas
Page: puslapis
Part: Dalis
Preface: Pratarmė
Proof: Įrodymas
References: Literatūra
See: žiūrėk
SeeAlso: taip pat
Table: lentelė
To: Kam
Abstract: Анотація
Appendix: Додаток
Bibliography: Бібліоґрафія
Cc: копія
Chapter: Розділ
Contents: Зміст
Encl: вкладка
Figure: Рис.
Glossary: Словник термінів
Index: Покажчик
ListOfFigures: Перелік ілюстрацій
ListOfTables: Перелік таблиць
Page: с.
Part: Частина
Preface: Вступ
Proof: Доведення
References: Література
See: див.
SeeAlso: див. також
Table: Табл.
To: До
Abstract: Абстракт
Appendix: Приложение
Bibliography: Библиография
Cc: копия
Chapter: Глава
Contents: Съдържание
Encl: Приложения
Figure: Фигура
Glossary: Glossary
Index: Азбучен указател
ListOfFigures: Списък на фигурите
ListOfTables: Списък на таблиците
Page: Стр.
Preface: Предговор
Proof: Proof
References: Литература
See: вж.
SeeAlso: вж. също и
Table: Таблица
Abstract: Abstract
Appendix: Appendix
Bibliography: Bibliography
Cc: cc
Chapter: Chapter
Contents: Contents
Encl: encl
Figure: Figure
Glossary: Glossary
Index: Index
Listing: Listing
ListOfFigures: ListOfFigures
ListOfTables: ListOfTables
Page: page
Part: Part
Preface: Preface
Proof: Proof
References: References
See: see
SeeAlso: see also
Table: Table
To: To
Abstract: 摘要
Appendix: 附錄
Bibliography: 文獻目錄
Cc: 副本
Chapter: 章
Contents: 目錄
Encl: 附件
Figure: 圖
Glossary: 術語
Index: 索引
Listing: 列表
ListOfFigures: 附圖目錄
ListOfTables: 表格索引
Page: 頁
Part: 段
Preface: 序
Proof: 校對
References: 參考文獻
See: 見
SeeAlso: 參見
Table: 表
To: 到
Abstract: 摘要
Appendix: 附录
Bibliography: 文献目录
Cc: 副本
Chapter: 章
Contents: 目录
Encl: 附件
Figure: 图
Glossary: 术语
Index: 索引
Listing: 列表
ListOfFigures: 附图目录
ListOfTables: 表格索引
Page: 页
Part: 段
Preface: 序
Proof: 校对
References: 参考文献
See: 见
SeeAlso: 参见
Table: 表
To: 到
Abstract: Rezumat
Appendix: Anexa
Bibliography: Bibliografie
Cc: Copie
Chapter: Capitolul
Contents: Cuprins
Encl: Anexă
Figure: Figura
Glossary: Glosar
Index: Glosar
ListOfFigures: Listă de figuri
ListOfTables: Listă de tabele
Page: Pagina
Part: Partea
Preface: Prefață
Proof: Demonstrație
References: Bibliografie
See: Vezi
SeeAlso: Vezi de asemenea
Table: Tabela
To: Pentru
Abstract: Сажетак
Appendix: Додатак
Bibliography: Литература
Cc: Копије
Chapter: Глава
Contents: Садржај
Encl: Прилози
Figure: Слика
Glossary: Речник непознатих речи
Index: Регистар
ListOfFigures: Списак слика
ListOfTables: Списак табела
Page: Страна
Part: Део
Preface: Предговор
Proof: Доказ
References: Библиографија
See: Види
SeeAlso: Види такође
Table: Табела
To: Прима
Abstract: Resumo
Appendix: Apêndice
Bibliography: Bibliografia
Cc: Com cópia a
Chapter: Capítulo
Contents: Conteúdo
Encl: Anexo
Figure: Figura
Glossary: Glossário
Index: Índice
ListOfFigures: Lista de Figuras
ListOfTables: Lista de Tabelas
Page: Página
Part: Parte
Preface: Prefácio
Proof: Demonstração
References: Referências
See: ver
SeeAlso: ver também
Table: Tabela
To: Para
Abstract: Zusammenfassung
Appendix: Anhang
Bibliography: Literaturverzeichnis
Cc: Verteiler
Chapter: Kapitel
Contents: Inhaltsverzeichnis
Encl: Anlage(n)
Figure: Abbildung
Glossary: Glossar
Index: Index
Listing: Auflistung
ListOfFigures: Abbildungsverzeichnis
ListOfTables: Tabellenverzeichnis
Page: Seite
Part: Teil
Preface: Vorwort
Proof: Beweis
References: Literatur
See: siehe
SeeAlso: siehe auch
Table: Tabelle
To: An
Abstract: សង្ខេប
Appendix: សេចក្ដីបន្ថែម
Bibliography: គន្ថនិទ្ទេស
Cc: ចម្លងជួន
Chapter: ជំពូក
Contents: មាតិការ
Encl: ឯកសារភ្ជាប់
Figure: រូប
Glossary: សទានុក្រម
Index: សន្ទស្សន៍
ListOfFigures: បញ្ជីរូបភាព
ListOfTables: បញ្ជីតារាង
Page: ទំព័រ
Part: ផ្នែក
Preface: អារម្ភកថា
Proof: សម្រាយ
References: ឯកសារយោង
See: មើល
SeeAlso: មើលបន្ថែម
Table: តារាង
To: ផ្ញើរទៅ
Abstract: Περίληψη
Appendix: Παράρτημα
Bibliography: Βιβλιογραφία
Cc: Κοινοποίηση
Chapter: Κεφάλαιο
Contents: Περιεχόμενα
Encl: Συνημμένα
Figure: Σχήμα
Glossary: Γλωσσάρι
Index: Ευρετήριο
ListOfFigures: Κατάλογος σχημάτων
ListOfTables: Κατάλογος πινάκων
Page: Σελίδα
Part: Μέρος
Preface: Πρόλογος
Proof: Απόδειξη
References: Αναφορές
See: βλέπε
SeeAlso: βλέπε επίσης
Table: Πίνακας
To: Προς
Abstract: አኅጽተሮ ጽሁፍ
Appendix: መድበል
Bibliography: ቢዋ መጽሃፍት
Cc: ግልባጭ
Chapter: ክፍል
Contents: ይዘት
Encl: አባሪዎች
Figure: ሥዕል
Index: ምህጻር ቃል
ListOfFigures: የሥዕችሎ ማውጫ
ListOfTables: የሰንጠዥረ ማውጫ
Page: ገጽ
Part: ንዑስ ክፍል
Preface: መቅድም
Proof: ማረጋገጫ
References: የነሥ ጹሁፍ ምንጭ
See: ይመልከቱ
SeeAlso: ይህምን ይመልከቱ
Table: ሰንጠረዥ
To: ለ
Abstract: Streszczenie
Appendix: Dodatek
Bibliography: Bibliografia
Cc: 'Kopie:'
Chapter: Rozdział
Contents: Spis treści
Encl: Załącznik
Figure: Rysunek
Glossary: Glossary
Index: Indeks
ListOfFigures: Spis rysunków
ListOfTables: Spis tabel
Page: Strona
Part: Część
Preface: Przedmowa
Proof: Dowód
References: Literatura
See: Zobacz
SeeAlso: Zobacz też
Table: Tabela
To: Do
Abstract: Abstrakt
Appendix: Dodatek
Bibliography: Literatura
Cc: 'Na vědomí:'
Chapter: Kapitola
Contents: Obsah
Encl: Příloha
Figure: Obrázek
Glossary: Slovník
Index: Index
ListOfFigures: Seznam obrázků
ListOfTables: Seznam tabulek
Page: Strana
Part: Část
Preface: Předmluva
Proof: Důkaz
References: Reference
See: viz
SeeAlso: viz
Table: Tabulka
To: Komu
Abstract: บทคัดย่อ
Appendix: ภาคผนวก
Bibliography: บรรณานุกรม
Cc: สำเนาถึง
Chapter: บทที่
Contents: สารบัญ
Encl: สิ่งที่แนบมาด้วย
Figure: รูปที่
Index: ดรรชนี
ListOfFigures: สารบัญรูป
ListOfTables: สารบัญตาราง
Page: หน้า
Part: ภาค
Preface: คำนำ
Proof: พิสูจน์
References: หนังสืออ้างอิง
See: ดู
SeeAlso: ดูเพิ่มเติม
Table: ตารางที่
To: เรียน
Abstract: Përmbledhja
Appendix: Shtesa
Bibliography: Bibliografia
Chapter: Kapitulli
Contents: Përmbajta
Figure: Figura
Glossary: Përhasja e Fjalëve
Index: Indeksi
ListOfFigures: Figurat
ListOfTables: Tabelat
Page: Faqe
Part: Pjesa
Preface: Parathenia
Proof: Vërtetim
References: Referencat
See: shiko
SeeAlso: shiko dhe
Table: Tabela
Abstract: Sažetak
Appendix: Dodatak
Bibliography: Bibliografija
Cc: Kopija
Chapter: Poglavlje
Contents: Sadržaj
Encl: Prilozi
Figure: Slika
Glossary: Pojmovnik
Index: Kazalo
ListOfFigures: Popis slika
ListOfTables: Popis tablica
Page: Stranica
Part: Dio
Preface: Predgovor
Proof: Dokaz
References: Literatura
See: Vidjeti
SeeAlso: Također vidjeti
Table: Tablica
To: Prima
Abstract: Résumé
Appendix: Annexe
Bibliography: Bibliographie
Cc: Cc
Chapter: Chapitre
Contents: Table des matières
Figure: Figure
Glossary: Glossaire
Index: Index
ListOfFigures: Table des figures
ListOfTables: Liste des tableaux
Page: Page
Part: Partie
Preface: Préface
Proof: Démonstration
References: Références
See: Voir
SeeAlso: Voir aussi
Table: Tableau
To: À
Abstract: Samenvatting
Appendix: Bijlage
Bibliography: Bibliografie
Cc: cc
Chapter: Hoofdstuk
Contents: Inhoudsopgave
Encl: Bijlage(n)
Figure: Figuur
Glossary: Verklarende woordenlijst
Index: Index
ListOfFigures: Lijst van figuren
ListOfTables: Lijst van tabellen
Page: Pagina
Part: Deel
Preface: Voorwoord
Proof: Bewijs
References: Referenties
See: zie
SeeAlso: zie ook
Table: Tabel
To: Aan
Abstract: Anotācija
Appendix: Pielikums
Bibliography: Literatūra
Cc: cc
Chapter: Nodaļa
Contents: Saturs
Encl: encl
Figure: Att.
Index: Index
ListOfFigures: Attēlu saraksts
ListOfTables: Tabulu saraksts
Page: lpp.
Part: Daļa
Preface: Priekšvārds
Proof: Pierādījums
References: Literatūras saraksts
See: sk.
SeeAlso: sk. arī
Table: Tabula
To: To
Abstract: Sažetak
Appendix: Dodatak
Bibliography: Literatura
Cc: Kopije
Chapter: Glava
Contents: Sadržaj
Encl: Prilozi
Figure: Slika
Glossary: Rečnik nepoznatih reči
Index: Registar
ListOfFigures: Spisak slika
ListOfTables: Spisak tabela
Page: Strana
Part: Deo
Preface: Predgovor
Proof: Dokaz
References: Bibliografija
See: Vidi
SeeAlso: Vidi takođe
Table: Tabela
To: Prima
Abstract: सारांश
Appendix: परिशिष्ट
Bibliography: संदर ग्रन्थ}
Cc: 
Chapter: अध्याय
Contents: विषय सूची
Encl: 
Figure: चित्र}
Headpage: पृषठ
Index: सूची
ListOfFigures: चित्रों की सूची
ListOfTables: तालिकाओं की सूची
Page: पृषठ
Part: खणड
Preface: प्रस्तावना}
References: हवाले
See: देखिए
SeeAlso: और देखिए
Table: तालिका
To: 
Preface: ﻡﺪﺨﻟ
References: ﺎﻠﻣﺭﺎﺠﻋ
Abstract: ﻢﻠﺨﺻ
Bibliography: ﺎﻠﻤﺻﺍﺩﺭ
Chapter: ﺏﺎﺑ
Appendix: ﺎﻠﻣﻼﺤﻗ
Contents: ﺎﻠﻤﺤﺗﻮﻳﺎﺗ
ListOfFigures: ﻕﺎﺌﻣﺓ ﺍﻸﺸﻛﺎﻟ
ListOfTables: ﻕﺎﺌﻣﺓ ﺎﻠﺟﺩﺍﻮﻟ
Index: ﺎﻠﻔﻫﺮﺳ
Figure: ﺶﻜﻟ
Table: ﺝﺩﻮﻟ
Part: ﺎﻠﻘﺴﻣ
Encl: ﺎﻠﻣﺮﻔﻗﺎﺗ
To: ﺈﻟﻯ
Page: ﺺﻔﺣﺓ
See: ﺭﺎﺠﻋ
SeeAlso: ﺭﺎﺠﻋ ﺄﻴﺿًﺍ
Proof: ﺏﺮﻫﺎﻧ
Glossary: ﻕﺎﻣﻮﺳ
Abstract: Sommario
Appendix: Appendice
Bibliography: Bibliografia
Cc: e p. c.
Chapter: Capitolo
Contents: Indice
Encl: Allegati
Figure: Figura
Glossary: Glossario
Index: Indice analitico
ListOfFigures: Elenco delle figure
ListOfTables: Elenco delle tabelle
Page: Pag.
Part: Parte
Preface: Prefazione
Proof: Dimostrazione
References: Riferimenti bibliografici
See: vedi
SeeAlso: vedi anche
Table: Tabella
To: Per
Abstract: Resumen
Appendix: Apéndice
Bibliography: Bibliografía
Cc: Copia a
Chapter: Capítulo
Contents: Índice general
Encl: Adjunto(s)
Figure: Figura
Glossary: Glosario
Index: Índice alfabético
ListOfFigures: Índice de figuras
ListOfTables: Índice de cuadros
Page: Página
Part: Parte
Preface: Prefacio
Proof: Prueba
References: Referencias
See: véase
SeeAlso: véase también
Table: Cuadro
To: A
Abstract: Tiivistelmä
Appendix: Liite
Bibliography: Kirjallisuutta
Cc: Jakelu
Chapter: Luku
Contents: Sisältö
Encl: Liitteet
Figure: Kuva
Glossary: Sanasto
Index: Hakemisto
ListOfFigures: Kuvat
ListOfTables: Taulukot
Page: Sivu
Part: Osa
Preface: Esipuhe
Proof: Todistus
References: Viitteet
See: katso
SeeAlso: katso myös
Table: Taulukko
To: Vastaanottaja
Abstract: Tóm tắt nội dung
Also: Xem thêm
Appendix: Phụ lục
Bib: Tài liệu tham khảo
Cc: Cùng gửi
Chapter: Chương
Contents: Mục lục
Encl: Kèm theo
Figure: Hình
Glossary: Từ điển chú giải
Headto: Gửi
Index: Chỉ mục
Listfigure: Danh sách hình vẽ
Listtable: Danh sách bẳng
Page: Trang
Part: Phần
Preface: Lời nói đầu
Proof: Chứng minh
References: Tài liệu
See: Xem
Table: Bẳng
/* This defines styles and classes used in the book */
@page {
  margin: 10px;
}
html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p,
blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img,
ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center,
fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed, figure, figcaption, footer, header,
hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video, ol,
ul, li, dl, dt, dd {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  vertical-align: baseline;
}
html {
  line-height: 1.2;
  font-family: Georgia, serif;
  color: #1a1a1a;
  background-color: #fdfdfd;
}
p {
  text-indent: 0;
  margin: 1em 0;
  widows: 2;
  orphans: 2;
}
a, a:visited {
  color: #1a1a1a;
}
img {
  max-width: 100%;
}
sup {
  vertical-align: super;
  font-size: smaller;
}
sub {
  vertical-align: sub;
  font-size: smaller;
}
h1 {
  margin: 3em 0 0 0;
  font-size: 2em;
  page-break-before: always;
  line-height: 150%;
}
h2 {
  margin: 1.5em 0 0 0;
  font-size: 1.5em;
  line-height: 135%;
}
h3 {
  margin: 1.3em 0 0 0;
  font-size: 1.3em;
}
h4 {
  margin: 1.2em 0 0 0;
  font-size: 1.2em;
}
h5 {
  margin: 1.1em 0 0 0;
  font-size: 1.1em;
}
h6 {
  font-size: 1em;
}
h1, h2, h3, h4, h5, h6 {
  text-indent: 0;
  text-align: left;
  font-weight: bold;
  page-break-after: avoid;
  page-break-inside: avoid;
}

ol, ul {
  margin: 1em 0 0 1.7em;
}
li > ol, li > ul {
  margin-top: 0;
}
blockquote {
  margin: 1em 0 1em 1.7em;
}
code {
  font-family: Menlo, Monaco, 'Lucida Console', Consolas, monospace;
  font-size: 85%;
  margin: 0;
  hyphens: manual;
}
pre {
  margin: 1em 0;
  overflow: auto;
}
pre code {
  padding: 0;
  overflow: visible;
  overflow-wrap: normal;
}
.sourceCode {
  background-color: transparent;
  overflow: visible;
}
hr {
  background-color: #1a1a1a;
  border: none;
  height: 1px;
  margin: 1em 0;
}
table {
  margin: 1em 0;
  border-collapse: collapse;
  width: 100%;
  overflow-x: auto;
  display: block;
}
table caption {
  margin-bottom: 0.75em;
}
tbody {
  margin-top: 0.5em;
  border-top: 1px solid #1a1a1a;
  border-bottom: 1px solid #1a1a1a;
}
th, td {
  padding: 0.25em 0.5em 0.25em 0.5em;
}
th {
  border-top: 1px solid #1a1a1a;
}
header {
  margin-bottom: 4em;
  text-align: center;
}
#TOC li {
  list-style: none;
}
#TOC ul {
  padding-left: 1.3em;
}
#TOC > ul {
  padding-left: 0;
}
#TOC a:not(:hover) {
  text-decoration: none;
}
code {
  white-space: pre-wrap;
}
span.smallcaps {
  font-variant: small-caps;
}

/* This is the most compatible CSS, but it only allows two columns: */
div.column {
  display: inline-block;
  vertical-align: top;
  width: 50%;
}
/* If you can rely on CSS3 support, use this instead: */
/* div.columns {
  display: flex;
  gap: min(4vw, 1.5em);
}
div.column {
  flex: auto;
  overflow-x: auto;
} */

div.hanging-indent {
  margin-left: 1.5em;
  text-indent: -1.5em;
}
ul.task-list {
  list-style: none;
}
ul.task-list li input[type="checkbox"] {
  width: 0.8em;
  margin: 0 0.8em 0.2em -1.6em;
  vertical-align: middle;
}
.display.math {
  display: block;
  text-align: center;
  margin: 0.5rem auto;
}

/* For title, author, and date on the cover page */
h1.title { }
p.author { }
p.date { }

nav#toc ol, nav#landmarks ol {
  padding: 0;
  margin-left: 1em;
}
nav#toc ol li, nav#landmarks ol li {
  list-style-type: none;
  margin: 0;
  padding: 0;
}
a.footnote-ref {
  vertical-align: super;
}
em, em em em, em em em em em {
  font-style: italic;
}
em em, em em em em {
  font-style: normal;
}
q {
  quotes: "“" "”" "‘" "’";
}
@media screen { /* Workaround for iBooks issue; see #6242 */
  .sourceCode {
    overflow: visible !important;
    white-space: pre-wrap !important;
  }
}
<?xml version="1.0" encoding="UTF-8"?>
<office:document-meta
    xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    xmlns:dc="http://purl.org/dc/elements/1.1/"
    xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0"
    xmlns:ooo="http://openoffice.org/2004/office"
    xmlns:grddl="http://www.w3.org/2003/g/data-view#"
    office:version="1.3">
  <office:meta>
    <meta:document-statistic
      meta:table-count="0" meta:image-count="0" meta:object-count="0"
      meta:page-count="1" meta:paragraph-count="2" meta:word-count="3"
      meta:character-count="14"
      meta:non-whitespace-character-count="12"/>
    <meta:generator>Pandoc</meta:generator>
  </office:meta>
</office:document-meta>
application/vnd.oasis.opendocument.text<?xml version="1.0" encoding="utf-8"?>
<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
  <rdf:Description rdf:about="styles.xml">
    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/odf#StylesFile"/>
  </rdf:Description>
  <rdf:Description rdf:about="">
    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="styles.xml"/>
  </rdf:Description>
  <rdf:Description rdf:about="content.xml">
    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/odf#ContentFile"/>
  </rdf:Description>
  <rdf:Description rdf:about="">
    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="content.xml"/>
  </rdf:Description>
  <rdf:Description rdf:about="">
    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#Document"/>
  </rdf:Description>
</rdf:RDF>
<?xml version="1.0" encoding="UTF-8"?>
<office:document-content xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:style="urn:oasis:names:tc:opendocument:xmlns:style:1.0" xmlns:text="urn:oasis:names:tc:opendocument:xmlns:text:1.0" xmlns:table="urn:oasis:names:tc:opendocument:xmlns:table:1.0" xmlns:draw="urn:oasis:names:tc:opendocument:xmlns:drawing:1.0" xmlns:fo="urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:number="urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0" xmlns:svg="urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0" xmlns:chart="urn:oasis:names:tc:opendocument:xmlns:chart:1.0" xmlns:dr3d="urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0" xmlns:math="http://www.w3.org/1998/Math/MathML" xmlns:form="urn:oasis:names:tc:opendocument:xmlns:form:1.0" xmlns:script="urn:oasis:names:tc:opendocument:xmlns:script:1.0" xmlns:ooo="http://openoffice.org/2004/office" xmlns:ooow="http://openoffice.org/2004/writer" xmlns:oooc="http://openoffice.org/2004/calc" xmlns:dom="http://www.w3.org/2001/xml-events" xmlns:xforms="http://www.w3.org/2002/xforms" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:rpt="http://openoffice.org/2005/report" xmlns:of="urn:oasis:names:tc:opendocument:xmlns:of:1.2" xmlns:xhtml="http://www.w3.org/1999/xhtml" xmlns:grddl="http://www.w3.org/2003/g/data-view#" xmlns:officeooo="http://openoffice.org/2009/office" xmlns:tableooo="http://openoffice.org/2009/table" xmlns:drawooo="http://openoffice.org/2010/draw" xmlns:calcext="urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0" xmlns:field="urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0" xmlns:formx="urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0" xmlns:css3t="http://www.w3.org/TR/css3-text/" office:version="1.3"><office:scripts/><office:font-face-decls><style:font-face style:name="StarSymbol" svg:font-family="StarSymbol"/><style:font-face style:name="Tahoma1" svg:font-family="Tahoma"/><style:font-face style:name="Courier New" svg:font-family="&apos;Courier New&apos;" style:font-family-generic="modern" style:font-pitch="fixed"/><style:font-face style:name="Times New Roman" svg:font-family="&apos;Times New Roman&apos;" style:font-family-generic="roman" style:font-pitch="variable"/><style:font-face style:name="Arial" svg:font-family="Arial" style:font-family-generic="swiss" style:font-pitch="variable"/><style:font-face style:name="Lucida Sans Unicode" svg:font-family="&apos;Lucida Sans Unicode&apos;" style:font-family-generic="system" style:font-pitch="variable"/><style:font-face style:name="Tahoma" svg:font-family="Tahoma" style:font-family-generic="system" style:font-pitch="variable"/></office:font-face-decls><office:automatic-styles><style:style style:name="P1" style:family="paragraph" style:parent-style-name="Footer"><style:paragraph-properties fo:text-align="center" style:justify-single-word="false"/></style:style></office:automatic-styles><office:body><office:text><text:sequence-decls><text:sequence-decl text:display-outline-level="0" text:name="Illustration"/><text:sequence-decl text:display-outline-level="0" text:name="Table"/><text:sequence-decl text:display-outline-level="0" text:name="Text"/><text:sequence-decl text:display-outline-level="0" text:name="Drawing"/></text:sequence-decls><text:p text:style-name="Standard">Hello World!</text:p></office:text></office:body></office:document-content>
<?xml version="1.0" encoding="utf-8"?>
<office:document-styles xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0"
xmlns:style="urn:oasis:names:tc:opendocument:xmlns:style:1.0"
xmlns:text="urn:oasis:names:tc:opendocument:xmlns:text:1.0"
xmlns:table="urn:oasis:names:tc:opendocument:xmlns:table:1.0"
xmlns:draw="urn:oasis:names:tc:opendocument:xmlns:drawing:1.0"
xmlns:fo="urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0"
xmlns:xlink="http://www.w3.org/1999/xlink"
xmlns:dc="http://purl.org/dc/elements/1.1/"
xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0"
xmlns:number="urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0"
xmlns:svg="urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0"
xmlns:chart="urn:oasis:names:tc:opendocument:xmlns:chart:1.0"
xmlns:dr3d="urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0"
xmlns:math="http://www.w3.org/1998/Math/MathML"
xmlns:form="urn:oasis:names:tc:opendocument:xmlns:form:1.0"
xmlns:script="urn:oasis:names:tc:opendocument:xmlns:script:1.0"
xmlns:ooo="http://openoffice.org/2004/office"
xmlns:ooow="http://openoffice.org/2004/writer"
xmlns:oooc="http://openoffice.org/2004/calc"
xmlns:dom="http://www.w3.org/2001/xml-events"
xmlns:rpt="http://openoffice.org/2005/report"
xmlns:of="urn:oasis:names:tc:opendocument:xmlns:of:1.2"
xmlns:xhtml="http://www.w3.org/1999/xhtml"
xmlns:grddl="http://www.w3.org/2003/g/data-view#"
xmlns:officeooo="http://openoffice.org/2009/office"
xmlns:tableooo="http://openoffice.org/2009/table"
xmlns:drawooo="http://openoffice.org/2010/draw"
xmlns:calcext="urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0"
xmlns:css3t="http://www.w3.org/TR/css3-text/" office:version="1.3">
  <office:font-face-decls>
    <style:font-face style:name="StarSymbol"
    svg:font-family="StarSymbol" />
    <style:font-face style:name="Tahoma1"
    svg:font-family="Tahoma" />
    <style:font-face style:name="Courier New"
    svg:font-family="'Courier New'"
    style:font-family-generic="modern" style:font-pitch="fixed" />
    <style:font-face style:name="Times New Roman"
    svg:font-family="'Times New Roman'"
    style:font-family-generic="roman"
    style:font-pitch="variable" />
    <style:font-face style:name="Arial" svg:font-family="Arial"
    style:font-family-generic="swiss"
    style:font-pitch="variable" />
    <style:font-face style:name="Lucida Sans Unicode"
    svg:font-family="'Lucida Sans Unicode'"
    style:font-family-generic="system"
    style:font-pitch="variable" />
    <style:font-face style:name="Tahoma" svg:font-family="Tahoma"
    style:font-family-generic="system"
    style:font-pitch="variable" />
  </office:font-face-decls>
  <office:styles>
    <style:default-style style:family="graphic">
      <style:graphic-properties fo:wrap-option="wrap"
      draw:shadow-offset-x="0.1181in"
      draw:shadow-offset-y="0.1181in"
      draw:start-line-spacing-horizontal="0.1114in"
      draw:start-line-spacing-vertical="0.1114in"
      draw:end-line-spacing-horizontal="0.1114in"
      draw:end-line-spacing-vertical="0.1114in"
      style:flow-with-text="false" />
      <style:paragraph-properties style:text-autospace="ideograph-alpha"
      style:line-break="strict" style:writing-mode="lr-tb"
      style:font-independent-line-spacing="false">
        <style:tab-stops />
      </style:paragraph-properties>
      <style:text-properties style:use-window-font-color="true"
      fo:font-size="12pt" fo:language="en" fo:country="US"
      style:letter-kerning="true" style:font-size-asian="12pt"
      style:language-asian="zxx" style:country-asian="none"
      style:font-size-complex="12pt" style:language-complex="zxx"
      style:country-complex="none" />
    </style:default-style>
    <style:style style:name="Author" style:family="paragraph"
      style:parent-style-name="Standard" style:class="html">
        <style:paragraph-properties fo:text-align="center" />
    </style:style>
    <style:style style:name="Date" style:family="paragraph"
      style:parent-style-name="Standard" style:class="html">
        <style:paragraph-properties fo:text-align="center" />
    </style:style>
    <style:style style:name="Abstract" style:family="paragraph"
      style:parent-style-name="Standard" style:class="html">
      <style:paragraph-properties fo:margin-left="0.3937in"
      fo:margin-right="0.3937in" fo:margin-top="0.1in"
      fo:margin-bottom="0.1in" style:contextual-spacing="false"
      fo:text-indent="0in" style:auto-text-indent="false" />
    </style:style>
    <style:default-style style:family="paragraph">
      <style:paragraph-properties fo:hyphenation-ladder-count="no-limit"
      style:text-autospace="ideograph-alpha"
      style:punctuation-wrap="hanging" style:line-break="strict"
      style:tab-stop-distance="0.4925in"
      style:writing-mode="page" />
      <style:text-properties style:use-window-font-color="true"
      style:font-name="Times New Roman" fo:font-size="12pt"
      fo:language="en" fo:country="US" style:letter-kerning="true"
      style:font-name-asian="Lucida Sans Unicode"
      style:font-size-asian="12pt" style:language-asian="zxx"
      style:country-asian="none" style:font-name-complex="Tahoma"
      style:font-size-complex="12pt" style:language-complex="zxx"
      style:country-complex="none" fo:hyphenate="false"
      fo:hyphenation-remain-char-count="2"
      fo:hyphenation-push-char-count="2" />
    </style:default-style>
    <style:default-style style:family="table">
      <style:table-properties table:border-model="collapsing" />
    </style:default-style>
    <style:default-style style:family="table-row">
      <style:table-row-properties fo:keep-together="auto" />
    </style:default-style>
    <style:style style:name="Standard" style:family="paragraph"
    style:class="text" />
    <style:style style:name="Heading" style:family="paragraph"
    style:parent-style-name="Standard"
    style:next-style-name="Text_20_body" style:class="text">
      <style:paragraph-properties fo:margin-top="0.1665in"
      fo:margin-bottom="0.0835in" style:contextual-spacing="false"
      fo:keep-with-next="always" />
      <style:text-properties style:font-name="Arial"
      fo:font-size="14pt"
      style:font-name-asian="Lucida Sans Unicode"
      style:font-size-asian="14pt" style:font-name-complex="Tahoma"
      style:font-size-complex="14pt" />
    </style:style>
    <style:style style:name="Text_20_body"
    style:display-name="Text body" style:family="paragraph"
    style:parent-style-name="Standard" style:class="text">
      <style:paragraph-properties fo:margin-top="0.0598in"
      fo:margin-bottom="0.0598in"
      style:contextual-spacing="false" />
    </style:style>
    <style:style style:name="List" style:family="paragraph"
    style:parent-style-name="Text_20_body" style:class="list">
      <style:text-properties style:font-name-complex="Tahoma1" />
    </style:style>
    <style:style style:name="Caption" style:family="paragraph"
    style:parent-style-name="Standard" style:class="extra">
      <style:paragraph-properties fo:margin-top="0.0835in"
      fo:margin-bottom="0.0835in" style:contextual-spacing="false"
      text:number-lines="false" text:line-number="0" />
      <style:text-properties fo:font-size="12pt"
      fo:font-style="italic" style:font-size-asian="12pt"
      style:font-style-asian="italic"
      style:font-name-complex="Tahoma1"
      style:font-size-complex="12pt"
      style:font-style-complex="italic" />
    </style:style>
    <style:style style:name="Table" style:family="paragraph"
    style:parent-style-name="Caption" style:class="extra">
    </style:style>
    <style:style style:name="FigureCaption" style:family="paragraph"
    style:parent-style-name="Caption" style:class="extra">
    </style:style>
    <style:style style:name="Figure" style:family="paragraph"
    style:parent-style-name="Standard" style:class="extra">
      <style:paragraph-properties text:number-lines="false"
      text:line-number="0" />
    </style:style>
    <style:style style:name="FigureWithCaption" style:family="paragraph"
    style:parent-style-name="Figure" style:class="extra">
      <style:paragraph-properties text:number-lines="false"
      text:line-number="0" fo:keep-with-next="always" />
    </style:style>
    <style:style style:name="Index" style:family="paragraph"
    style:parent-style-name="Standard" style:class="index">
      <style:paragraph-properties text:number-lines="false"
      text:line-number="0" />
      <style:text-properties style:font-name-complex="Tahoma1" />
    </style:style>
    <style:style style:name="Heading_20_1"
    style:display-name="Heading 1" style:family="paragraph"
    style:parent-style-name="Heading"
    style:next-style-name="Text_20_body"
    style:default-outline-level="1" style:class="text">
      <style:text-properties fo:font-size="115%"
      fo:font-weight="bold" style:font-size-asian="115%"
      style:font-weight-asian="bold" style:font-size-complex="115%"
      style:font-weight-complex="bold" />
    </style:style>
    <style:style style:name="Heading_20_2"
    style:display-name="Heading 2" style:family="paragraph"
    style:parent-style-name="Heading"
    style:next-style-name="Text_20_body"
    style:default-outline-level="2" style:class="text">
      <style:text-properties fo:font-size="14pt"
      fo:font-style="italic" fo:font-weight="bold"
      style:font-size-asian="14pt" style:font-style-asian="italic"
      style:font-weight-asian="bold" style:font-size-complex="14pt"
      style:font-style-complex="italic"
      style:font-weight-complex="bold" />
    </style:style>
    <style:style style:name="Heading_20_3"
    style:display-name="Heading 3" style:family="paragraph"
    style:parent-style-name="Heading"
    style:next-style-name="Text_20_body"
    style:default-outline-level="3" style:class="text">
      <style:text-properties fo:font-size="14pt"
      fo:font-weight="bold" style:font-size-asian="14pt"
      style:font-weight-asian="bold" style:font-size-complex="14pt"
      style:font-weight-complex="bold" />
    </style:style>
    <style:style style:name="Heading_20_4"
    style:display-name="Heading 4" style:family="paragraph"
    style:parent-style-name="Heading"
    style:next-style-name="Text_20_body"
    style:default-outline-level="4" style:class="text">
      <style:text-properties fo:font-size="85%"
      fo:font-style="italic" fo:font-weight="bold"
      style:font-size-asian="85%" style:font-style-asian="italic"
      style:font-weight-asian="bold" style:font-size-complex="85%"
      style:font-style-complex="italic"
      style:font-weight-complex="bold" />
    </style:style>
    <style:style style:name="Heading_20_5"
    style:display-name="Heading 5" style:family="paragraph"
    style:parent-style-name="Heading"
    style:next-style-name="Text_20_body"
    style:default-outline-level="5" style:class="text">
      <style:text-properties fo:font-size="85%"
      fo:font-weight="bold" style:font-size-asian="85%"
      style:font-weight-asian="bold" style:font-size-complex="85%"
      style:font-weight-complex="bold" />
    </style:style>
    <style:style style:name="Heading_20_6"
    style:display-name="Heading 6" style:family="paragraph"
    style:parent-style-name="Heading"
    style:next-style-name="Text_20_body"
    style:default-outline-level="6" style:class="text">
      <style:text-properties fo:font-size="75%"
      fo:font-weight="bold" style:font-size-asian="75%"
      style:font-weight-asian="bold" style:font-size-complex="75%"
      style:font-weight-complex="bold" />
    </style:style>
    <style:style style:name="Quotations" style:family="paragraph"
    style:parent-style-name="Standard" style:class="html">
      <style:paragraph-properties fo:margin-left="0.3937in"
      fo:margin-right="0.3937in" fo:margin-top="0.1in"
      fo:margin-bottom="0.1in" style:contextual-spacing="false"
      fo:text-indent="0in" style:auto-text-indent="false" />
    </style:style>
    <style:style style:name="Preformatted_20_Text"
    style:display-name="Preformatted Text" style:family="paragraph"
    style:parent-style-name="Standard" style:class="html">
      <style:paragraph-properties fo:margin-top="0in"
      fo:margin-bottom="0in" style:contextual-spacing="false" />
      <style:text-properties style:font-name="Courier New"
      fo:font-size="10pt" style:font-name-asian="Courier New"
      style:font-size-asian="10pt"
      style:font-name-complex="Courier New"
      style:font-size-complex="10pt"
      fo:language="zxx" />
    </style:style>
    <style:style style:name="Source_Text" style:family="text">
      <style:text-properties style:font-name="Courier New"
      fo:font-size="10pt" style:font-name-asian="Courier New"
      style:font-size-asian="10pt"
      style:font-name-complex="Courier New"
      style:font-size-complex="10pt"
      fo:language="zxx" />
    </style:style>
    <style:style style:name="Highlighted" style:family="text">
      <style:text-properties fo:background-color="#ffff38" />
    </style:style>
    <style:style style:name="Definition_20_Term"
    style:display-name="Definition Term" style:family="paragraph"
    style:parent-style-name="Standard"
    style:next-style-name="Definition_20_Definition">
      <style:paragraph-properties fo:margin-top="0.0598in"
      fo:margin-bottom="0.0598in"
      style:contextual-spacing="false" />
    </style:style>
    <style:style style:name="Definition_20_Definition"
    style:display-name="Definition Definition"
    style:family="paragraph" style:parent-style-name="Standard"
    style:next-style-name="Text_20_body">
      <style:paragraph-properties fo:margin-left="0.5in"
      fo:margin-right="0in" fo:text-indent="0in"
      style:auto-text-indent="false" />
    </style:style>
    <style:style style:name="Table_20_Contents"
    style:display-name="Table Contents" style:family="paragraph"
    style:parent-style-name="Standard" style:class="extra">
      <style:paragraph-properties fo:margin-left="0.0299in"
      fo:margin-right="0.0299in" fo:text-indent="0in"
      style:auto-text-indent="false" text:number-lines="false"
      text:line-number="0" />
    </style:style>
    <style:style style:name="Table_20_Heading"
    style:display-name="Table Heading" style:family="paragraph"
    style:parent-style-name="Table_20_Contents"
    style:class="extra">
      <style:paragraph-properties fo:margin-left="0.0299in"
      fo:margin-right="0.0299in" fo:text-align="start"
      style:justify-single-word="false" fo:text-indent="0in"
      style:auto-text-indent="false" style:shadow="none"
      text:number-lines="false" text:line-number="0" />
      <style:text-properties fo:font-weight="bold"
      style:font-weight-asian="bold"
      style:font-weight-complex="bold" />
    </style:style>
    <style:style style:name="Footnote" style:family="paragraph"
    style:parent-style-name="Standard" style:class="extra">
      <style:paragraph-properties fo:margin-left="0.1965in"
      fo:margin-right="0in" fo:text-indent="-0.1965in"
      style:auto-text-indent="false" text:number-lines="false"
      text:line-number="0" />
      <style:text-properties fo:font-size="10pt"
      style:font-size-asian="10pt"
      style:font-size-complex="10pt" />
    </style:style>
    <style:style style:name="Footer" style:family="paragraph"
    style:parent-style-name="Standard" style:class="extra">
      <style:paragraph-properties text:number-lines="false"
      text:line-number="0">
        <style:tab-stops>
          <style:tab-stop style:position="3.25in"
          style:type="center" />
          <style:tab-stop style:position="6.5in"
          style:type="right" />
        </style:tab-stops>
      </style:paragraph-properties>
    </style:style>
    <style:style style:name="Definition_20_Term_20_Tight"
    style:display-name="Definition Term Tight"
    style:family="paragraph" style:parent-style-name="Standard"
    style:next-style-name="Definition_20_Definition_20_Tight">
      <style:paragraph-properties fo:margin-top="0.0799in"
      fo:margin-bottom="0.0799in"
      style:contextual-spacing="false" />
    </style:style>
    <style:style style:name="Definition_20_Definition_20_Tight"
    style:display-name="Definition Definition Tight"
    style:family="paragraph" style:parent-style-name="Standard">
      <style:paragraph-properties fo:margin-left="0.5in"
      fo:margin-right="0in" fo:margin-top="0in"
      fo:margin-bottom="0in" style:contextual-spacing="false"
      fo:text-indent="0in" style:auto-text-indent="false" />
    </style:style>
    <style:style style:name="Horizontal_20_Line"
    style:display-name="Horizontal Line" style:family="paragraph"
    style:parent-style-name="Standard"
    style:next-style-name="Text_20_body" style:class="html">
      <style:paragraph-properties fo:margin-top="0in"
      fo:margin-bottom="0.1965in" style:contextual-spacing="false"
      style:border-line-width-bottom="0.0008in 0.0138in 0.0008in"
      fo:padding="0in" fo:border-left="none" fo:border-right="none"
      fo:border-top="none" fo:border-bottom="1.11pt double #808080"
      text:number-lines="false" text:line-number="0"
      style:join-border="false" />
      <style:text-properties fo:font-size="6pt"
      style:font-size-asian="6pt" style:font-size-complex="6pt" />
    </style:style>
    <style:style style:name="First_20_paragraph"
    style:display-name="First paragraph" style:family="paragraph"
    style:parent-style-name="Text_20_body"
    style:next-style-name="Text_20_body" style:class="text" />
    <style:style style:name="Numbering_20_Symbols"
    style:display-name="Numbering Symbols" style:family="text" />
    <style:style style:name="Bullet_20_Symbols"
    style:display-name="Bullet Symbols" style:family="text">
      <style:text-properties style:font-name="StarSymbol"
      fo:font-size="9pt" style:font-name-asian="StarSymbol"
      style:font-size-asian="9pt"
      style:font-name-complex="StarSymbol"
      style:font-size-complex="9pt" />
    </style:style>
    <style:style style:name="Emphasis" style:family="text">
      <style:text-properties fo:font-style="italic"
      style:font-style-asian="italic"
      style:font-style-complex="italic" />
    </style:style>
    <style:style style:name="Strong_20_Emphasis"
    style:display-name="Strong Emphasis" style:family="text">
      <style:text-properties fo:font-weight="bold"
      style:font-weight-asian="bold"
      style:font-weight-complex="bold" />
    </style:style>
    <style:style style:name="Strikeout" style:family="text">
      <style:text-properties style:text-line-through-style="solid" />
    </style:style>
    <style:style style:name="Superscript" style:family="text">
      <style:text-properties style:text-position="super 58%" />
    </style:style>
    <style:style style:name="Subscript" style:family="text">
      <style:text-properties style:text-position="sub 58%" />
    </style:style>
    <style:style style:name="Citation" style:family="text">
      <style:text-properties fo:font-style="italic"
      style:font-style-asian="italic"
      style:font-style-complex="italic" />
    </style:style>
    <style:style style:name="Teletype" style:family="text">
      <style:text-properties style:font-name="Courier New"
      style:font-name-asian="Courier New"
      style:font-name-complex="Courier New" />
    </style:style>

    <style:style style:name="Internet_20_link"
    style:display-name="Internet link" style:family="text">
      <style:text-properties fo:color="#000080"
      style:text-underline-style="solid"
      style:text-underline-width="auto"
      style:text-underline-color="font-color" />
    </style:style>
    <style:style style:name="Footnote_20_Symbol"
    style:display-name="Footnote Symbol" style:family="text" />
    <style:style style:name="Footnote_20_anchor"
    style:display-name="Footnote anchor" style:family="text">
      <style:text-properties style:text-position="super 58%" />
    </style:style>
    <style:style style:name="Definition" style:family="text" />
    <text:outline-style style:name="Outline">
      <text:outline-level-style text:level="1" style:num-format="">
        <style:list-level-properties text:min-label-distance="0.15in" />
      </text:outline-level-style>
      <text:outline-level-style text:level="2" style:num-format="">
        <style:list-level-properties text:min-label-distance="0.15in" />
      </text:outline-level-style>
      <text:outline-level-style text:level="3" style:num-format="">
        <style:list-level-properties text:min-label-distance="0.15in" />
      </text:outline-level-style>
      <text:outline-level-style text:level="4" style:num-format="">
        <style:list-level-properties text:min-label-distance="0.15in" />
      </text:outline-level-style>
      <text:outline-level-style text:level="5" style:num-format="">
        <style:list-level-properties text:min-label-distance="0.15in" />
      </text:outline-level-style>
      <text:outline-level-style text:level="6" style:num-format="">
        <style:list-level-properties text:min-label-distance="0.15in" />
      </text:outline-level-style>
      <text:outline-level-style text:level="7" style:num-format="">
        <style:list-level-properties text:min-label-distance="0.15in" />
      </text:outline-level-style>
      <text:outline-level-style text:level="8" style:num-format="">
        <style:list-level-properties text:min-label-distance="0.15in" />
      </text:outline-level-style>
      <text:outline-level-style text:level="9" style:num-format="">
        <style:list-level-properties text:min-label-distance="0.15in" />
      </text:outline-level-style>
      <text:outline-level-style text:level="10"
      style:num-format="">
        <style:list-level-properties text:min-label-distance="0.15in" />
      </text:outline-level-style>
    </text:outline-style>
    <text:list-style style:name="Numbering_20_1"
    style:display-name="Numbering 1">
      <text:list-level-style-number text:level="1"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="1">
        <style:list-level-properties text:min-label-width="0.1965in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="2"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="1">
        <style:list-level-properties text:space-before="0.1972in"
        text:min-label-width="0.1965in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="3"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="1">
        <style:list-level-properties text:space-before="0.3937in"
        text:min-label-width="0.1965in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="4"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="1">
        <style:list-level-properties text:space-before="0.5909in"
        text:min-label-width="0.1965in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="5"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="1">
        <style:list-level-properties text:space-before="0.7874in"
        text:min-label-width="0.1965in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="6"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="1">
        <style:list-level-properties text:space-before="0.9846in"
        text:min-label-width="0.1965in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="7"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="1">
        <style:list-level-properties text:space-before="1.1815in"
        text:min-label-width="0.1965in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="8"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="1">
        <style:list-level-properties text:space-before="1.3787in"
        text:min-label-width="0.1965in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="9"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="1">
        <style:list-level-properties text:space-before="1.5752in"
        text:min-label-width="0.1965in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="10"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="1">
        <style:list-level-properties text:space-before="1.7724in"
        text:min-label-width="0.1965in" />
      </text:list-level-style-number>
    </text:list-style>
    <text:list-style style:name="Numbering_20_2"
    style:display-name="Numbering 2">
      <text:list-level-style-number text:level="1"
      text:style-name="Numbering_20_Symbols" style:num-format="1">
        <style:list-level-properties text:min-label-width="0.1965in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="2"
      text:style-name="Numbering_20_Symbols" style:num-format="1"
      text:start-value="2">
        <style:list-level-properties text:space-before="0.1965in"
        text:min-label-width="0.1965in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="3"
      text:style-name="Numbering_20_Symbols" style:num-format="1"
      text:start-value="3">
        <style:list-level-properties text:space-before="0.3929in"
        text:min-label-width="0.3937in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="4"
      text:style-name="Numbering_20_Symbols" style:num-format="1"
      text:start-value="4">
        <style:list-level-properties text:space-before="0.7866in"
        text:min-label-width="0.4925in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="5"
      text:style-name="Numbering_20_Symbols" style:num-format="1"
      text:start-value="5">
        <style:list-level-properties text:space-before="1.2791in"
        text:min-label-width="0.5902in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="6"
      text:style-name="Numbering_20_Symbols" style:num-format="1"
      text:start-value="6">
        <style:list-level-properties text:space-before="1.8693in"
        text:min-label-width="0.7091in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="7"
      text:style-name="Numbering_20_Symbols" style:num-format="1"
      text:start-value="7">
        <style:list-level-properties text:space-before="2.5783in"
        text:min-label-width="0.9055in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="8"
      text:style-name="Numbering_20_Symbols" style:num-format="1"
      text:start-value="8">
        <style:list-level-properties text:space-before="3.4839in"
        text:min-label-width="1.0236in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="9"
      text:style-name="Numbering_20_Symbols" style:num-format="1"
      text:start-value="9">
        <style:list-level-properties text:space-before="4.5075in"
        text:min-label-width="1.1028in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="10"
      text:style-name="Numbering_20_Symbols" style:num-format="1"
      text:start-value="10">
        <style:list-level-properties text:space-before="5.6102in"
        text:min-label-width="1.2209in" />
      </text:list-level-style-number>
    </text:list-style>
    <text:list-style style:name="Numbering_20_3"
    style:display-name="Numbering 3">
      <text:list-level-style-number text:level="1"
      text:style-name="Numbering_20_Symbols" style:num-format="1">
        <style:list-level-properties text:min-label-width="1.1811in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="2"
      text:style-name="Numbering_20_Symbols" style:num-format="1"
      text:start-value="2">
        <style:list-level-properties text:space-before="1.1815in"
        text:min-label-width="1.1811in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="3"
      text:style-name="Numbering_20_Symbols" style:num-format="1"
      text:start-value="3">
        <style:list-level-properties text:space-before="2.3626in"
        text:min-label-width="1.1811in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="4"
      text:style-name="Numbering_20_Symbols" style:num-format="1"
      text:start-value="4">
        <style:list-level-properties text:space-before="3.5441in"
        text:min-label-width="1.1811in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="5"
      text:style-name="Numbering_20_Symbols" style:num-format="1"
      text:start-value="5">
        <style:list-level-properties text:space-before="4.7252in"
        text:min-label-width="1.1811in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="6"
      text:style-name="Numbering_20_Symbols" style:num-format="1"
      text:start-value="6">
        <style:list-level-properties text:space-before="5.9063in"
        text:min-label-width="1.1811in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="7"
      text:style-name="Numbering_20_Symbols" style:num-format="1"
      text:start-value="7">
        <style:list-level-properties text:space-before="7.0878in"
        text:min-label-width="1.1811in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="8"
      text:style-name="Numbering_20_Symbols" style:num-format="1"
      text:start-value="8">
        <style:list-level-properties text:space-before="8.2689in"
        text:min-label-width="1.1811in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="9"
      text:style-name="Numbering_20_Symbols" style:num-format="1"
      text:start-value="9">
        <style:list-level-properties text:space-before="9.45in"
        text:min-label-width="1.1811in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="10"
      text:style-name="Numbering_20_Symbols" style:num-format="1"
      text:start-value="10">
        <style:list-level-properties text:space-before="10.6315in"
        text:min-label-width="1.1811in" />
      </text:list-level-style-number>
    </text:list-style>
    <text:list-style style:name="Numbering_20_4"
    style:display-name="Numbering 4">
      <text:list-level-style-number text:level="1"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="I">
        <style:list-level-properties text:min-label-width="0.1965in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="2"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="I" text:start-value="2">
        <style:list-level-properties text:space-before="0.1972in"
        text:min-label-width="0.1965in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="3"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="I" text:start-value="3">
        <style:list-level-properties text:space-before="0.3937in"
        text:min-label-width="0.1965in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="4"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="I" text:start-value="4">
        <style:list-level-properties text:space-before="0.5909in"
        text:min-label-width="0.1965in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="5"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="I" text:start-value="5">
        <style:list-level-properties text:space-before="0.7874in"
        text:min-label-width="0.1965in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="6"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="I" text:start-value="6">
        <style:list-level-properties text:space-before="0.9846in"
        text:min-label-width="0.1965in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="7"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="I" text:start-value="7">
        <style:list-level-properties text:space-before="1.1815in"
        text:min-label-width="0.1965in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="8"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="I" text:start-value="8">
        <style:list-level-properties text:space-before="1.3787in"
        text:min-label-width="0.1965in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="9"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="I" text:start-value="9">
        <style:list-level-properties text:space-before="1.5752in"
        text:min-label-width="0.1965in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="10"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="I" text:start-value="10">
        <style:list-level-properties text:space-before="1.7724in"
        text:min-label-width="0.1965in" />
      </text:list-level-style-number>
    </text:list-style>
    <text:list-style style:name="Numbering_20_5"
    style:display-name="Numbering 5">
      <text:list-level-style-number text:level="1"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="1">
        <style:list-level-properties text:min-label-width="0.1575in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="2"
      text:style-name="Numbering_20_Symbols" style:num-suffix="."
      style:num-format="1" text:start-value="2"
      text:display-levels="2">
        <style:list-level-properties text:space-before="0.1772in"
        text:min-label-width="0.2563in" />
      </text:list-level-style-number>
      <text:list-level-style-number text:level="3"
      text:style-name="Numbering_20_Symbols" style:num-suffix=")"
      style:num-format="a" text:start-value="3">
        <style:list-level-properties text:space-before="0.4331in"
        text:min-label-width="0.1772in" />
      </text:list-level-style-number>
      <text:list-level-style-bullet text:level="4"
      text:style-name="Numbering_20_Symbols" text:bullet-char="•">
        <style:list-level-properties text:space-before="0.6319in"
        text:min-label-width="0.1555in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="5"
      text:style-name="Numbering_20_Symbols" text:bullet-char="•">
        <style:list-level-properties text:space-before="0.7874in"
        text:min-label-width="0.1555in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="6"
      text:style-name="Numbering_20_Symbols" text:bullet-char="•">
        <style:list-level-properties text:space-before="0.9429in"
        text:min-label-width="0.1555in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="7"
      text:style-name="Numbering_20_Symbols" text:bullet-char="•">
        <style:list-level-properties text:space-before="1.0988in"
        text:min-label-width="0.1555in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="8"
      text:style-name="Numbering_20_Symbols" text:bullet-char="•">
        <style:list-level-properties text:space-before="1.2543in"
        text:min-label-width="0.1555in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="9"
      text:style-name="Numbering_20_Symbols" text:bullet-char="•">
        <style:list-level-properties text:space-before="1.4098in"
        text:min-label-width="0.1555in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="10"
      text:style-name="Numbering_20_Symbols" text:bullet-char="•">
        <style:list-level-properties text:space-before="1.5654in"
        text:min-label-width="0.1555in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
    </text:list-style>
    <text:list-style style:name="List_20_1"
    style:display-name="List 1">
      <text:list-level-style-bullet text:level="1"
      text:style-name="Numbering_20_Symbols" text:bullet-char="•">
        <style:list-level-properties text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="2"
      text:style-name="Numbering_20_Symbols" text:bullet-char="•">
        <style:list-level-properties text:space-before="0.1579in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="3"
      text:style-name="Numbering_20_Symbols" text:bullet-char="•">
        <style:list-level-properties text:space-before="0.3146in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="4"
      text:style-name="Numbering_20_Symbols" text:bullet-char="•">
        <style:list-level-properties text:space-before="0.4724in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="5"
      text:style-name="Numbering_20_Symbols" text:bullet-char="•">
        <style:list-level-properties text:space-before="0.6299in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="6"
      text:style-name="Numbering_20_Symbols" text:bullet-char="•">
        <style:list-level-properties text:space-before="0.7878in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="7"
      text:style-name="Numbering_20_Symbols" text:bullet-char="•">
        <style:list-level-properties text:space-before="0.9445in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="8"
      text:style-name="Numbering_20_Symbols" text:bullet-char="•">
        <style:list-level-properties text:space-before="1.1024in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="9"
      text:style-name="Numbering_20_Symbols" text:bullet-char="•">
        <style:list-level-properties text:space-before="1.2598in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="10"
      text:style-name="Numbering_20_Symbols" text:bullet-char="•">
        <style:list-level-properties text:space-before="1.4177in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
    </text:list-style>
    <text:list-style style:name="List_20_2"
    style:display-name="List 2">
      <text:list-level-style-bullet text:level="1"
      text:style-name="Numbering_20_Symbols" text:bullet-char="–">
        <style:list-level-properties text:min-label-width="0.1181in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="2"
      text:style-name="Numbering_20_Symbols" text:bullet-char="–">
        <style:list-level-properties text:space-before="0.1181in"
        text:min-label-width="0.1181in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="3"
      text:style-name="Numbering_20_Symbols" text:bullet-char="–">
        <style:list-level-properties text:space-before="0.2362in"
        text:min-label-width="0.1181in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="4"
      text:style-name="Numbering_20_Symbols" text:bullet-char="–">
        <style:list-level-properties text:space-before="0.3539in"
        text:min-label-width="0.1181in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="5"
      text:style-name="Numbering_20_Symbols" text:bullet-char="–">
        <style:list-level-properties text:space-before="0.472in"
        text:min-label-width="0.1181in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="6"
      text:style-name="Numbering_20_Symbols" text:bullet-char="–">
        <style:list-level-properties text:space-before="0.5902in"
        text:min-label-width="0.1181in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="7"
      text:style-name="Numbering_20_Symbols" text:bullet-char="–">
        <style:list-level-properties text:space-before="0.7091in"
        text:min-label-width="0.1181in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="8"
      text:style-name="Numbering_20_Symbols" text:bullet-char="–">
        <style:list-level-properties text:space-before="0.8272in"
        text:min-label-width="0.1181in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="9"
      text:style-name="Numbering_20_Symbols" text:bullet-char="–">
        <style:list-level-properties text:space-before="0.9453in"
        text:min-label-width="0.1181in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="10"
      text:style-name="Numbering_20_Symbols" text:bullet-char="–">
        <style:list-level-properties text:space-before="1.063in"
        text:min-label-width="0.1181in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
    </text:list-style>
    <text:list-style style:name="List_20_3"
    style:display-name="List 3">
      <text:list-level-style-bullet text:level="1"
      text:style-name="Numbering_20_Symbols" text:bullet-char="☑">
        <style:list-level-properties text:min-label-width="0.1555in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="2"
      text:style-name="Numbering_20_Symbols" text:bullet-char="□">
        <style:list-level-properties text:space-before="0.1555in"
        text:min-label-width="0.1555in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="3"
      text:style-name="Numbering_20_Symbols" text:bullet-char="☑">
        <style:list-level-properties text:min-label-width="0.1555in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="4"
      text:style-name="Numbering_20_Symbols" text:bullet-char="□">
        <style:list-level-properties text:space-before="0.1555in"
        text:min-label-width="0.1555in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="5"
      text:style-name="Numbering_20_Symbols" text:bullet-char="☑">
        <style:list-level-properties text:min-label-width="0.1555in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="6"
      text:style-name="Numbering_20_Symbols" text:bullet-char="□">
        <style:list-level-properties text:space-before="0.1555in"
        text:min-label-width="0.1555in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="7"
      text:style-name="Numbering_20_Symbols" text:bullet-char="☑">
        <style:list-level-properties text:min-label-width="0.1555in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="8"
      text:style-name="Numbering_20_Symbols" text:bullet-char="□">
        <style:list-level-properties text:space-before="0.1555in"
        text:min-label-width="0.1555in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="9"
      text:style-name="Numbering_20_Symbols" text:bullet-char="☑">
        <style:list-level-properties text:min-label-width="0.1555in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="10"
      text:style-name="Numbering_20_Symbols" text:bullet-char="□">
        <style:list-level-properties text:space-before="0.1555in"
        text:min-label-width="0.1555in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
    </text:list-style>
    <text:list-style style:name="List_20_4"
    style:display-name="List 4">
      <text:list-level-style-bullet text:level="1"
      text:style-name="Numbering_20_Symbols" text:bullet-char="➢">
        <style:list-level-properties text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="2"
      text:style-name="Numbering_20_Symbols" text:bullet-char="">
        <style:list-level-properties text:space-before="0.1579in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="3"
      text:style-name="Numbering_20_Symbols" text:bullet-char="">
        <style:list-level-properties text:space-before="0.3146in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="4"
      text:style-name="Numbering_20_Symbols" text:bullet-char="">
        <style:list-level-properties text:space-before="0.4724in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="5"
      text:style-name="Numbering_20_Symbols" text:bullet-char="">
        <style:list-level-properties text:space-before="0.6299in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="6"
      text:style-name="Numbering_20_Symbols" text:bullet-char="">
        <style:list-level-properties text:space-before="0.7878in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="7"
      text:style-name="Numbering_20_Symbols" text:bullet-char="">
        <style:list-level-properties text:space-before="0.9445in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="8"
      text:style-name="Numbering_20_Symbols" text:bullet-char="">
        <style:list-level-properties text:space-before="1.1024in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="9"
      text:style-name="Numbering_20_Symbols" text:bullet-char="">
        <style:list-level-properties text:space-before="1.2598in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="10"
      text:style-name="Numbering_20_Symbols" text:bullet-char="">
        <style:list-level-properties text:space-before="1.4177in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
    </text:list-style>
    <text:list-style style:name="List_20_5"
    style:display-name="List 5">
      <text:list-level-style-bullet text:level="1"
      text:style-name="Numbering_20_Symbols" text:bullet-char="✗">
        <style:list-level-properties text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="2"
      text:style-name="Numbering_20_Symbols" text:bullet-char="✗">
        <style:list-level-properties text:space-before="0.1579in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="3"
      text:style-name="Numbering_20_Symbols" text:bullet-char="✗">
        <style:list-level-properties text:space-before="0.3146in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="4"
      text:style-name="Numbering_20_Symbols" text:bullet-char="✗">
        <style:list-level-properties text:space-before="0.4724in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="5"
      text:style-name="Numbering_20_Symbols" text:bullet-char="✗">
        <style:list-level-properties text:space-before="0.6299in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="6"
      text:style-name="Numbering_20_Symbols" text:bullet-char="✗">
        <style:list-level-properties text:space-before="0.7878in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="7"
      text:style-name="Numbering_20_Symbols" text:bullet-char="✗">
        <style:list-level-properties text:space-before="0.9445in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="8"
      text:style-name="Numbering_20_Symbols" text:bullet-char="✗">
        <style:list-level-properties text:space-before="1.1024in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="9"
      text:style-name="Numbering_20_Symbols" text:bullet-char="✗">
        <style:list-level-properties text:space-before="1.2598in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
      <text:list-level-style-bullet text:level="10"
      text:style-name="Numbering_20_Symbols" text:bullet-char="✗">
        <style:list-level-properties text:space-before="1.4177in"
        text:min-label-width="0.1575in" />
        <style:text-properties style:font-name="StarSymbol" />
      </text:list-level-style-bullet>
    </text:list-style>
    <text:notes-configuration text:note-class="footnote"
    text:citation-style-name="Footnote_20_Symbol"
    text:citation-body-style-name="Footnote_20_anchor"
    style:num-format="1" text:start-value="0"
    text:footnotes-position="page"
    text:start-numbering-at="document" />
    <text:notes-configuration text:note-class="endnote"
    style:num-format="i" text:start-value="0" />
    <text:linenumbering-configuration text:number-lines="false"
    text:offset="0.1965in" style:num-format="1"
    text:number-position="left" text:increment="5" />
  </office:styles>
  <office:automatic-styles>
    <style:style style:name="MP1" style:family="paragraph"
    style:parent-style-name="Footer">
      <style:paragraph-properties fo:text-align="center"
      style:justify-single-word="false" />
    </style:style>
    <style:page-layout style:name="Mpm1">
      <style:page-layout-properties fo:page-width="8.5in"
      fo:page-height="11in" style:num-format="1"
      style:print-orientation="portrait" fo:margin-top="1in"
      fo:margin-bottom="1in" fo:margin-left="1in"
      fo:margin-right="1in" style:writing-mode="lr-tb"
      style:footnote-max-height="0in">
        <style:footnote-sep style:width="0.0071in"
        style:distance-before-sep="0.0398in"
        style:distance-after-sep="0.0398in" style:line-style="none"
        style:adjustment="left" style:rel-width="25%"
        style:color="#000000" />
      </style:page-layout-properties>
      <style:header-style />
      <style:footer-style>
        <style:header-footer-properties fo:min-height="0.4in"
        fo:margin-left="0in" fo:margin-right="0in"
        fo:margin-top="0.2in" style:dynamic-spacing="false" />
      </style:footer-style>
    </style:page-layout>
  </office:automatic-styles>
  <office:master-styles>
    <style:master-page style:name="Standard"
    style:page-layout-name="Mpm1">
      <style:footer>
        <text:p text:style-name="MP1">
          <text:page-number text:select-page="current">
          1</text:page-number>
        </text:p>
      </style:footer>
    </style:master-page>
  </office:master-styles>
</office:document-styles>
<?xml version="1.0" encoding="UTF-8"?>
<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.3">
 <manifest:file-entry manifest:full-path="/" manifest:version="1.3" manifest:media-type="application/vnd.oasis.opendocument.text"/>
 <manifest:file-entry manifest:full-path="meta.xml" manifest:media-type="text/xml"/>
 <manifest:file-entry manifest:full-path="content.xml" manifest:media-type="text/xml"/>
 <manifest:file-entry manifest:full-path="manifest.rdf" manifest:media-type="application/rdf+xml"/>
 <manifest:file-entry manifest:full-path="styles.xml" manifest:media-type="text/xml"/>
</manifest:manifest>
<!DOCTYPE html>

<meta charset="utf-8">
<title>The Title Of Your Presentation</title>

<!-- Your Slides -->
<!-- One section is one slide -->

<section>
    <!-- This is the first slide -->
    <h1>My Presentation</h1>
    <footer>by John Doe</footer>
</section>

<section>
    <p>Some random text: But I've never been to the moon! You can see how I lived before I met you. Also Zoidberg.
    I could if you hadn't turned on the light and shut off my stereo.</p>
</section>

<section>
    <h3>An incremental list</h3>
    <ul class="incremental">
      <li>Item 1
      <li>Item 2
      <li>Item 3
        <ul class="incremental">
          <li> Item 3.1
          <li> Item 3.2
        </ul>
    </ul>
    <div role="note">Some notes. They are only visible using onstage shell.</div>
</section>

<section>
  <blockquote>
    Who's brave enough to fly into something we all keep calling a death sphere?
  </blockquote>
  <details>
    <p>In the onstage shell, notes scroll rather than overflow:</p>
    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla ac dui eu est feugiat lacinia sit amet nec leo. Mauris eu ipsum leo. Nulla mi odio, cursus sed sollicitudin non, fringilla id magna. Suspendisse sit amet posuere elit. Maecenas iaculis, turpis a placerat imperdiet, libero lorem feugiat nisi, nec tincidunt diam nibh sit amet massa. Vestibulum quis adipiscing tellus. Maecenas sollicitudin sodales pulvinar. Donec dui ipsum, bibendum facilisis consequat interdum, tempus ut mauris. Aliquam ut dolor nec odio scelerisque bibendum quis in neque. Aliquam dui dui, pulvinar quis fermentum quis, gravida eu augue. Nunc tristique dolor a urna pulvinar bibendum. Curabitur mollis cursus neque, in scelerisque metus porta non. Donec tempor enim in nibh vestibulum et convallis nisi malesuada. Duis ut lectus sed metus venenatis porttitor id pharetra quam. Suspendisse sapien turpis, ornare in molestie et, gravida eget turpis.
    </p>
  </details>
</section>

<section>
    <h2>Part two</h2>
</section>

<section>
    <figure> <!-- Figures are used to display images and videos fullpage -->
      <img src="http://placekitten.com/g/800/600">
      <figcaption>An image</figcaption>
    </figure>
    <div role="note">Kittens are so cute!</div>
</section>

<section>
    <figure> <!-- Videos are automatically played -->
      <video src="http://videos-cdn.mozilla.net/brand/Mozilla_Firefox_Manifesto_v0.2_640.webm" poster="http://www.mozilla.org/images/about/poster.jpg"></video>
      <figcaption>A video</figcaption>
    </figure>
</section>

<section>
    <h2>End!</h2>
</section>

<!-- Your Style -->
<!-- Define the style of your presentation -->

<!-- Maybe a font from http://www.google.com/webfonts ? -->
<link href='http://fonts.googleapis.com/css?family=Oswald' rel='stylesheet'>

<style>
  html, .view body { background-color: black; counter-reset: slideidx; }
  body, .view section { background-color: white; border-radius: 12px }
  /* A section is a slide. It's size is 800x600, and this will never change */
  section, .view head > title {
      /* The font from Google */
      font-family: 'Oswald', arial, serif;
      font-size: 30px;
  }

  .view section:after {
    counter-increment: slideidx;
    content: counter(slideidx, decimal-leading-zero);
    position: absolute; bottom: -80px; right: 100px;
    color: white;
  }

  .view head > title {
    color: white;
    text-align: center;
    margin: 1em 0 1em 0;
  }

  h1, h2 {
    margin-top: 200px;
    text-align: center;
    font-size: 80px;
  }
  h3 {
    margin: 100px 0 50px 100px;
  }

  ul {
      margin: 50px 200px;
  }
  li > ul {
      margin: 15px 50px;
  }

  p {
    margin: 75px;
    font-size: 50px;
  }

  blockquote {
    height: 100%;
    background-color: black;
    color: white;
    font-size: 60px;
    padding: 50px;
  }
  blockquote:before {
    content: open-quote;
  }
  blockquote:after {
    content: close-quote;
  }

  /* Figures are displayed full-page, with the caption
     on top of the image/video */
  figure {
    background-color: black;
    width: 100%;
    height: 100%;
  }
  figure > * {
    position: absolute;
  }
  figure > img, figure > video {
    width: 100%; height: 100%;
  }
  figcaption {
    margin: 70px;
    font-size: 50px;
  }

  footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 40px;
    text-align: right;
    background-color: #F3F4F8;
    border-top: 1px solid #CCC;
  }

  /* Transition effect */
  /* Feel free to change the transition effect for original
     animations. See here:
     https://developer.mozilla.org/en/CSS/CSS_transitions
     How to use CSS3 Transitions: */
  section {
    -moz-transition: left 400ms linear 0s;
    -webkit-transition: left 400ms linear 0s;
    -ms-transition: left 400ms linear 0s;
    transition: left 400ms linear 0s;
  }
  .view section {
    -moz-transition: none;
    -webkit-transition: none;
    -ms-transition: none;
    transition: none;
  }

  .view section[aria-selected] {
    border: 5px red solid;
  }

  /* Before */
  section { left: -150%; }
  /* Now */
  section[aria-selected] { left: 0; }
  /* After */
  section[aria-selected] ~ section { left: +150%; }

  /* Incremental elements */

  /* By default, visible */
  .incremental > * { opacity: 1; }

  /* The current item */
  .incremental > *[aria-selected] { opacity: 1; }

  /* The items to-be-selected */
  .incremental > *[aria-selected] ~ * { opacity: 0; }

  /* The progressbar, at the bottom of the slides, show the global
     progress of the presentation. */
  #progress-bar {
    height: 2px;
    background: #AAA;
  }
</style>

<!-- {{{{ dzslides core
#
#
#     __  __  __       .  __   ___  __
#    |  \  / /__` |    | |  \ |__  /__`
#    |__/ /_ .__/ |___ | |__/ |___ .__/ core :€
#
#
# The following block of code is not supposed to be edited.
# But if you want to change the behavior of these slides,
# feel free to hack it!
#
-->

<div id="progress-bar"></div>

<!-- Default Style -->
<style>
  * { margin: 0; padding: 0; -moz-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box; }
  [role="note"] { display: none; }
  body {
    width: 800px; height: 600px;
    margin-left: -400px; margin-top: -300px;
    position: absolute; top: 50%; left: 50%;
    overflow: hidden;
    display: none;
  }
  .view body {
    position: static;
    margin: 0; padding: 0;
    width: 100%; height: 100%;
    display: inline-block;
    overflow: visible; overflow-x: hidden;
    /* undo Dz.onresize */
    transform: none !important;
    -moz-transform: none !important;
    -webkit-transform: none !important;
    -o-transform: none !important;
    -ms-transform: none !important;
  }
  .view head, .view head > title { display: block }
  section {
    position: absolute;
    pointer-events: none;
    width: 100%; height: 100%;
  }
  .view section {
    pointer-events: auto;
    position: static;
    width: 800px; height: 600px;
    margin: -150px -200px;
    float: left;

    transform: scale(.4);
    -moz-transform: scale(.4);
    -webkit-transform: scale(.4);
    -o-transform: scale(.4);
    -ms-transform: scale(.4);
  }
  .view section > * { pointer-events: none; }
  section[aria-selected] { pointer-events: auto; }
  html { overflow: hidden; }
  html.view { overflow: visible; }
  body.loaded { display: block; }
  .incremental {visibility: hidden; }
  .incremental[active] {visibility: visible; }
  #progress-bar{
    bottom: 0;
    position: absolute;
    -moz-transition: width 400ms linear 0s;
    -webkit-transition: width 400ms linear 0s;
    -ms-transition: width 400ms linear 0s;
    transition: width 400ms linear 0s;
  }
  .view #progress-bar {
    display: none;
  }
</style>

<script>
  var Dz = {
    remoteWindows: [],
    idx: -1,
    step: 0,
    html: null,
    slides: null,
    progressBar : null,
    params: {
      autoplay: "1"
    }
  };

  Dz.init = function() {
    document.body.className = "loaded";
    this.slides = Array.prototype.slice.call($$("body > section"));
    this.progressBar = $("#progress-bar");
    this.html = document.body.parentNode;
    this.setupParams();
    this.onhashchange();
    this.setupTouchEvents();
    this.onresize();
    this.setupView();
  }

  Dz.setupParams = function() {
    var p = window.location.search.substr(1).split('&');
    p.forEach(function(e, i, a) {
      var keyVal = e.split('=');
      Dz.params[keyVal[0]] = decodeURIComponent(keyVal[1]);
    });
  // Specific params handling
    if (!+this.params.autoplay)
      $$.forEach($$("video"), function(v){ v.controls = true });
  }

  Dz.onkeydown = function(aEvent) {
    // Don't intercept keyboard shortcuts
    if (aEvent.altKey
      || aEvent.ctrlKey
      || aEvent.metaKey
      || aEvent.shiftKey) {
      return;
    }
    if ( aEvent.keyCode == 37 // left arrow
      || aEvent.keyCode == 38 // up arrow
      || aEvent.keyCode == 33 // page up
    ) {
      aEvent.preventDefault();
      this.back();
    }
    if ( aEvent.keyCode == 39 // right arrow
      || aEvent.keyCode == 40 // down arrow
      || aEvent.keyCode == 34 // page down
    ) {
      aEvent.preventDefault();
      this.forward();
    }
    if (aEvent.keyCode == 35) { // end
      aEvent.preventDefault();
      this.goEnd();
    }
    if (aEvent.keyCode == 36) { // home
      aEvent.preventDefault();
      this.goStart();
    }
    if (aEvent.keyCode == 32) { // space
      aEvent.preventDefault();
      this.toggleContent();
    }
    if (aEvent.keyCode == 70) { // f
      aEvent.preventDefault();
      this.goFullscreen();
    }
    if (aEvent.keyCode == 79) { // o
      aEvent.preventDefault();
      this.toggleView();
    }
  }

  /* Touch Events */

  Dz.setupTouchEvents = function() {
    var orgX, newX;
    var tracking = false;

    var db = document.body;
    db.addEventListener("touchstart", start.bind(this), false);
    db.addEventListener("touchmove", move.bind(this), false);

    function start(aEvent) {
      aEvent.preventDefault();
      tracking = true;
      orgX = aEvent.changedTouches[0].pageX;
    }

    function move(aEvent) {
      if (!tracking) return;
      newX = aEvent.changedTouches[0].pageX;
      if (orgX - newX > 100) {
        tracking = false;
        this.forward();
      } else {
        if (orgX - newX < -100) {
          tracking = false;
          this.back();
        }
      }
    }
  }

  Dz.setupView = function() {
    document.body.addEventListener("click", function ( e ) {
      if (!Dz.html.classList.contains("view")) return;
      if (!e.target || e.target.nodeName != "SECTION") return;

      Dz.html.classList.remove("view");
      Dz.setCursor(Dz.slides.indexOf(e.target) + 1);
    }, false);
  }

  /* Adapt the size of the slides to the window */

  Dz.onresize = function() {
    var db = document.body;
    var sx = db.clientWidth / window.innerWidth;
    var sy = db.clientHeight / window.innerHeight;
    var transform = "scale(" + (1/Math.max(sx, sy)) + ")";

    db.style.MozTransform = transform;
    db.style.WebkitTransform = transform;
    db.style.OTransform = transform;
    db.style.msTransform = transform;
    db.style.transform = transform;
  }


  Dz.getNotes = function(aIdx) {
    var s = $("section:nth-of-type(" + aIdx + ")");
    var d = s.$("[role='note']");
    return d ? d.innerHTML : "";
  }

  Dz.onmessage = function(aEvent) {
    var argv = aEvent.data.split(" "), argc = argv.length;
    argv.forEach(function(e, i, a) { a[i] = decodeURIComponent(e) });
    var win = aEvent.source;
    if (argv[0] === "REGISTER" && argc === 1) {
      this.remoteWindows.push(win);
      this.postMsg(win, "REGISTERED", document.title, this.slides.length);
      this.postMsg(win, "CURSOR", this.idx + "." + this.step);
      return;
    }
    if (argv[0] === "BACK" && argc === 1)
      this.back();
    if (argv[0] === "FORWARD" && argc === 1)
      this.forward();
    if (argv[0] === "START" && argc === 1)
      this.goStart();
    if (argv[0] === "END" && argc === 1)
      this.goEnd();
    if (argv[0] === "TOGGLE_CONTENT" && argc === 1)
      this.toggleContent();
    if (argv[0] === "SET_CURSOR" && argc === 2)
      window.location.hash = "#" + argv[1];
    if (argv[0] === "GET_CURSOR" && argc === 1)
      this.postMsg(win, "CURSOR", this.idx + "." + this.step);
    if (argv[0] === "GET_NOTES" && argc === 1)
      this.postMsg(win, "NOTES", this.getNotes(this.idx));
  }

  Dz.toggleContent = function() {
    // If a Video is present in this new slide, play it.
    // If a Video is present in the previous slide, stop it.
    var s = $("section[aria-selected]");
    if (s) {
      var video = s.$("video");
      if (video) {
        if (video.ended || video.paused) {
          video.play();
        } else {
          video.pause();
        }
      }
    }
  }

  Dz.setCursor = function(aIdx, aStep) {
    // If the user change the slide number in the URL bar, jump
    // to this slide.
    aStep = (aStep != 0 && typeof aStep !== "undefined") ? "." + aStep : ".0";
    window.location.hash = "#" + aIdx + aStep;
  }

  Dz.onhashchange = function() {
    var cursor = window.location.hash.split("#"),
        newidx = 1,
        newstep = 0;
    if (cursor.length == 2) {
      newidx = ~~cursor[1].split(".")[0];
      newstep = ~~cursor[1].split(".")[1];
      if (newstep > Dz.slides[newidx - 1].$$('.incremental > *').length) {
        newstep = 0;
        newidx++;
      }
    }
    this.setProgress(newidx, newstep);
    if (newidx != this.idx) {
      this.setSlide(newidx);
    }
    if (newstep != this.step) {
      this.setIncremental(newstep);
    }
    for (var i = 0; i < this.remoteWindows.length; i++) {
      this.postMsg(this.remoteWindows[i], "CURSOR", this.idx + "." + this.step);
    }
  }

  Dz.back = function() {
    if (this.idx == 1 && this.step == 0) {
      return;
    }
    if (this.step == 0) {
      this.setCursor(this.idx - 1,
                     this.slides[this.idx - 2].$$('.incremental > *').length);
    } else {
      this.setCursor(this.idx, this.step - 1);
    }
  }

  Dz.forward = function() {
    if (this.idx >= this.slides.length &&
        this.step >= this.slides[this.idx - 1].$$('.incremental > *').length) {
        return;
    }
    if (this.step >= this.slides[this.idx - 1].$$('.incremental > *').length) {
      this.setCursor(this.idx + 1, 0);
    } else {
      this.setCursor(this.idx, this.step + 1);
    }
  }

  Dz.goStart = function() {
    this.setCursor(1, 0);
  }

  Dz.goEnd = function() {
    var lastIdx = this.slides.length;
    var lastStep = this.slides[lastIdx - 1].$$('.incremental > *').length;
    this.setCursor(lastIdx, lastStep);
  }

  Dz.toggleView = function() {
    this.html.classList.toggle("view");

    if (this.html.classList.contains("view")) {
      $("section[aria-selected]").scrollIntoView(true);
    }
  }

  Dz.setSlide = function(aIdx) {
    this.idx = aIdx;
    var old = $("section[aria-selected]");
    var next = $("section:nth-of-type("+ this.idx +")");
    if (old) {
      old.removeAttribute("aria-selected");
      var video = old.$("video");
      if (video) {
        video.pause();
      }
    }
    if (next) {
      next.setAttribute("aria-selected", "true");
      if (this.html.classList.contains("view")) {
        next.scrollIntoView();
      }
      var video = next.$("video");
      if (video && !!+this.params.autoplay) {
        video.play();
      }
    } else {
      // That should not happen
      this.idx = -1;
      // console.warn("Slide doesn't exist.");
    }
  }

  Dz.setIncremental = function(aStep) {
    this.step = aStep;
    var old = this.slides[this.idx - 1].$('.incremental > *[aria-selected]');
    if (old) {
      old.removeAttribute('aria-selected');
    }
    var incrementals = $$('.incremental');
    if (this.step <= 0) {
      $$.forEach(incrementals, function(aNode) {
        aNode.removeAttribute('active');
      });
      return;
    }
    var next = this.slides[this.idx - 1].$$('.incremental > *')[this.step - 1];
    if (next) {
      next.setAttribute('aria-selected', true);
      next.parentNode.setAttribute('active', true);
      var found = false;
      $$.forEach(incrementals, function(aNode) {
        if (aNode != next.parentNode)
          if (found)
            aNode.removeAttribute('active');
          else
            aNode.setAttribute('active', true);
        else
          found = true;
      });
    } else {
      setCursor(this.idx, 0);
    }
    return next;
  }

  Dz.goFullscreen = function() {
    var html = $('html'),
        requestFullscreen = html.requestFullscreen || html.requestFullScreen || html.mozRequestFullScreen || html.webkitRequestFullScreen;
    if (requestFullscreen) {
      requestFullscreen.apply(html);
    }
  }
  
  Dz.setProgress = function(aIdx, aStep) {
    var slide = $("section:nth-of-type("+ aIdx +")");
    if (!slide)
      return;
    var steps = slide.$$('.incremental > *').length + 1,
        slideSize = 100 / (this.slides.length - 1),
        stepSize = slideSize / steps;
    this.progressBar.style.width = ((aIdx - 1) * slideSize + aStep * stepSize) + '%';
  }
  
  Dz.postMsg = function(aWin, aMsg) { // [arg0, [arg1...]]
    aMsg = [aMsg];
    for (var i = 2; i < arguments.length; i++)
      aMsg.push(encodeURIComponent(arguments[i]));
    aWin.postMessage(aMsg.join(" "), "*");
  }
  
  function init() {
    Dz.init();
    window.onkeydown = Dz.onkeydown.bind(Dz);
    window.onresize = Dz.onresize.bind(Dz);
    window.onhashchange = Dz.onhashchange.bind(Dz);
    window.onmessage = Dz.onmessage.bind(Dz);
  }

  window.onload = init;
</script>


<script> // Helpers
  if (!Function.prototype.bind) {
    Function.prototype.bind = function (oThis) {

      // closest thing possible to the ECMAScript 5 internal IsCallable
      // function 
      if (typeof this !== "function")
      throw new TypeError(
        "Function.prototype.bind - what is trying to be fBound is not callable"
      );

      var aArgs = Array.prototype.slice.call(arguments, 1),
          fToBind = this,
          fNOP = function () {},
          fBound = function () {
            return fToBind.apply( this instanceof fNOP ? this : oThis || window,
                   aArgs.concat(Array.prototype.slice.call(arguments)));
          };

      fNOP.prototype = this.prototype;
      fBound.prototype = new fNOP();

      return fBound;
    };
  }

  var $ = (HTMLElement.prototype.$ = function(aQuery) {
    return this.querySelector(aQuery);
  }).bind(document);

  var $$ = (HTMLElement.prototype.$$ = function(aQuery) {
    return this.querySelectorAll(aQuery);
  }).bind(document);

  $$.forEach = function(nodeList, fun) {
    Array.prototype.forEach.call(nodeList, fun);
  }

</script>
<!-- vim: set fdm=marker: }}} -->
<?xml version="1.0" encoding="utf-8"?>
<style xmlns="http://purl.org/net/xbiblio/csl" class="in-text" version="1.0" demote-non-dropping-particle="display-and-sort" page-range-format="chicago">
  <info>
    <title>Chicago Manual of Style 17th edition (author-date)</title>
    <id>http://www.zotero.org/styles/chicago-author-date</id>
    <link href="http://www.zotero.org/styles/chicago-author-date" rel="self"/>
    <link href="http://www.chicagomanualofstyle.org/tools_citationguide.html" rel="documentation"/>
    <author>
      <name>Julian Onions</name>
      <email><EMAIL></email>
    </author>
    <contributor>
      <name>Sebastian Karcher</name>
    </contributor>
    <contributor>
      <name>Richard Karnesky</name>
      <email><EMAIL></email>
      <uri>http://arc.nucapt.northwestern.edu/Richard_Karnesky</uri>
    </contributor>
    <contributor>
      <name>Andrew Dunning</name>
      <email><EMAIL></email>
      <uri>https://orcid.org/0000-0003-0464-5036</uri>
    </contributor>
    <contributor>
      <name>Matthew Roth</name>
      <email><EMAIL></email>
      <uri> https://orcid.org/0000-0001-7902-6331</uri>
    </contributor>
    <contributor>
      <name>Brenton M. Wiernik</name>
    </contributor>
    <category citation-format="author-date"/>
    <category field="generic-base"/>
    <summary>The author-date variant of the Chicago style</summary>
    <updated>2018-01-24T12:00:00+00:00</updated>
    <rights license="http://creativecommons.org/licenses/by-sa/3.0/">This work is licensed under a Creative Commons Attribution-ShareAlike 3.0 License</rights>
  </info>
  <locale xml:lang="en">
    <terms>
      <term name="editor" form="verb-short">ed.</term>
      <term name="container-author" form="verb">by</term>
      <term name="translator" form="verb-short">trans.</term>
      <term name="editortranslator" form="verb">edited and translated by</term>
      <term name="translator" form="short">trans.</term>
    </terms>
  </locale>
  <locale xml:lang="pt-PT">
    <terms>
      <term name="accessed">acedido a</term>
    </terms>
  </locale>
  <locale xml:lang="pt">
    <terms>
      <term name="editor" form="verb">editado por</term>
      <term name="editor" form="verb-short">ed.</term>
      <term name="container-author" form="verb">por</term>
      <term name="translator" form="verb-short">traduzido por</term>
      <term name="translator" form="short">trad.</term>
      <term name="editortranslator" form="verb">editado e traduzido por</term>
      <term name="and">e</term>
      <term name="no date" form="long">s.d</term>
      <term name="no date" form="short">s.d.</term>
      <term name="in">em</term>
      <term name="at">em</term>
      <term name="by">por</term>
    </terms>
  </locale>
  <macro name="secondary-contributors">
    <choose>
      <if type="chapter entry-dictionary entry-encyclopedia paper-conference" match="none">
        <group delimiter=". ">
          <names variable="editor translator" delimiter=". ">
            <label form="verb" text-case="capitalize-first" suffix=" "/>
            <name and="text" delimiter=", "/>
          </names>
          <names variable="director" delimiter=". ">
            <label form="verb" text-case="capitalize-first" suffix=" "/>
            <name and="text" delimiter=", "/>
          </names>
        </group>
      </if>
    </choose>
  </macro>
  <macro name="container-contributors">
    <choose>
      <if type="chapter entry-dictionary entry-encyclopedia paper-conference" match="any">
        <group prefix=", " delimiter=", ">
          <names variable="container-author" delimiter=", ">
            <label form="verb" suffix=" "/>
            <name and="text" delimiter=", "/>
          </names>
          <names variable="editor translator" delimiter=", ">
            <label form="verb" suffix=" "/>
            <name and="text" delimiter=", "/>
          </names>
        </group>
      </if>
    </choose>
  </macro>
  <macro name="editor">
    <names variable="editor">
      <name name-as-sort-order="first" and="text" sort-separator=", " delimiter=", " delimiter-precedes-last="always"/>
      <label form="short" prefix=", "/>
    </names>
  </macro>
  <macro name="translator">
    <names variable="translator">
      <name name-as-sort-order="first" and="text" sort-separator=", " delimiter=", " delimiter-precedes-last="always"/>
      <label form="short" prefix=", "/>
    </names>
  </macro>
  <macro name="recipient">
    <choose>
      <if type="personal_communication">
        <choose>
          <if variable="genre">
            <text variable="genre" text-case="capitalize-first"/>
          </if>
          <else>
            <text term="letter" text-case="capitalize-first"/>
          </else>
        </choose>
      </if>
    </choose>
    <names variable="recipient" delimiter=", ">
      <label form="verb" prefix=" " text-case="lowercase" suffix=" "/>
      <name and="text" delimiter=", "/>
    </names>
  </macro>
  <macro name="substitute-title">
    <choose>
      <if type="article-magazine article-newspaper review review-book" match="any">
        <text macro="container-title"/>
      </if>
    </choose>
  </macro>
  <macro name="contributors">
    <group delimiter=". ">
      <names variable="author">
        <name and="text" name-as-sort-order="first" sort-separator=", " delimiter=", " delimiter-precedes-last="always"/>
        <label form="short" prefix=", "/>
        <substitute>
          <names variable="editor"/>
          <names variable="translator"/>
          <names variable="director"/>
          <text macro="substitute-title"/>
          <text macro="title"/>
        </substitute>
      </names>
      <text macro="recipient"/>
    </group>
  </macro>
  <macro name="contributors-short">
    <names variable="author">
      <name form="short" and="text" delimiter=", " initialize-with=". "/>
      <substitute>
        <names variable="editor"/>
        <names variable="translator"/>
        <names variable="director"/>
        <text macro="substitute-title"/>
        <text macro="title"/>
      </substitute>
    </names>
  </macro>
  <macro name="interviewer">
    <names variable="interviewer" delimiter=", ">
      <label form="verb" prefix=" " text-case="capitalize-first" suffix=" "/>
      <name and="text" delimiter=", "/>
    </names>
  </macro>
  <macro name="archive">
    <group delimiter=". ">
      <text variable="archive_location" text-case="capitalize-first"/>
      <text variable="archive"/>
      <text variable="archive-place"/>
    </group>
  </macro>
  <macro name="access">
    <group delimiter=". ">
      <choose>
        <if type="graphic report" match="any">
          <text macro="archive"/>
        </if>
        <else-if type="article-journal bill book chapter legal_case legislation motion_picture paper-conference" match="none">
          <text macro="archive"/>
        </else-if>
      </choose>
      <choose>
        <if type="webpage post-weblog" match="any">
          <date variable="issued" form="text"/>
        </if>
      </choose>
      <choose>
        <if variable="issued" match="none">
          <group delimiter=" ">
            <text term="accessed" text-case="capitalize-first"/>
            <date variable="accessed" form="text"/>
          </group>
        </if>
      </choose>
      <choose>
        <if type="legal_case" match="none">
          <choose>
            <if variable="DOI">
              <text variable="DOI" prefix="https://doi.org/"/>
            </if>
            <else>
              <text variable="URL"/>
            </else>
          </choose>
        </if>
      </choose>
    </group>
  </macro>
  <macro name="title">
    <choose>
      <if variable="title" match="none">
        <choose>
          <if type="personal_communication speech thesis" match="none">
            <text variable="genre" text-case="capitalize-first"/>
          </if>
        </choose>
      </if>
      <else-if type="bill book graphic legislation motion_picture song" match="any">
        <text variable="title" text-case="title" font-style="italic"/>
        <group prefix=" (" suffix=")" delimiter=" ">
          <text term="version"/>
          <text variable="version"/>
        </group>
      </else-if>
      <else-if variable="reviewed-author">
        <choose>
          <if variable="reviewed-title">
            <group delimiter=". ">
              <text variable="title" text-case="title" quotes="true"/>
              <group delimiter=", ">
                <text variable="reviewed-title" text-case="title" font-style="italic" prefix="Review of "/>
                <names variable="reviewed-author">
                  <label form="verb-short" text-case="lowercase" suffix=" "/>
                  <name and="text" delimiter=", "/>
                </names>
              </group>
            </group>
          </if>
          <else>
            <group delimiter=", ">
              <text variable="title" text-case="title" font-style="italic" prefix="Review of "/>
              <names variable="reviewed-author">
                <label form="verb-short" text-case="lowercase" suffix=" "/>
                <name and="text" delimiter=", "/>
              </names>
            </group>
          </else>
        </choose>
      </else-if>
      <else-if type="legal_case interview patent" match="any">
        <text variable="title"/>
      </else-if>
      <else>
        <text variable="title" text-case="title" quotes="true"/>
      </else>
    </choose>
  </macro>
  <macro name="edition">
    <choose>
      <if type="bill book graphic legal_case legislation motion_picture report song" match="any">
        <choose>
          <if is-numeric="edition">
            <group delimiter=" " prefix=". ">
              <number variable="edition" form="ordinal"/>
              <text term="edition" form="short" strip-periods="true"/>
            </group>
          </if>
          <else>
            <text variable="edition" text-case="capitalize-first" prefix=". "/>
          </else>
        </choose>
      </if>
      <else-if type="chapter entry-dictionary entry-encyclopedia paper-conference" match="any">
        <choose>
          <if is-numeric="edition">
            <group delimiter=" " prefix=", ">
              <number variable="edition" form="ordinal"/>
              <text term="edition" form="short"/>
            </group>
          </if>
          <else>
            <text variable="edition" prefix=", "/>
          </else>
        </choose>
      </else-if>
    </choose>
  </macro>
  <macro name="locators">
    <choose>
      <if type="article-journal">
        <choose>
          <if variable="volume">
            <text variable="volume" prefix=" "/>
            <group prefix=" (" suffix=")">
              <choose>
                <if variable="issue">
                  <text variable="issue"/>
                </if>
                <else>
                  <date variable="issued">
                    <date-part name="month"/>
                  </date>
                </else>
              </choose>
            </group>
          </if>
          <else-if variable="issue">
            <group delimiter=" " prefix=", ">
              <text term="issue" form="short"/>
              <text variable="issue"/>
              <date variable="issued" prefix="(" suffix=")">
                <date-part name="month"/>
              </date>
            </group>
          </else-if>
          <else>
            <date variable="issued" prefix=", ">
              <date-part name="month"/>
            </date>
          </else>
        </choose>
      </if>
      <else-if type="legal_case">
        <text variable="volume" prefix=", "/>
        <text variable="container-title" prefix=" "/>
        <text variable="page" prefix=" "/>
      </else-if>
      <else-if type="bill book graphic legal_case legislation motion_picture report song" match="any">
        <group prefix=". " delimiter=". ">
          <group>
            <text term="volume" form="short" text-case="capitalize-first" suffix=" "/>
            <number variable="volume" form="numeric"/>
          </group>
          <group>
            <number variable="number-of-volumes" form="numeric"/>
            <text term="volume" form="short" prefix=" " plural="true"/>
          </group>
        </group>
      </else-if>
      <else-if type="chapter entry-dictionary entry-encyclopedia paper-conference" match="any">
        <choose>
          <if variable="page" match="none">
            <group prefix=". ">
              <text term="volume" form="short" text-case="capitalize-first" suffix=" "/>
              <number variable="volume" form="numeric"/>
            </group>
          </if>
        </choose>
      </else-if>
    </choose>
  </macro>
  <macro name="locators-chapter">
    <choose>
      <if type="chapter entry-dictionary entry-encyclopedia paper-conference" match="any">
        <choose>
          <if variable="page">
            <group prefix=", ">
              <text variable="volume" suffix=":"/>
              <text variable="page"/>
            </group>
          </if>
        </choose>
      </if>
    </choose>
  </macro>
  <macro name="locators-article">
    <choose>
      <if type="article-newspaper">
        <group prefix=", " delimiter=", ">
          <group delimiter=" ">
            <text variable="edition"/>
            <text term="edition"/>
          </group>
          <group>
            <text term="section" form="short" suffix=" "/>
            <text variable="section"/>
          </group>
        </group>
      </if>
      <else-if type="article-journal">
        <choose>
          <if variable="volume issue" match="any">
            <text variable="page" prefix=": "/>
          </if>
          <else>
            <text variable="page" prefix=", "/>
          </else>
        </choose>
      </else-if>
    </choose>
  </macro>
  <macro name="point-locators">
    <choose>
      <if variable="locator">
        <choose>
          <if locator="page" match="none">
            <choose>
              <if type="bill book graphic legal_case legislation motion_picture report song" match="any">
                <choose>
                  <if variable="volume">
                    <group>
                      <text term="volume" form="short" suffix=" "/>
                      <number variable="volume" form="numeric"/>
                      <label variable="locator" form="short" prefix=", " suffix=" "/>
                    </group>
                  </if>
                  <else>
                    <label variable="locator" form="short" suffix=" "/>
                  </else>
                </choose>
              </if>
              <else>
                <label variable="locator" form="short" suffix=" "/>
              </else>
            </choose>
          </if>
          <else-if type="bill book graphic legal_case legislation motion_picture report song" match="any">
            <number variable="volume" form="numeric" suffix=":"/>
          </else-if>
        </choose>
        <text variable="locator"/>
      </if>
    </choose>
  </macro>
  <macro name="container-prefix">
    <text term="in" text-case="capitalize-first"/>
  </macro>
  <macro name="container-title">
    <choose>
      <if type="chapter entry-dictionary entry-encyclopedia paper-conference" match="any">
        <text macro="container-prefix" suffix=" "/>
      </if>
    </choose>
    <choose>
      <if type="webpage">
        <text variable="container-title" text-case="title"/>
      </if>
      <else-if type="legal_case" match="none">
        <group delimiter=" ">
          <text variable="container-title" text-case="title" font-style="italic"/>
          <choose>
            <if type="post-weblog">
              <text value="(blog)"/>
            </if>
          </choose>
        </group>
      </else-if>
    </choose>
  </macro>
  <macro name="publisher">
    <group delimiter=": ">
      <text variable="publisher-place"/>
      <text variable="publisher"/>
    </group>
  </macro>
  <macro name="date">
    <choose>
      <if variable="issued">
        <group delimiter=" ">
          <date variable="original-date" form="text" date-parts="year" prefix="(" suffix=")"/>
          <date variable="issued">
            <date-part name="year"/>
          </date>
        </group>
      </if>
      <else-if variable="status">
        <text variable="status" text-case="capitalize-first"/>
      </else-if>
      <else>
        <text term="no date" form="short"/>
      </else>
    </choose>
  </macro>
  <macro name="date-in-text">
    <choose>
      <if variable="issued">
        <group delimiter=" ">
          <date variable="original-date" form="text" date-parts="year" prefix="[" suffix="]"/>
          <date variable="issued">
            <date-part name="year"/>
          </date>
        </group>
      </if>
      <else-if variable="status">
        <text variable="status"/>
      </else-if>
      <else>
        <text term="no date" form="short"/>
      </else>
    </choose>
  </macro>
  <macro name="day-month">
    <date variable="issued">
      <date-part name="month"/>
      <date-part name="day" prefix=" "/>
    </date>
  </macro>
  <macro name="collection-title">
    <choose>
      <if match="none" type="article-journal">
        <choose>
          <if match="none" is-numeric="collection-number">
            <group delimiter=", ">
              <text variable="collection-title" text-case="title"/>
              <text variable="collection-number"/>
            </group>
          </if>
          <else>
            <group delimiter=" ">
              <text variable="collection-title" text-case="title"/>
              <text variable="collection-number"/>
            </group>
          </else>
        </choose>
      </if>
    </choose>
  </macro>
  <macro name="collection-title-journal">
    <choose>
      <if type="article-journal">
        <group delimiter=" ">
          <text variable="collection-title"/>
          <text variable="collection-number"/>
        </group>
      </if>
    </choose>
  </macro>
  <macro name="event">
    <group delimiter=" ">
      <choose>
        <if variable="genre">
          <text term="presented at"/>
        </if>
        <else>
          <text term="presented at" text-case="capitalize-first"/>
        </else>
      </choose>
      <text variable="event"/>
    </group>
  </macro>
  <macro name="description">
    <choose>
      <if variable="interviewer" type="interview" match="any">
        <group delimiter=". ">
          <text macro="interviewer"/>
          <text variable="medium" text-case="capitalize-first"/>
        </group>
      </if>
      <else-if type="patent">
        <group delimiter=" " prefix=". ">
          <text variable="authority"/>
          <text variable="number"/>
        </group>
      </else-if>
      <else>
        <text variable="medium" text-case="capitalize-first" prefix=". "/>
      </else>
    </choose>
    <choose>
      <if variable="title" match="none"/>
      <else-if type="thesis personal_communication speech" match="any"/>
      <else>
        <group delimiter=" " prefix=". ">
          <text variable="genre" text-case="capitalize-first"/>
          <choose>
            <if type="report">
              <text variable="number"/>
            </if>
          </choose>
        </group>
      </else>
    </choose>
  </macro>
  <macro name="issue">
    <choose>
      <if type="legal_case">
        <text variable="authority" prefix=". "/>
      </if>
      <else-if type="speech">
        <group prefix=". " delimiter=", ">
          <group delimiter=" ">
            <text variable="genre" text-case="capitalize-first"/>
            <text macro="event"/>
          </group>
          <text variable="event-place"/>
          <text macro="day-month"/>
        </group>
      </else-if>
      <else-if type="article-newspaper article-magazine personal_communication" match="any">
        <date variable="issued" form="text" prefix=", "/>
      </else-if>
      <else-if type="patent">
        <group delimiter=", " prefix=", ">
          <group delimiter=" ">
            <!--Needs Localization-->
            <text value="filed"/>
            <date variable="submitted" form="text"/>
          </group>
          <group delimiter=" ">
            <choose>
              <if variable="issued submitted" match="all">
                <text term="and"/>
              </if>
            </choose>
            <!--Needs Localization-->
            <text value="issued"/>
            <date variable="issued" form="text"/>
          </group>
        </group>
      </else-if>
      <else-if type="article-journal" match="any"/>
      <else>
        <group prefix=". " delimiter=", ">
          <choose>
            <if type="thesis">
              <text variable="genre" text-case="capitalize-first"/>
            </if>
          </choose>
          <text macro="publisher"/>
        </group>
      </else>
    </choose>
  </macro>
  <citation et-al-min="4" et-al-use-first="1" disambiguate-add-year-suffix="true" disambiguate-add-names="true" disambiguate-add-givenname="true" givenname-disambiguation-rule="primary-name" collapse="year" after-collapse-delimiter="; ">
    <layout prefix="(" suffix=")" delimiter="; ">
      <group delimiter=", ">
        <choose>
          <if variable="issued accessed" match="any">
            <group delimiter=" ">
              <text macro="contributors-short"/>
              <text macro="date-in-text"/>
            </group>
          </if>
          <!---comma before forthcoming and n.d.-->
          <else>
            <group delimiter=", ">
              <text macro="contributors-short"/>
              <text macro="date-in-text"/>
            </group>
          </else>
        </choose>
        <text macro="point-locators"/>
      </group>
    </layout>
  </citation>
  <bibliography hanging-indent="true" et-al-min="11" et-al-use-first="7" subsequent-author-substitute="&#8212;&#8212;&#8212;" entry-spacing="0">
    <sort>
      <key macro="contributors"/>
      <key variable="issued"/>
      <key variable="title"/>
    </sort>
    <layout suffix=".">
      <group delimiter=". ">
        <text macro="contributors"/>
        <text macro="date"/>
        <text macro="title"/>
      </group>
      <text macro="description"/>
      <text macro="secondary-contributors" prefix=". "/>
      <text macro="container-title" prefix=". "/>
      <text macro="container-contributors"/>
      <text macro="edition"/>
      <text macro="locators-chapter"/>
      <text macro="collection-title-journal" prefix=", " suffix=", "/>
      <text macro="locators"/>
      <text macro="collection-title" prefix=". "/>
      <text macro="issue"/>
      <text macro="locators-article"/>
      <text macro="access" prefix=". "/>
    </layout>
  </bibliography>
</style>
aet.
aetat.
al.
Apr.
Aug.
bk.
Bros.
c.
Capt.
cf.
ch.
chap.
chs.
Co.
col.
Corp.
cp.
d.
Dec.
Dr.
e.g.
ed.
eds.
esp.
f.
fasc.
Feb.
ff.
fig.
fl.
fol.
fols.
Fr.
Gen.
Gov.
Hon.
i.e.
ill.
Inc.
incl.
Jan.
Jr.
Jul.
Jun.
Ltd.
M.A.
M.D.
Mar.
Mr.
Mrs.
Ms.
n.
n.b.
nn.
No.
Nov.
Oct.
p.
Ph.D.
pp.
Pres.
Prof.
pt.
q.v.
Rep.
Rev.
s.v.
s.vv.
saec.
sec.
Sen.
Sep.
Sept.
Sgt.
Sr.
St.
univ.
viz.
vol.
vs.
aacgr 03AC
Aacgr 0386
aacute 00E1
Aacute 00C1
abreve 0103
Abreve 0102
ac 223E
acd 223F
acE 223E  0333
acirc 00E2
Acirc 00C2
acute 00B4
acy 0430
Acy 0410
aelig 00E6
AElig 00C6
af 2061
afr 1D51E
Afr 1D504
agr 03B1
Agr 0391
agrave 00E0
Agrave 00C0
alefsym 2135
aleph 2135
alpha 03B1
Alpha 0391
amacr 0101
Amacr 0100
amalg 2A3F
amp 0026
AMP 0026
and 2227
And 2A53
andand 2A55
andd 2A5C
andslope 2A58
andv 2A5A
ang 2220
ange 29A4
angle 2220
angmsd 2221
angmsdaa 29A8
angmsdab 29A9
angmsdac 29AA
angmsdad 29AB
angmsdae 29AC
angmsdaf 29AD
angmsdag 29AE
angmsdah 29AF
angrt 221F
angrtvb 22BE
angrtvbd 299D
angsph 2222
angst 00C5
angzarr 237C
aogon 0105
Aogon 0104
aopf 1D552
Aopf 1D538
ap 2248
apacir 2A6F
ape 224A
apE 2A70
apid 224B
apos 0027
ApplyFunction 2061
approx 2248
approxeq 224A
aring 00E5
Aring 00C5
ascr 1D4B6
Ascr 1D49C
Assign 2254
ast 002A
asymp 2248
asympeq 224D
atilde 00E3
Atilde 00C3
auml 00E4
Auml 00C4
awconint 2233
awint 2A11
b.alpha 1D6C2
b.beta 1D6C3
b.chi 1D6D8
b.delta 1D6C5
b.Delta 1D6AB
b.epsi 1D6C6
b.epsiv 1D6DC
b.eta 1D6C8
b.gamma 1D6C4
b.Gamma 1D6AA
b.gammad 1D7CB
b.Gammad 1D7CA
b.iota 1D6CA
b.kappa 1D6CB
b.kappav 1D6DE
b.lambda 1D6CC
b.Lambda 1D6B2
b.mu 1D6CD
b.nu 1D6CE
b.omega 1D6DA
b.Omega 1D6C0
b.phi 1D6D7
b.Phi 1D6BD
b.phiv 1D6DF
b.pi 1D6D1
b.Pi 1D6B7
b.piv 1D6E1
b.psi 1D6D9
b.Psi 1D6BF
b.rho 1D6D2
b.rhov 1D6E0
b.sigma 1D6D4
b.Sigma 1D6BA
b.sigmav 1D6D3
b.tau 1D6D5
b.Theta 1D6AF
b.thetas 1D6C9
b.thetav 1D6DD
b.upsi 1D6D6
b.UpsiUpsilon
b.xi 1D6CF
b.Xi 1D6B5
b.zeta 1D6C7
backcong 224C
backepsilon 03F6
backprime 2035
backsim 223D
backsimeq 22CD
Backslash 2216
Barv 2AE7
barvee 22BD
barwed 2305
Barwed 2306
barwedge 2305
bbrk 23B5
bbrktbrk 23B6
bcong 224C
bcy 0431
Bcy 0411
bdquo 201E
becaus 2235
because 2235
Because 2235
bemptyv 29B0
bepsi 03F6
bernou 212C
Bernoullis 212C
beta 03B2
Beta 0392
beth 2136
between 226C
bfr 1D51F
Bfr 1D505
bgr 03B2
Bgr 0392
bigcap 22C2
bigcirc 25EF
bigcup 22C3
bigodot 2A00
bigoplus 2A01
bigotimes 2A02
bigsqcup 2A06
bigstarUB starf
bigtriangledown 25BD
bigtriangleup 25B3
biguplus 2A04
bigvee 22C1
bigwedge 22C0
bkarow 290D
blacklozengeUB lozf
blacksquare 25AA
blacktriangleUB utrif
blacktriangledownUB dtrif
blacktriangleleftUB ltrif
blacktrianglerightUB rtrif
blank 2423
blk12 2592
blk14 2591
blk34 2593
block 2588
bne 003D  20E5
bnequiv 2261  20E5
bnot 2310
bNot 2AED
bopf 1D553
Bopf 1D539
bot 22A5
bottom 22A5
bowtie 22C8
boxbox 29C9
boxdl 2510
boxdL 2555
boxDl 2556
boxDL 2557
boxdr 250C
boxdR 2552
boxDr 2553
boxDR 2554
boxh 2500
boxH 2550
boxhd 252C
boxhD 2565
boxHd 2564
boxHD 2566
boxhu 2534
boxhU 2568
boxHu 2567
boxHU 2569
boxminus 229F
boxplus 229E
boxtimes 22A0
boxul 2518
boxuL 255B
boxUl 255C
boxUL 255D
boxur 2514
boxuR 2558
boxUr 2559
boxUR 255A
boxv 2502
boxV 2551
boxvh 253C
boxvH 256A
boxVh 256B
boxVH 256C
boxvl 2524
boxvL 2561
boxVl 2562
boxVL 2563
boxvr 251C
boxvR 255E
boxVr 255F
boxVR 2560
bprime 2035
breve 02D8
Breve 02D8
brvbar 00A6
bscr 1D4B7
Bscr 212C
bsemi 204F
bsim 223D
bsime 22CD
bsol 005C
bsolb 29C5
bsolhsub 27C8
bull 2022
bulletUB bull
bump 224E
bumpe 224F
bumpE 2AAE
bumpeq 224F
Bumpeq 224E
cacute 0107
Cacute 0106
cap 2229
Cap 22D2
capand 2A44
capbrcup 2A49
capcap 2A4B
capcup 2A47
capdot 2A40
CapitalDifferentialD 2145
caps 2229  FE00
caret 2041
caron 02C7
Cayleys 212D
ccaps 2A4D
ccaron 010D
Ccaron 010C
ccedil 00E7
Ccedil 00C7
ccirc 0109
Ccirc 0108
Cconint 2230
ccups 2A4C
ccupssm 2A50
cdot 010B
Cdot 010A
cedil 00B8
Cedilla 00B8
cemptyv 29B2
cent 00A2
centerdotUM middot
CenterDotUM middot
cfr 1D520
Cfr 212D
chcy 0447
CHcy 0427
check 2713
checkmarkUB check
chi 03C7
Chi 03A7
cir 25CB
circ 02C6
circeq 2257
circlearrowleft 21BA
circlearrowright 21BB
circledast 229B
circledcirc 229A
circleddash 229D
CircleDot 2299
circledRUM reg
circledS 24C8
CircleMinus 2296
CirclePlus 2295
CircleTimes 2297
cire 2257
cirE 29C3
cirfnint 2A10
cirmid 2AEF
cirscir 29C2
ClockwiseContourIntegral 2232
CloseCurlyDoubleQuoteUM rdquo
CloseCurlyQuoteUM rsquo
clubs 2663
clubsuitUB clubs
colon 003A
Colon 2237
colone 2254
Colone 2A74
coloneq 2254
comma 002C
commat 0040
comp 2201
compfn 2218
complement 2201
complexes 2102
cong 2245
congdot 2A6D
Congruent 2261
conint 222E
Conint 222F
ContourIntegral 222E
copf 1D554
Copf 2102
coprod 2210
Coproduct 2210
copy 00A9
COPY 00A9
copysr 2117
CounterClockwiseContourIntegral 2233
crarr 21B5
cross 2717
Cross 2A2F
cscr 1D4B8
Cscr 1D49E
csub 2ACF
csube 2AD1
csup 2AD0
csupe 2AD2
ctdot 22EF
cudarrl 2938
cudarrr 2935
cuepr 22DE
cuesc 22DF
cularr 21B6
cularrp 293D
cup 222A
Cup 22D3
cupbrcap 2A48
cupcap 2A46
CupCap 224D
cupcup 2A4A
cupdot 228D
cupor 2A45
cups 222A  FE00
curarr 21B7
curarrm 293C
curlyeqprec 22DE
curlyeqsucc 22DF
curlyvee 22CE
curlywedge 22CF
curren 00A4
curvearrowleft 21B6
curvearrowright 21B7
cuvee 22CE
cuwed 22CF
cwconint 2232
cwint 2231
cylcty 232D
dagger 2020
Dagger 2021
daleth 2138
darr 2193
dArr 21D3
Darr 21A1
dash 2010
dashv 22A3
Dashv 2AE4
dbkarow 290F
dblac 02DD
dcaron 010F
Dcaron 010E
dcy 0434
Dcy 0414
dd 2146
DD 2145
ddaggerUB Dagger
ddarr 21CA
DDotrahd 2911
ddotseq 2A77
deg 00B0
Del 2207
delta 03B4
Delta 0394
demptyv 29B1
dfisht 297F
dfr 1D521
Dfr 1D507
dgr 03B4
Dgr 0394
dHar 2965
dharl 21C3
dharr 21C2
DiacriticalAcute 00B4
DiacriticalDot 02D9
DiacriticalDoubleAcute 02DD
DiacriticalGrave 0060
DiacriticalTilde 02DC
diam 22C4
diamond 22C4
Diamond 22C4
diamondsuitUB diams
diams 2666
die 00A8
DifferentialD 2146
digamma 03DD
disin 22F2
divUM divide
divide 00F7
divideontimes 22C7
divonx 22C7
djcy 0452
DJcy 0402
dlcorn 231E
dlcrop 230D
dollar 0024
dopf 1D555
Dopf 1D53B
dot 02D9
Dot 00A8
DotDot 20DC
doteq 2250
doteqdot 2251
DotEqual 2250
dotminus 2238
dotplus 2214
dotsquare 22A1
doublebarwedge 2306
DoubleContourIntegral 222F
DoubleDot 00A8
DoubleDownArrow 21D3
DoubleLeftArrow 21D0
DoubleLeftRightArrow 21D4
DoubleLeftTee 2AE4
DoubleLongLeftArrow 27F8
DoubleLongLeftRightArrow 27FA
DoubleLongRightArrow 27F9
DoubleRightArrow 21D2
DoubleRightTee 22A8
DoubleUpArrow 21D1
DoubleUpDownArrow 21D5
DoubleVerticalBar 2225
downarrowUM darr
Downarrow 21D3
DownArrowUM darr
DownArrowBar 2913
DownArrowUpArrow 21F5
DownBreve 0311
downdownarrows 21CA
downharpoonleft 21C3
downharpoonright 21C2
DownLeftRightVector 2950
DownLeftTeeVector 295E
DownLeftVector 21BD
DownLeftVectorBar 2956
DownRightTeeVector 295F
DownRightVector 21C1
DownRightVectorBar 2957
DownTee 22A4
DownTeeArrow 21A7
drbkarow 2910
drcorn 231F
drcrop 230C
dscr 1D4B9
Dscr 1D49F
dscy 0455
DScy 0405
dsol 29F6
dstrok 0111
Dstrok 0110
dtdot 22F1
dtri 25BF
dtrif 25BE
duarr 21F5
duhar 296F
dwangle 29A6
dzcy 045F
DZcy 040F
dzigrarr 27FF
eacgr 03AD
Eacgr 0388
eacute 00E9
Eacute 00C9
easter 2A6E
ecaron 011B
Ecaron 011A
ecir 2256
ecirc 00EA
Ecirc 00CA
ecolon 2255
ecy 044D
Ecy 042D
eDDot 2A77
edot 0117
eDot 2251
Edot 0116
ee 2147
eeacgr 03AE
EEacgr 0389
eegr 03B7
EEgr 0397
efDot 2252
efr 1D522
Efr 1D508
eg 2A9A
egr 03B5
Egr 0395
egrave 00E8
Egrave 00C8
egs 2A96
egsdot 2A98
el 2A99
Element 2208
elinters 23E7
ell 2113
els 2A95
elsdot 2A97
emacr 0113
Emacr 0112
empty 2205
emptyset 2205
EmptySmallSquare 25FB
emptyv 2205
EmptyVerySmallSquare 25AB
emsp 2003
emsp13 2004
emsp14 2005
eng 014B
ENG 014A
ensp 2002
eogon 0119
Eogon 0118
eopf 1D556
Eopf 1D53C
epar 22D5
eparsl 29E3
eplus 2A71
epsi 03B5
epsilon 03B5
Epsilon 0395
epsiv 03F5
eqcirc 2256
eqcolon 2255
eqsim 2242
eqslantgtr 2A96
eqslantless 2A95
Equal 2A75
equals 003D
EqualTilde 2242
equest 225F
Equilibrium 21CC
equiv 2261
equivDD 2A78
eqvparsl 29E5
erarr 2971
erDot 2253
escr 212F
Escr 2130
esdot 2250
esim 2242
Esim 2A73
eta 03B7
Eta 0397
eth 00F0
ETH 00D0
euml 00EB
Euml 00CB
euro 20AC
excl 0021
exist 2203
Exists 2203
expectation 2130
exponentiale 2147
ExponentialE 2147
fallingdotseq 2252
fcy 0444
Fcy 0424
female 2640
ffilig FB03
fflig FB00
ffllig FB04
ffr 1D523
Ffr 1D509
filig FB01
FilledSmallSquare 25FC
FilledVerySmallSquare 25AA
fjlig 0066  006A
flat 266D
fllig FB02
fltns 25B1
fnof 0192
fopf 1D557
Fopf 1D53D
forall 2200
ForAll 2200
fork 22D4
forkv 2AD9
Fouriertrf 2131
fpartint 2A0D
frac12 00BD
frac13 2153
frac14 00BC
frac15 2155
frac16 2159
frac18 215B
frac23 2154
frac25 2156
frac34 00BE
frac35 2157
frac38 215C
frac45 2158
frac56 215A
frac58 215D
frac78 215E
frasl 2044
frown 2322
fscr 1D4BB
Fscr 2131
gacute 01F5
gamma 03B3
Gamma 0393
gammad 03DD
Gammad 03DC
gap 2A86
gbreve 011F
Gbreve 011E
Gcedil 0122
gcirc 011D
Gcirc 011C
gcy 0433
Gcy 0413
gdot 0121
Gdot 0120
ge 2265
gE 2267
gel 22DB
gEl 2A8C
geq 2265
geqq 2267
geqslant 2A7E
ges 2A7E
gescc 2AA9
gesdot 2A80
gesdoto 2A82
gesdotol 2A84
gesl 22DB  FE00
gesles 2A94
gfr 1D524
Gfr 1D50A
gg 226B
Gg 22D9
ggg 22D9
ggr 03B3
Ggr 0393
gimel 2137
gjcy 0453
GJcy 0403
gl 2277
gla 2AA5
glE 2A92
glj 2AA4
gnap 2A8A
gnapprox 2A8A
gne 2A88
gnE 2269
gneq 2A88
gneqq 2269
gnsim 22E7
gopf 1D558
Gopf 1D53E
grave 0060
GreaterEqual 2265
GreaterEqualLess 22DB
GreaterFullEqual 2267
GreaterGreater 2AA2
GreaterLess 2277
GreaterSlantEqual 2A7E
GreaterTilde 2273
gscr 210A
Gscr 1D4A2
gsim 2273
gsime 2A8E
gsiml 2A90
gt 003E
Gt 226B
GT 003E
gtcc 2AA7
gtcir 2A7A
gtdot 22D7
gtlPar 2995
gtquest 2A7C
gtrapprox 2A86
gtrarr 2978
gtrdot 22D7
gtreqless 22DB
gtreqqless 2A8C
gtrless 2277
gtrsim 2273
gvertneqq 2269  FE00
gvnE 2269  FE00
Hacek 02C7
hairsp 200A
half 00BD
hamilt 210B
hardcy 044A
HARDcy 042A
harr 2194
hArr 21D4
harrcir 2948
harrw 21AD
Hat 005E
hbar 210F
hcirc 0125
Hcirc 0124
hearts 2665
heartsuitUB hearts
hellip 2026
hercon 22B9
hfr 1D525
Hfr 210C
HilbertSpace 210B
hksearow 2925
hkswarow 2926
hoarr 21FF
homtht 223B
hookleftarrow 21A9
hookrightarrow 21AA
hopf 1D559
Hopf 210D
horbar 2015
HorizontalLine 2500
hscr 1D4BD
Hscr 210B
hslash 210F
hstrok 0127
Hstrok 0126
HumpDownHump 224E
HumpEqual 224F
hybull 2043
hyphen 2010
iacgr 03AF
Iacgr 038A
iacute 00ED
Iacute 00CD
ic 2063
icirc 00EE
Icirc 00CE
icy 0438
Icy 0418
idiagr 0390
idigr 03CA
Idigr 03AA
Idot 0130
iecy 0435
IEcy 0415
iexcl 00A1
iff 21D4
ifr 1D526
Ifr 2111
igr 03B9
Igr 0399
igrave 00EC
Igrave 00CC
ii 2148
iiiint 2A0C
iiint 222D
iinfin 29DC
iiota 2129
ijlig 0133
IJlig 0132
Im 2111
imacr 012B
Imacr 012A
image 2111
ImaginaryI 2148
imagline 2110
imagpart 2111
imath 0131
imof 22B7
imped 01B5
Implies 21D2
in 2208
incare 2105
infin 221E
infintie 29DD
inodot 0131
int 222B
Int 222C
intcal 22BA
integers 2124
Integral 222B
intercal 22BA
Intersection 22C2
intlarhk 2A17
intprod 2A3C
InvisibleComma 2063
InvisibleTimes 2062
iocy 0451
IOcy 0401
iogon 012F
Iogon 012E
iopf 1D55A
Iopf 1D540
iota 03B9
Iota 0399
iprod 2A3C
iquest 00BF
iscr 1D4BE
Iscr 2110
isin 2208
isindot 22F5
isinE 22F9
isins 22F4
isinsv 22F3
isinv 2208
it 2062
itilde 0129
Itilde 0128
iukcyUkrainian
IukcyUkrainian
iuml 00EF
Iuml 00CF
jcirc 0135
Jcirc 0134
jcy 0439
Jcy 0419
jfr 1D527
Jfr 1D50D
jmath 0237
jopf 1D55B
Jopf 1D541
jscr 1D4BF
Jscr 1D4A5
jsercy 0458
Jsercy 0408
jukcyUkrainian
JukcyUkrainian
kappa 03BA
Kappa 039A
kappav 03F0
kcedil 0137
Kcedil 0136
kcy 043A
Kcy 041A
kfr 1D528
Kfr 1D50E
kgr 03BA
Kgr 039A
kgreen 0138
khcy 0445
KHcy 0425
khgr 03C7
KHgr 03A7
kjcy 045C
KJcy 040C
kopf 1D55C
Kopf 1D542
kscr 1D4C0
Kscr 1D4A6
lAarr 21DA
lacute 013A
Lacute 0139
laemptyv 29B4
lagran 2112
lambda 03BB
Lambda 039B
lang 27E8
Lang 27EA
langd 2991
langle 27E8
lap 2A85
Laplacetrf 2112
laquo 00AB
larr 2190
lArr 21D0
Larr 219E
larrb 21E4
larrbfs 291F
larrfs 291D
larrhk 21A9
larrlp 21AB
larrpl 2939
larrsim 2973
larrtl 21A2
lat 2AAB
latail 2919
lAtail 291B
late 2AAD
lates 2AAD  FE00
lbarr 290C
lBarr 290E
lbbrk 2772
lbraceUM lcub
lbrackUM lsqb
lbrke 298B
lbrksld 298F
lbrkslu 298D
lcaron 013E
Lcaron 013D
lcedil 013C
Lcedil 013B
lceil 2308
lcub 007B
lcy 043B
Lcy 041B
ldca 2936
ldquo 201C
ldquor 201E
ldrdhar 2967
ldrushar 294B
ldsh 21B2
le 2264
lE 2266
LeftAngleBracket 27E8
leftarrowUM larr
Leftarrow 21D0
LeftArrowUM larr
LeftArrowBar 21E4
LeftArrowRightArrow 21C6
leftarrowtail 21A2
LeftCeiling 2308
LeftDoubleBracket 27E6
LeftDownTeeVector 2961
LeftDownVector 21C3
LeftDownVectorBar 2959
LeftFloor 230A
leftharpoondown 21BD
leftharpoonup 21BC
leftleftarrows 21C7
leftrightarrow 2194
Leftrightarrow 21D4
LeftRightArrow 2194
leftrightarrows 21C6
leftrightharpoons 21CB
leftrightsquigarrow 21AD
LeftRightVector 294E
LeftTee 22A3
LeftTeeArrow 21A4
LeftTeeVector 295A
leftthreetimes 22CB
LeftTriangle 22B2
LeftTriangleBar 29CF
LeftTriangleEqual 22B4
LeftUpDownVector 2951
LeftUpTeeVector 2960
LeftUpVector 21BF
LeftUpVectorBar 2958
LeftVector 21BC
LeftVectorBar 2952
leg 22DA
lEg 2A8B
leq 2264
leqq 2266
leqslant 2A7D
les 2A7D
lescc 2AA8
lesdot 2A7F
lesdoto 2A81
lesdotor 2A83
lesg 22DA  FE00
lesges 2A93
lessapprox 2A85
lessdot 22D6
lesseqgtr 22DA
lesseqqgtr 2A8B
LessEqualGreater 22DA
LessFullEqual 2266
LessGreater 2276
lessgtr 2276
LessLess 2AA1
lesssim 2272
LessSlantEqual 2A7D
LessTilde 2272
lfisht 297C
lfloor 230A
lfr 1D529
Lfr 1D50F
lg 2276
lgE 2A91
lgr 03BB
Lgr 039B
lHar 2962
lhard 21BD
lharu 21BC
lharul 296A
lhblk 2584
ljcy 0459
LJcy 0409
ll 226A
Ll 22D8
llarr 21C7
llcorner 231E
Lleftarrow 21DA
llhard 296B
lltri 25FA
lmidot 0140
Lmidot 013F
lmoust 23B0
lmoustache 23B0
lnap 2A89
lnapprox 2A89
lne 2A87
lnE 2268
lneq 2A87
lneqq 2268
lnsim 22E6
loang 27EC
loarr 21FD
lobrk 27E6
longleftarrow 27F5
Longleftarrow 27F8
LongLeftArrow 27F5
longleftrightarrow 27F7
Longleftrightarrow 27FA
LongLeftRightArrow 27F7
longmapsto 27FC
longrightarrow 27F6
Longrightarrow 27F9
LongRightArrow 27F6
looparrowleft 21AB
looparrowright 21AC
lopar 2985
lopf 1D55D
Lopf 1D543
loplus 2A2D
lotimes 2A34
lowast 2217
lowbar 005F
LowerLeftArrow 2199
LowerRightArrow 2198
loz 25CA
lozengeUB loz
lozf 29EB
lpar 0028
lparlt 2993
lrarr 21C6
lrcorner 231F
lrhar 21CB
lrhard 296D
lrm 200E
lrtri 22BF
lsaquo 2039
lscr 1D4C1
Lscr 2112
lsh 21B0
Lsh 21B0
lsim 2272
lsime 2A8D
lsimg 2A8F
lsqb 005B
lsquo 2018
lsquor 201A
lstrok 0142
Lstrok 0141
lt 003C
Lt 226A
LT 003C
ltcc 2AA6
ltcir 2A79
ltdot 22D6
lthree 22CB
ltimes 22C9
ltlarr 2976
ltquest 2A7B
ltri 25C3
ltrie 22B4
ltrif 25C2
ltrPar 2996
lurdshar 294A
luruhar 2966
lvertneqq 2268  FE00
lvnE 2268  FE00
macr 00AF
male 2642
malt 2720
malteseUB malt
map 21A6
Map 2905
mapsto 21A6
mapstodown 21A7
mapstoleft 21A4
mapstoup 21A5
marker 25AE
mcomma 2A29
mcy 043C
Mcy 041C
mdash 2014
mDDot 223A
measuredangle 2221
MediumSpace 205F
Mellintrf 2133
mfr 1D52A
Mfr 1D510
mgr 03BC
Mgr 039C
mho 2127
micro 00B5
mid 2223
midast 002A
midcir 2AF0
middot 00B7
minus 2212
minusb 229F
minusd 2238
minusdu 2A2A
MinusPlus 2213
mlcp 2ADB
mldr 2026
mnplus 2213
models 22A7
mopf 1D55E
Mopf 1D544
mp 2213
mscr 1D4C2
Mscr 2133
mstpos 223E
mu 03BC
Mu 039C
multimap 22B8
mumap 22B8
nabla 2207
nacute 0144
Nacute 0143
nang 2220  20D2
nap 2249
napE 2A70  0338
napid 224B  0338
napos 0149
napprox 2249
natur 266E
naturalUB natur
naturals 2115
nbsp 00A0
nbump 224E  0338
nbumpe 224F  0338
ncap 2A43
ncaron 0148
Ncaron 0147
ncedil 0146
Ncedil 0145
ncong 2247
ncongdot 2A6D  0338
ncup 2A42
ncy 043D
Ncy 041D
ndash 2013
ne 2260
nearhk 2924
nearr 2197
neArr 21D7
nearrow 2197
nedot 2250  0338
NegativeMediumSpace 200B
NegativeThickSpace 200B
NegativeThinSpace 200B
NegativeVeryThinSpace 200B
nequiv 2262
nesear 2928
nesim 2242  0338
NestedGreaterGreater 226B
NestedLessLess 226A
NewLine 000A
nexist 2204
nexists 2204
nfr 1D52B
Nfr 1D511
nge 2271
ngE 2267  0338
ngeq 2271
ngeqq 2267  0338
ngeqslant 2A7E  0338
nges 2A7E  0338
nGg 22D9  0338
ngr 03BD
Ngr 039D
ngsim 2275
ngt 226F
nGt 226B  20D2
ngtr 226F
nGtv 226B  0338
nharr 21AE
nhArr 21CE
nhpar 2AF2
ni 220B
nis 22FC
nisd 22FA
niv 220B
njcy 045A
NJcy 040A
nlarr 219A
nlArr 21CD
nldr 2025
nle 2270
nlE 2266  0338
nleftarrow 219A
nLeftarrow 21CD
nleftrightarrow 21AE
nLeftrightarrow 21CE
nleq 2270
nleqq 2266  0338
nleqslant 2A7D  0338
nles 2A7D  0338
nless 226E
nLl 22D8  0338
nlsim 2274
nlt 226E
nLt 226A  20D2
nltri 22EA
nltrie 22EC
nLtv 226A  0338
nmid 2224
NoBreak 2060
NonBreakingSpaceUM nbsp
nopf 1D55F
Nopf 2115
not 00AC
Not 2AEC
NotCongruent 2262
NotCupCap 226D
NotDoubleVerticalBar 2226
NotElement 2209
NotEqual 2260
NotEqualTilde 2242  0338
NotExists 2204
NotGreater 226F
NotGreaterEqual 2271
NotGreaterFullEqual 2267  0338
NotGreaterGreater 226B  0338
NotGreaterLess 2279
NotGreaterSlantEqual 2A7E  0338
NotGreaterTilde 2275
NotHumpDownHump 224E  0338
NotHumpEqual 224F  0338
notin 2209
notindot 22F5  0338
notinE 22F9  0338
notinva 2209
notinvb 22F7
notinvc 22F6
NotLeftTriangle 22EA
NotLeftTriangleBar 29CF  0338
NotLeftTriangleEqual 22EC
NotLess 226E
NotLessEqual 2270
NotLessGreater 2278
NotLessLess 226A  0338
NotLessSlantEqual 2A7D  0338
NotLessTilde 2274
NotNestedGreaterGreater 2AA2  0338
NotNestedLessLess 2AA1  0338
notni 220C
notniva 220C
notnivb 22FE
notnivc 22FD
NotPrecedes 2280
NotPrecedesEqual 2AAF  0338
NotPrecedesSlantEqual 22E0
NotReverseElement 220C
NotRightTriangle 22EB
NotRightTriangleBar 29D0  0338
NotRightTriangleEqual 22ED
NotSquareSubset 228F  0338
NotSquareSubsetEqual 22E2
NotSquareSuperset 2290  0338
NotSquareSupersetEqual 22E3
NotSubset 2282  20D2
NotSubsetEqual 2288
NotSucceeds 2281
NotSucceedsEqual 2AB0  0338
NotSucceedsSlantEqual 22E1
NotSucceedsTilde 227F  0338
NotSuperset 2283  20D2
NotSupersetEqual 2289
NotTilde 2241
NotTildeEqual 2244
NotTildeFullEqual 2247
NotTildeTilde 2249
NotVerticalBar 2224
npar 2226
nparallel 2226
nparsl 2AFD  20E5
npart 2202  0338
npolint 2A14
npr 2280
nprcue 22E0
npre 2AAF  0338
nprec 2280
npreceq 2AAF  0338
nrarr 219B
nrArr 21CF
nrarrc 2933  0338
nrarrw 219D  0338
nrightarrow 219B
nRightarrow 21CF
nrtri 22EB
nrtrie 22ED
nsc 2281
nsccue 22E1
nsce 2AB0  0338
nscr 1D4C3
Nscr 1D4A9
nshortmid 2224
nshortparallel 2226
nsim 2241
nsime 2244
nsimeq 2244
nsmid 2224
nspar 2226
nsqsube 22E2
nsqsupe 22E3
nsub 2284
nsube 2288
nsubE 2AC5  0338
nsubset 2282  20D2
nsubseteq 2288
nsubseteqq 2AC5  0338
nsucc 2281
nsucceq 2AB0  0338
nsup 2285
nsupe 2289
nsupE 2AC6  0338
nsupset 2283  20D2
nsupseteq 2289
nsupseteqq 2AC6  0338
ntgl 2279
ntilde 00F1
Ntilde 00D1
ntlg 2278
ntriangleleft 22EA
ntrianglelefteq 22EC
ntriangleright 22EB
ntrianglerighteq 22ED
nu 03BD
Nu 039D
num 0023
numero 2116
numsp 2007
nvap 224D  20D2
nvdash 22AC
nvDash 22AD
nVdash 22AE
nVDash 22AF
nvge 2265  20D2
nvgt 003E  20D2
nvHarr 2904
nvinfin 29DE
nvlArr 2902
nvle 2264  20D2
nvlt 003C  20D2
nvltrie 22B4  20D2
nvrArr 2903
nvrtrie 22B5  20D2
nvsim 223C  20D2
nwarhk 2923
nwarr 2196
nwArr 21D6
nwarrow 2196
nwnear 2927
oacgr 03CC
Oacgr 038C
oacute 00F3
Oacute 00D3
oast 229B
ocir 229A
ocirc 00F4
Ocirc 00D4
ocy 043E
Ocy 041E
odash 229D
odblac 0151
Odblac 0150
odiv 2A38
odot 2299
odsold 29BC
oelig 0153
OElig 0152
ofcir 29BF
ofr 1D52C
Ofr 1D512
ogon 02DB
ogr 03BF
Ogr 039F
ograve 00F2
Ograve 00D2
ogt 29C1
ohacgr 03CE
OHacgr 038F
ohbar 29B5
ohgr 03C9
OHgr 03A9
ohm 03A9
oint 222E
olarr 21BA
olcir 29BE
olcross 29BB
oline 203E
olt 29C0
omacr 014D
Omacr 014C
omega 03C9
Omega 03A9
omicron 03BF
Omicron 039F
omid 29B6
ominus 2296
oopf 1D560
Oopf 1D546
opar 29B7
OpenCurlyDoubleQuoteUM ldquo
OpenCurlyQuoteUM lsquo
operp 29B9
oplus 2295
or 2228
Or 2A54
orarr 21BB
ord 2A5D
order 2134
orderof 2134
ordf 00AA
ordm 00BA
origof 22B6
oror 2A56
orslope 2A57
orv 2A5B
oS 24C8
oscr 2134
Oscr 1D4AA
oslash 00F8
Oslash 00D8
osol 2298
otilde 00F5
Otilde 00D5
otimes 2297
Otimes 2A37
otimesas 2A36
ouml 00F6
Ouml 00D6
ovbar 233D
OverBar 203E
OverBrace 23DE
OverBracket 23B4
OverParenthesis 23DC
par 2225
para 00B6
parallel 2225
parsim 2AF3
parsl 2AFD
part 2202
PartialD 2202
pcy 043F
Pcy 041F
percnt 0025
period 002E
permil 2030
perp 22A5
pertenk 2031
pfr 1D52D
Pfr 1D513
pgr 03C0
Pgr 03A0
phgr 03C6
PHgr 03A6
phi 03C6
Phi 03A6
phiv 03D5
phmmat 2133
phone 260E
pi 03C0
Pi 03A0
pitchfork 22D4
piv 03D6
planck 210F
planckh 210E
plankv 210F
plus 002B
plusacir 2A23
plusb 229E
pluscir 2A22
plusdo 2214
plusdu 2A25
pluse 2A72
PlusMinusUM plusmn
plusmn 00B1
plussim 2A26
plustwo 2A27
pmUM plusmn
Poincareplane 210C
pointint 2A15
popf 1D561
Popf 2119
pound 00A3
pr 227A
Pr 2ABB
prap 2AB7
prcue 227C
pre 2AAF
prE 2AB3
prec 227A
precapprox 2AB7
preccurlyeq 227C
Precedes 227A
PrecedesEqual 2AAF
PrecedesSlantEqual 227C
PrecedesTilde 227E
preceq 2AAF
precnapprox 2AB9
precneqq 2AB5
precnsim 22E8
precsim 227E
prime 2032
Prime 2033
primes 2119
prnap 2AB9
prnE 2AB5
prnsim 22E8
prod 220F
Product 220F
profalar 232E
profline 2312
profsurf 2313
prop 221D
Proportion 2237
Proportional 221D
propto 221D
prsim 227E
prurel 22B0
pscr 1D4C5
Pscr 1D4AB
psgr 03C8
PSgr 03A8
psi 03C8
Psi 03A8
puncsp 2008
qfr 1D52E
Qfr 1D514
qint 2A0C
qopf 1D562
Qopf 211A
qprime 2057
qscr 1D4C6
Qscr 1D4AC
quaternions 210D
quatint 2A16
quest 003F
questeq 225F
quot 0022
QUOT 0022
rAarr 21DB
race 223D  0331
racute 0155
Racute 0154
radic 221A
raemptyv 29B3
rang 27E9
Rang 27EB
rangd 2992
range 29A5
rangle 27E9
raquo 00BB
rarr 2192
rArr 21D2
Rarr 21A0
rarrap 2975
rarrb 21E5
rarrbfs 2920
rarrc 2933
rarrfs 291E
rarrhk 21AA
rarrlp 21AC
rarrpl 2945
rarrsim 2974
rarrtl 21A3
Rarrtl 2916
rarrw 219D
ratail 291A
rAtail 291C
ratio 2236
rationals 211A
rbarr 290D
rBarr 290F
RBarr 2910
rbbrk 2773
rbraceUM rcub
rbrackUM rsqb
rbrke 298C
rbrksld 298E
rbrkslu 2990
rcaron 0159
Rcaron 0158
rcedil 0157
Rcedil 0156
rceil 2309
rcub 007D
rcy 0440
Rcy 0420
rdca 2937
rdldhar 2969
rdquo 201D
rdquor 201D
rdsh 21B3
Re 211C
real 211C
realine 211B
realpart 211C
reals 211D
rect 25AD
reg 00AE
REG 00AE
ReverseElement 220B
ReverseEquilibrium 21CB
ReverseUpEquilibrium 296F
rfisht 297D
rfloor 230B
rfr 1D52F
Rfr 211C
rgr 03C1
Rgr 03A1
rHar 2964
rhard 21C1
rharu 21C0
rharul 296C
rho 03C1
Rho 03A1
rhov 03F1
RightAngleBracket 27E9
rightarrowUM rarr
Rightarrow 21D2
RightArrowUM rarr
RightArrowBar 21E5
RightArrowLeftArrow 21C4
rightarrowtail 21A3
RightCeiling 2309
RightDoubleBracket 27E7
RightDownTeeVector 295D
RightDownVector 21C2
RightDownVectorBar 2955
RightFloor 230B
rightharpoondown 21C1
rightharpoonup 21C0
rightleftarrows 21C4
rightleftharpoons 21CC
rightrightarrows 21C9
rightsquigarrow 219D
RightTee 22A2
RightTeeArrow 21A6
RightTeeVector 295B
rightthreetimes 22CC
RightTriangle 22B3
RightTriangleBar 29D0
RightTriangleEqual 22B5
RightUpDownVector 294F
RightUpTeeVector 295C
RightUpVector 21BE
RightUpVectorBar 2954
RightVector 21C0
RightVectorBar 2953
ring 02DA
risingdotseq 2253
rlarr 21C4
rlhar 21CC
rlm 200F
rmoust 23B1
rmoustache 23B1
rnmid 2AEE
roang 27ED
roarr 21FE
robrk 27E7
ropar 2986
ropf 1D563
Ropf 211D
roplus 2A2E
rotimes 2A35
RoundImplies 2970
rpar 0029
rpargt 2994
rppolint 2A12
rrarr 21C9
Rrightarrow 21DB
rsaquo 203A
rscr 1D4C7
Rscr 211B
rsh 21B1
Rsh 21B1
rsqb 005D
rsquo 2019
rsquor 2019
rthree 22CC
rtimes 22CA
rtri 25B9
rtrie 22B5
rtrif 25B8
rtriltri 29CE
RuleDelayed 29F4
ruluhar 2968
rx 211E
sacute 015B
Sacute 015A
sbquo 201A
sc 227B
Sc 2ABC
scap 2AB8
scaron 0161
Scaron 0160
sccue 227D
sce 2AB0
scE 2AB4
scedil 015F
Scedil 015E
scirc 015D
Scirc 015C
scnap 2ABA
scnE 2AB6
scnsim 22E9
scpolint 2A13
scsim 227F
scy 0441
Scy 0421
sdot 22C5
sdotb 22A1
sdote 2A66
searhk 2925
searr 2198
seArr 21D8
searrow 2198
sect 00A7
semi 003B
seswar 2929
setminus 2216
setmn 2216
sext 2736
sfgr 03C2
sfr 1D530
Sfr 1D516
sfrown 2322
sgr 03C3
Sgr 03A3
sharp 266F
shchcy 0449
SHCHcy 0429
shcy 0448
SHcy 0428
ShortDownArrow 2193
ShortLeftArrow 2190
shortmid 2223
shortparallel 2225
ShortRightArrow 2192
ShortUpArrow 2191
shy 00AD
sigma 03C3
Sigma 03A3
sigmaf 03C2
sigmav 03C2
sim 223C
simdot 2A6A
sime 2243
simeq 2243
simg 2A9E
simgE 2AA0
siml 2A9D
simlE 2A9F
simne 2246
simplus 2A24
simrarr 2972
slarr 2190
SmallCircle 2218
smallsetminus 2216
smashp 2A33
smeparsl 29E4
smid 2223
smile 2323
smt 2AAA
smte 2AAC
smtes 2AAC  FE00
softcy 044C
SOFTcy 042C
sol 002F
solb 29C4
solbar 233F
sopf 1D564
Sopf 1D54A
spades 2660
spadesuitUB spades
spar 2225
sqcap 2293
sqcaps 2293  FE00
sqcup 2294
sqcups 2294  FE00
Sqrt 221A
sqsub 228F
sqsube 2291
sqsubset 228F
sqsubseteq 2291
sqsup 2290
sqsupe 2292
sqsupset 2290
sqsupseteq 2292
squ 25A1
square 25A1
Square 25A1
SquareIntersection 2293
SquareSubset 228F
SquareSubsetEqual 2291
SquareSuperset 2290
SquareSupersetEqual 2292
SquareUnion 2294
squarf 25AA
squf 25AA
srarr 2192
sscr 1D4C8
Sscr 1D4AE
ssetmn 2216
ssmile 2323
sstarf 22C6
star 2606
Star 22C6
starf 2605
straightepsilon 03F5
straightphi 03D5
strns 00AF
sub 2282
Sub 22D0
subdot 2ABD
sube 2286
subE 2AC5
subedot 2AC3
submult 2AC1
subne 228A
subnE 2ACB
subplus 2ABF
subrarr 2979
subset 2282
Subset 22D0
subseteq 2286
subseteqq 2AC5
SubsetEqual 2286
subsetneq 228A
subsetneqq 2ACB
subsim 2AC7
subsub 2AD5
subsup 2AD3
succ 227B
succapprox 2AB8
succcurlyeq 227D
Succeeds 227B
SucceedsEqual 2AB0
SucceedsSlantEqual 227D
SucceedsTilde 227F
succeq 2AB0
succnapprox 2ABA
succneqq 2AB6
succnsim 22E9
succsim 227F
SuchThat 220B
sum 2211
Sum 2211
sung 266A
sup 2283
Sup 22D1
sup1 00B9
sup2 00B2
sup3 00B3
supdot 2ABE
supdsub 2AD8
supe 2287
supE 2AC6
supedot 2AC4
Superset 2283
SupersetEqual 2287
suphsol 27C9
suphsub 2AD7
suplarr 297B
supmult 2AC2
supne 228B
supnE 2ACC
supplus 2AC0
supset 2283
Supset 22D1
supseteq 2287
supseteqq 2AC6
supsetneq 228B
supsetneqq 2ACC
supsim 2AC8
supsub 2AD4
supsup 2AD6
swarhk 2926
swarr 2199
swArr 21D9
swarrow 2199
swnwar 292A
szlig 00DF
Tab 0009
target 2316
tau 03C4
Tau 03A4
tbrk 23B4
tcaron 0165
Tcaron 0164
tcedil 0163
Tcedil 0162
tcy 0442
Tcy 0422
tdot 20DB
telrec 2315
tfr 1D531
Tfr 1D517
tgr 03C4
Tgr 03A4
there4 2234
therefore 2234
Therefore 2234
theta 03B8
Theta 0398
thetasym 03D1
thetav 03D1
thgr 03B8
THgr 0398
thickapprox 2248
thicksim 223C
ThickSpace 205F  200A
thinsp 2009
ThinSpaceUB thinsp
thkap 2248
thksim 223C
thorn 00FE
THORN 00DE
tilde 02DC
Tilde 223C
TildeEqual 2243
TildeFullEqual 2245
TildeTilde 2248
times 00D7
timesb 22A0
timesbar 2A31
timesd 2A30
tint 222D
toea 2928
top 22A4
topbot 2336
topcir 2AF1
topf 1D565
Topf 1D54B
topfork 2ADA
tosa 2929
tprime 2034
trade 2122
TRADE 2122
triangleUB utri
triangledownUB dtri
triangleleftUB ltri
trianglelefteq 22B4
triangleq 225C
trianglerightUB rtri
trianglerighteq 22B5
tridot 25EC
trie 225C
triminus 2A3A
TripleDot 20DB
triplus 2A39
trisb 29CD
tritime 2A3B
trpezium 23E2
tscr 1D4C9
Tscr 1D4AF
tscy 0446
TScy 0426
tshcy 045B
TSHcy 040B
tstrok 0167
Tstrok 0166
twixt 226C
twoheadleftarrow 219E
twoheadrightarrow 21A0
uacgr 03CD
UacgrUpsilon
uacute 00FA
UacuteU with acute
uarr 2191
uArr 21D1
Uarr 219F
Uarrocir 2949
ubrcy 045E
UbrcyU
ubreve 016D
UbreveU
ucirc 00FB
UcircU with circumflex
ucy 0443
UcyU
udarr 21C5
udblac 0171
UdblacU
udhar 296E
udiagr 03B0
udigr 03CB
UdigrUpsilon
ufisht 297E
ufr 1D532
UfrU
ugr 03C5
UgrUpsilon
ugrave 00F9
UgraveU with grave
uHar 2963
uharl 21BF
uharr 21BE
uhblk 2580
ulcorn 231C
ulcorner 231C
ulcrop 230F
ultri 25F8
umacr 016B
UmacrU
uml 00A8
UnderBar 005F
UnderBrace 23DF
UnderBracket 23B5
UnderParenthesis 23DD
Union 22C3
UnionPlus 228E
uogon 0173
UogonU
uopf 1D566
UopfU
uparrowUM uarr
Uparrow 21D1
UpArrowUM uarr
UpArrowBar 2912
UpArrowDownArrow 21C5
updownarrow 2195
Updownarrow 21D5
UpDownArrow 2195
UpEquilibrium 296E
upharpoonleft 21BF
upharpoonright 21BE
uplus 228E
UpperLeftArrow 2196
UpperRightArrow 2197
upsi 03C5
UpsiUpsilon capital Upsilon
upsih 03D2
upsilon 03C5
UpsilonUgr
UpTee 22A5
UpTeeArrow 21A5
upuparrows 21C8
urcorn 231D
urcorner 231D
urcrop 230E
uring 016F
UringU
urtri 25F9
uscr 1D4CA
UscrU
utdot 22F0
utilde 0169
UtildeU
utri 25B5
utrif 25B4
uuarr 21C8
uuml 00FC
UumlU with diaeresis
uwangle 29A7
vangrt 299C
varepsilon 03F5
varkappa 03F0
varnothing 2205
varphi 03D5
varpi 03D6
varpropto 221D
varr 2195
vArrUpdownarrow A: up&down dbl arrow
varrho 03F1
varsigma 03C2
varsubsetneq 228A  FE00
varsubsetneqq 2ACB  FE00
varsupsetneq 228B  FE00
varsupsetneqq 2ACC  FE00
vartheta 03D1
vartriangleleft 22B2
vartriangleright 22B3
vBar 2AE8
Vbar 2AEB
vBarv 2AE9
vcy 0432
Vcy 0412
vdash 22A2
vDash 22A8
Vdash 22A9
VDash 22AB
Vdashl 2AE6
vee 2228
Vee 22C1
veebar 22BB
veeeq 225A
vellip 22EE
verbar 007C
Verbar 2016
vertUM verbar
Vert 2016
VerticalBar 2223
VerticalLineUM verbar
VerticalSeparator 2758
VerticalTilde 2240
VeryThinSpaceUB hairsp
vfr 1D533
Vfr 1D519
vltri 22B2
vnsub 2282  20D2
vnsup 2283  20D2
vopf 1D567
Vopf 1D54D
vprop 221D
vrtri 22B3
vscr 1D4CB
Vscr 1D4B1
vsubne 228A  FE00
vsubnE 2ACB  FE00
vsupne 228B  FE00
vsupnE 2ACC  FE00
Vvdash 22AA
vzigzag 299A
wcirc 0175
Wcirc 0174
wedbar 2A5F
wedge 2227
Wedge 22C0
wedgeq 2259
weierp 2118
wfr 1D534
Wfr 1D51A
wopf 1D568
Wopf 1D54E
wp 2118
wr 2240
wreath 2240
wscr 1D4CC
Wscr 1D4B2
xcap 22C2
xcirc 25EF
xcup 22C3
xdtri 25BD
xfr 1D535
Xfr 1D51B
xgr 03BE
Xgr 039E
xharr 27F7
xhArr 27FA
xi 03BE
Xi 039E
xlarr 27F5
xlArr 27F8
xmap 27FC
xnis 22FB
xodot 2A00
xopf 1D569
Xopf 1D54F
xoplus 2A01
xotime 2A02
xrarr 27F6
xrArr 27F9
xscr 1D4CD
Xscr 1D4B3
xsqcup 2A06
xuplus 2A04
xutri 25B3
xvee 22C1
xwedge 22C0
yacute 00FD
Yacute 00DD
yacy 044F
YAcy 042F
ycirc 0177
Ycirc 0176
ycy 044B
YcyU
yen 00A5
yfr 1D536
Yfr 1D51C
yicyUkrainian
YIcyUkrainian
yopf 1D56A
Yopf 1D550
yscr 1D4CE
Yscr 1D4B4
yucy 044E
YUcyU
yuml 00FF
Yuml 0178
zacute 017A
Zacute 0179
zcaron 017E
Zcaron 017D
zcy 0437
Zcy 0417
zdot 017C
Zdot 017B
zeetrf 2128
ZeroWidthSpace 200B
zeta 03B6
Zeta 0396
zfr 1D537
Zfr 2128
zgr 03B6
Zgr 0396
zhcy 0436
ZHcy 0416
zigrarr 21DD
zopf 1D56B
Zopf 2124
zscr 1D4CF
Zscr 1D4B5
zwj 200D
zwnj 200C
-- A sample custom reader for Creole 1.0 (common wiki markup)
-- http://www.wikicreole.org/wiki/CheatSheet

-- For better performance we put these functions in local variables:
local P, S, R, Cf, Cc, Ct, V, Cs, Cg, Cb, B, C, Cmt =
  lpeg.P, lpeg.S, lpeg.R, lpeg.Cf, lpeg.Cc, lpeg.Ct, lpeg.V,
  lpeg.Cs, lpeg.Cg, lpeg.Cb, lpeg.B, lpeg.C, lpeg.Cmt

local whitespacechar = S(" \t\r\n")
local specialchar = S("/*~[]\\{}|")
local wordchar = (1 - (whitespacechar + specialchar))
local spacechar = S(" \t")
local newline = P"\r"^-1 * P"\n"
local blankline = spacechar^0 * newline
local endline = newline * #-blankline
local endequals = spacechar^0 * P"="^0 * spacechar^0 * newline
local cellsep = spacechar^0 * P"|"

local function trim(s)
   return (s:gsub("^%s*(.-)%s*$", "%1"))
end

local function ListItem(lev, ch)
  local start
  if ch == nil then
    start = S"*#"
  else
    start = P(ch)
  end
  local subitem = function(c)
    if lev < 6 then
      return ListItem(lev + 1, c)
    else
      return (1 - 1) -- fails
    end
  end
  local parser = spacechar^0
               * start^lev
               * #(- start)
               * spacechar^0
               * Ct((V"Inline" - (newline * spacechar^0 * S"*#"))^0)
               * newline
               * (Ct(subitem("*")^1) / pandoc.BulletList
                  +
                  Ct(subitem("#")^1) / pandoc.OrderedList
                  +
                  Cc(nil))
               / function (ils, sublist)
                   return { pandoc.Plain(ils), sublist }
                 end
  return parser
end

-- Grammar
G = P{ "Doc",
  Doc = Ct(V"Block"^0)
      / pandoc.Pandoc ;
  Block = blankline^0
        * ( V"Header"
          + V"HorizontalRule"
          + V"CodeBlock"
          + V"List"
          + V"Table"
          + V"Para") ;
  Para = Ct(V"Inline"^1)
       * newline
       / pandoc.Para ;
  HorizontalRule = spacechar^0
                 * P"----"
                 * spacechar^0
                 * newline
                 / pandoc.HorizontalRule;
  Header = (P("=")^1 / string.len)
         * spacechar^1
         * Ct((V"Inline" - endequals)^1)
         * endequals
         / pandoc.Header;
  CodeBlock = P"{{{"
            * blankline
            * C((1 - (newline * P"}}}"))^0)
            * newline
            * P"}}}"
            / pandoc.CodeBlock;
  Placeholder = P"<<<"
              * C(P(1) - P">>>")^0
              * P">>>"
              / function() return pandoc.Div({}) end;
  List = V"BulletList"
       + V"OrderedList" ;
  BulletList = Ct(ListItem(1,'*')^1)
             / pandoc.BulletList ;
  OrderedList = Ct(ListItem(1,'#')^1)
             / pandoc.OrderedList ;
  Table = (V"TableHeader" + Cc{})
        * Ct(V"TableRow"^1)
        / function(headrow, bodyrows)
            local numcolumns = #(bodyrows[1])
            local aligns = {}
            local widths = {}
            for i = 1,numcolumns do
              aligns[i] = pandoc.AlignDefault
              widths[i] = 0
            end
            return pandoc.utils.from_simple_table(
              pandoc.SimpleTable({}, aligns, widths, headrow, bodyrows))
          end ;
  TableHeader = Ct(V"HeaderCell"^1)
              * cellsep^-1
              * spacechar^0
              * newline ;
  TableRow   = Ct(V"BodyCell"^1)
             * cellsep^-1
             * spacechar^0
             * newline ;
  HeaderCell = cellsep
             * P"="
             * spacechar^0
             * Ct((V"Inline" - (newline + cellsep))^0)
             / function(ils) return { pandoc.Plain(ils) } end ;
  BodyCell   = cellsep
             * spacechar^0
             * Ct((V"Inline" - (newline + cellsep))^0)
             / function(ils) return { pandoc.Plain(ils) } end ;
  Inline = V"Emph"
         + V"Strong"
         + V"LineBreak"
         + V"Link"
         + V"URL"
         + V"Image"
         + V"Str"
         + V"Space"
         + V"SoftBreak"
         + V"Escaped"
         + V"Placeholder"
         + V"Code"
         + V"Special" ;
  Str = wordchar^1
      / pandoc.Str;
  Escaped = P"~"
          * C(P(1))
          / pandoc.Str ;
  Special = specialchar
          / pandoc.Str;
  Space = spacechar^1
        / pandoc.Space ;
  SoftBreak = endline
            * # -(V"HorizontalRule" + V"CodeBlock")
            / pandoc.SoftBreak ;
  LineBreak = P"\\\\"
            / pandoc.LineBreak ;
  Code = P"{{{"
       * C((1 - P"}}}")^0)
       * P"}}}"
       / trim / pandoc.Code ;
  Link = P"[["
       * C((1 - (P"]]" + P"|"))^0)
       * (P"|" * Ct((V"Inline" - P"]]")^1))^-1 * P"]]"
       / function(url, desc)
           local txt = desc or {pandoc.Str(url)}
           return pandoc.Link(txt, url)
         end ;
  Image = P"{{"
        * #-P"{"
        * C((1 - (S"}"))^0)
        * (P"|" * Ct((V"Inline" - P"}}")^1))^-1
        * P"}}"
        / function(url, desc)
            local txt = desc or ""
            return pandoc.Image(txt, url)
          end ;
  URL = P"http"
      * P"s"^-1
      * P":"
      * (1 - (whitespacechar + (S",.?!:;\"'" * #whitespacechar)))^1
      / function(url)
          return pandoc.Link(pandoc.Str(url), url)
        end ;
  Emph = P"//"
       * Ct((V"Inline" - P"//")^1)
       * P"//"
       / pandoc.Emph ;
  Strong = P"**"
         * Ct((V"Inline" -P"**")^1)
         * P"**"
         / pandoc.Strong ;
}

function Reader(input, reader_options)
  return lpeg.match(G, tostring(input))
end
