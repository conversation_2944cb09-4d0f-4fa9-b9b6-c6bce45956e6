<!DOCTYPE html>
<html>
<head>
    <title>Worker Test</title>
</head>
<body>
    <h1>Worker Test</h1>
    <button onclick="testWorker()">Test Worker</button>
    <div id="output"></div>
    
    <script>
        function testWorker() {
            const output = document.getElementById('output');
            output.innerHTML = 'Testing worker...<br>';
            
            try {
                const worker = new Worker('/workers/test.worker.js');
                
                worker.onmessage = function(e) {
                    output.innerHTML += 'Message from worker: ' + JSON.stringify(e.data) + '<br>';
                };
                
                worker.onerror = function(error) {
                    output.innerHTML += 'Worker error: ' + error + '<br>';
                    console.error('Worker error:', error);
                };
                
                worker.postMessage({ test: 'hello' });
                output.innerHTML += 'Message sent to worker<br>';
            } catch (error) {
                output.innerHTML += 'Failed to create worker: ' + error + '<br>';
                console.error('Failed to create worker:', error);
            }
        }
        
        // Auto test on load
        window.onload = testWorker;
    </script>
</body>
</html>