{"name": "fork-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:turbo": "next build --turbopack", "start": "next start", "lint": "eslint", "test:pwa": "node scripts/test-pwa.js", "pwa:build": "pnpm build && pnpm start", "create-tool": "node scripts/create-tool.js", "download-wasm": "node scripts/download-wasm.js", "postinstall": "node scripts/download-wasm.js"}, "dependencies": {"clsx": "^2.1.1", "lucide-react": "^0.540.0", "next": "15.5.0", "next-pwa": "^5.6.0", "pandoc-wasm": "^0.0.2", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "workbox-webpack-plugin": "^7.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.0", "tailwindcss": "^4", "typescript": "^5"}}