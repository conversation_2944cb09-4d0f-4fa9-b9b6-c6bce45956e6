# 更新日志

本文件记录了 ToolFork 项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增
- 初始化项目结构
- 基于 Next.js 15 + React 19 的现代化技术栈
- 工具分类系统（文本、文档、图像、音频、视频、开发、实用工具、设计、加密、转换）
- 响应式用户界面设计
- 工具搜索和筛选功能
- 工具收藏功能
- 完整的项目文档

### 计划中
- WebAssembly 集成支持
- 第一批工具实现
- 用户系统
- 工具使用统计
- 多语言支持

## [0.1.0] - 2025-01-XX

### 新增
- 项目初始化
- 基础架构搭建
- 核心组件开发
- 文档编写

---

## 版本说明

- **主版本号**：当你做了不兼容的 API 修改
- **次版本号**：当你做了向下兼容的功能性新增
- **修订号**：当你做了向下兼容的问题修正

## 变更类型

- `新增` - 新功能
- `变更` - 对现有功能的变更
- `弃用` - 即将移除的功能
- `移除` - 已移除的功能
- `修复` - 问题修复
- `安全` - 安全相关的修复
