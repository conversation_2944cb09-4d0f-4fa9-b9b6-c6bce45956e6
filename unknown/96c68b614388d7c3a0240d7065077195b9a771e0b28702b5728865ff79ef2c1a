import { MCPServer, MCPClient, ServerCategory, ClientPlatform, FAQItem } from '@/types/mcp'

// 模拟 MCP Servers 数据
export const mockServers: MCPServer[] = [
  {
    id: 'edgeone-pages-mcp',
    name: 'EdgeOne Pages MCP',
    description: 'An MCP service designed for deploying HTML content to EdgeOne Pages and obtaining an accessible public URL.',
    author: 'TencentEdgeOne',
    imageUrl: '/api/placeholder/48/48',
    category: ServerCategory.TOOLS,
    tags: ['deployment', 'cdn', 'pages'],
    featured: true,
    sponsored: false,
    verified: true,
    official: false,
    hosted: false,
    createdAt: '2024-01-15',
    updatedAt: '2024-01-20',
    stars: 245,
    downloads: 1200
  },
  {
    id: 'siliconflow',
    name: 'SiliconFlow',
    description: 'AI Infrastructure for LLMs & Multimodal Models',
    author: 'SiliconFlow',
    imageUrl: '/api/placeholder/48/48',
    category: ServerCategory.AI,
    tags: ['ai', 'llm', 'infrastructure'],
    featured: true,
    sponsored: true,
    verified: true,
    official: false,
    hosted: true,
    createdAt: '2024-01-10',
    updatedAt: '2024-01-22',
    stars: 890,
    downloads: 5600
  },
  {
    id: 'time',
    name: 'Time',
    description: 'A Model Context Protocol server that provides time and timezone conversion capabilities. This server enables LLMs to get current time information and perform timezone conversions using IANA timezone names, with automatic system timezone detection.',
    author: 'modelcontextprotocol',
    avatar: '🕐',
    category: ServerCategory.UTILITY,
    tags: ['time', 'timezone', 'utility'],
    featured: true,
    sponsored: false,
    verified: true,
    official: true,
    hosted: false,
    createdAt: '2023-12-01',
    updatedAt: '2024-01-18',
    stars: 567,
    downloads: 3400
  },
  {
    id: 'aiimagemultistyle',
    name: 'Aiimagemultistyle',
    description: 'A Model Context Protocol (MCP) server for image generation and manipulation using fal.ai\'s Stable Diffusion model.',
    author: 'codecraftm',
    avatar: 'A',
    category: ServerCategory.AI,
    tags: ['image', 'ai', 'generation'],
    featured: true,
    sponsored: false,
    verified: false,
    official: false,
    hosted: false,
    createdAt: '2024-01-05',
    updatedAt: '2024-01-19',
    stars: 234,
    downloads: 890
  },
  {
    id: 'zhipu-web-search',
    name: 'Zhipu Web Search',
    description: 'Zhipu Web Search MCP Server is a search engine specifically designed for large models. It integrates four search engines, allowing users to flexibly compare and switch between them.',
    author: 'BigModel',
    imageUrl: '/api/placeholder/48/48',
    category: ServerCategory.SEARCH,
    tags: ['search', 'web', 'ai'],
    featured: true,
    sponsored: false,
    verified: true,
    official: false,
    hosted: false,
    createdAt: '2024-01-12',
    updatedAt: '2024-01-21',
    stars: 456,
    downloads: 2300
  },
  {
    id: 'mcp-advisor',
    name: 'MCP Advisor',
    description: 'MCP Advisor & Installation - Use the right MCP server for your needs',
    author: 'istarwyh',
    imageUrl: '/api/placeholder/48/48',
    category: ServerCategory.TOOLS,
    tags: ['advisor', 'installation', 'helper'],
    featured: true,
    sponsored: false,
    verified: false,
    official: false,
    hosted: false,
    createdAt: '2024-01-08',
    updatedAt: '2024-01-20',
    stars: 123,
    downloads: 560
  },
]

// 模拟 MCP Clients 数据
export const mockClients: MCPClient[] = [
  {
    id: 'hyperchat',
    name: 'HyperChat',
    description: 'HyperChat is a Chat client that strives for openness, utilizing APIs from various LLMs to achieve the best Chat experience, as well as implementing productivity tools through the MCP protocol.',
    author: 'BigSweetPotatoStudio',
    imageUrl: '/api/placeholder/48/48',
    platform: [ClientPlatform.WINDOWS, ClientPlatform.MACOS, ClientPlatform.LINUX],
    tags: ['chat', 'llm', 'productivity'],
    featured: true,
    sponsored: false,
    verified: true,
    official: false,
    createdAt: '2024-01-14',
    updatedAt: '2024-01-22',
    stars: 678,
    downloads: 4500
  },
  {
    id: 'y-gui',
    name: 'Y Gui',
    description: 'A web-based graphical interface for AI chat interactions with support for multiple AI models and MCP (Model Context Protocol) servers.',
    author: 'luohy15',
    avatar: 'Y',
    platform: [ClientPlatform.WEB],
    tags: ['web', 'chat', 'gui'],
    featured: true,
    sponsored: false,
    verified: false,
    official: false,
    createdAt: '2024-01-11',
    updatedAt: '2024-01-19',
    stars: 234,
    downloads: 1200
  },
  {
    id: 'deepchat',
    name: 'DeepChat',
    description: 'Your AI Partner on Desktop',
    author: 'ThinkInAI',
    imageUrl: '/api/placeholder/48/48',
    platform: [ClientPlatform.WINDOWS, ClientPlatform.MACOS],
    tags: ['desktop', 'ai', 'chat'],
    featured: true,
    sponsored: false,
    verified: true,
    official: false,
    createdAt: '2024-01-09',
    updatedAt: '2024-01-20',
    stars: 890,
    downloads: 6700
  },
  {
    id: 'cherry-studio',
    name: 'Cherry Studio',
    description: '🍒 Cherry Studio is a desktop client that supports for multiple LLM providers.',
    author: 'CherryHQ',
    imageUrl: '/api/placeholder/48/48',
    platform: [ClientPlatform.WINDOWS, ClientPlatform.MACOS, ClientPlatform.LINUX],
    tags: ['desktop', 'llm', 'studio'],
    featured: true,
    sponsored: false,
    verified: true,
    official: false,
    createdAt: '2024-01-07',
    updatedAt: '2024-01-21',
    stars: 567,
    downloads: 3400
  },
  {
    id: 'vscode',
    name: 'Visual Studio Code - Open Source',
    description: 'Visual Studio Code',
    author: 'microsoft',
    imageUrl: '/api/placeholder/48/48',
    platform: [ClientPlatform.VSCODE],
    tags: ['ide', 'editor', 'development'],
    featured: true,
    sponsored: false,
    verified: true,
    official: true,
    createdAt: '2023-11-01',
    updatedAt: '2024-01-22',
    stars: 12000,
    downloads: 89000
  },
  {
    id: 'cline',
    name: 'Cline – #1 on OpenRouter',
    description: 'Autonomous coding agent right in your IDE, capable of creating/editing files, executing commands, using the browser, and more with your permission every step of the way.',
    author: 'cline',
    imageUrl: '/api/placeholder/48/48',
    platform: [ClientPlatform.VSCODE],
    tags: ['coding', 'agent', 'automation'],
    featured: true,
    sponsored: false,
    verified: true,
    official: false,
    createdAt: '2024-01-06',
    updatedAt: '2024-01-19',
    stars: 2345,
    downloads: 12000
  },
]

// FAQ 数据
export const faqItems: FAQItem[] = [
  {
    id: 1,
    question: 'What is MCP (Model Context Protocol)?',
    answer: 'MCP is an open-source protocol developed by Anthropic that enables AI systems like Claude to securely connect with various data sources. It provides a universal standard for AI assistants to access external data, tools, and prompts through a client-server architecture.'
  },
  {
    id: 2,
    question: 'What is MCP Server?',
    answer: 'MCP Server is a system that provides context, tools, and prompts to AI clients. It can expose data sources like files, documents, databases, and API integrations, allowing AI assistants to access real-time information in a secure way.'
  },
  {
    id: 3,
    question: 'How do MCP Server work?',
    answer: 'MCP Server work through a simple client-server architecture. They expose data and tools through a standardized protocol, maintaining secure 1:1 connections with clients inside host applications like Claude Desktop.'
  },
  {
    id: 4,
    question: 'What can MCP Server provide?',
    answer: 'MCP Server can share resources (files, docs, data), expose tools (API integrations, actions), and provide prompts (templated interactions). They control their own resources and maintain clear system boundaries for security.'
  },
  {
    id: 5,
    question: 'How does Claude use MCP?',
    answer: 'Claude can connect to MCP server to access external data sources and tools, enhancing its capabilities with real-time information. Currently, this works with local MCP servers, with enterprise remote server support coming soon.'
  },
  {
    id: 6,
    question: 'Is MCP Server secure?',
    answer: 'Yes, security is built into the MCP protocol. Server controls its own resources, there\'s no need to share API keys with LLM providers, and the system maintains clear boundaries. Each server manages its own authentication and access control.'
  },
  {
    id: 7,
    question: 'What is mcp.so?',
    answer: 'mcp.so is a community-driven platform that collects and organizes third-party MCP Servers. It serves as a central directory where users can discover, share, and learn about various MCP Servers available for AI applications.'
  },
  {
    id: 8,
    question: 'How can I submit my MCP Server to mcp.so?',
    answer: 'You can submit your MCP Server by creating a new issue in our GitHub repository. Click the \'Submit\' button in the navigation bar or visit our GitHub issues page directly. Please provide details about your server including its name, description, features, and connection information.'
  }
]